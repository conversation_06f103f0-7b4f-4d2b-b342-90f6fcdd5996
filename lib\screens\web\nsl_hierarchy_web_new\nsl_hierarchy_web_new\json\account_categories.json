{"status": 200, "message": "success", "result": [{"id": "CREDIT_0", "name": "Accounts Payable/Accured Expenses", "category_type": "Credit"}, {"id": "DEBIT_24463", "name": "Accessibility Testing Expense", "category_type": "Debit"}, {"id": "DEBIT_16741", "name": "Access Management", "category_type": "Debit"}, {"id": "DEBIT_12273", "name": "Accounting Expense", "category_type": "Debit"}, {"id": "DEBIT_11741", "name": "Accounting Operations", "category_type": "Debit"}, {"id": "DEBIT_15899", "name": "Administrative Expenses", "category_type": "Debit"}, {"id": "DEBIT_179", "name": "Analysis expenses", "category_type": "Debit"}, {"id": "DEBIT_13248", "name": "Analysis Expenses", "category_type": "Debit"}, {"id": "DEBIT_20949", "name": "API Development Cost", "category_type": "Debit"}, {"id": "DEBIT_17904", "name": "Asset Maintenance and Operations", "category_type": "Debit"}, {"id": "DEBIT_22214", "name": "Automation Testing Expenses", "category_type": "Debit"}, {"id": "DEBIT_573", "name": "Background Check and Screening Costs", "category_type": "Debit"}, {"id": "DEBIT_13398", "name": "Bank Fees", "category_type": "Debit"}, {"id": "DEBIT_8846", "name": "Bank Management", "category_type": "Debit"}, {"id": "DEBIT_9361", "name": "Bank Reconciliation", "category_type": "Debit"}, {"id": "DEBIT_9269", "name": "Bank Statements", "category_type": "Debit"}, {"id": "DEBIT_22364", "name": "Bug Management Expenses", "category_type": "Debit"}, {"id": "DEBIT_25705", "name": "Business Development Cost", "category_type": "Debit"}, {"id": "DEBIT_2580", "name": "Business Intelligence - <PERSON><PERSON>ses", "category_type": "Debit"}, {"id": "DEBIT_2709", "name": "Business Intelligence - Analysis Expenses", "category_type": "Debit"}, {"id": "DEBIT_2613", "name": "Business Intelligence - Approval Process", "category_type": "Debit"}, {"id": "DEBIT_2592", "name": "Business Intelligence - Dashboard Design", "category_type": "Debit"}, {"id": "DEBIT_2691", "name": "Business Intelligence - Dashboard Usage", "category_type": "Debit"}, {"id": "DEBIT_2779", "name": "Business Intelligence - Data Quality Management", "category_type": "Debit"}, {"id": "DEBIT_2992", "name": "Business Intelligence - Documentation", "category_type": "Debit"}, {"id": "DEBIT_2688", "name": "Business Intelligence - Executive Access", "category_type": "Debit"}, {"id": "DEBIT_2732", "name": "Business Intelligence - KPI Management", "category_type": "Debit"}, {"id": "DEBIT_2959", "name": "Business Intelligence - Performance Optimization", "category_type": "Debit"}, {"id": "DEBIT_2817", "name": "Business Intelligence - Quality Resolution", "category_type": "Debit"}, {"id": "DEBIT_2832", "name": "Business Intelligence - Report Building", "category_type": "Debit"}, {"id": "DEBIT_2640", "name": "Business Intelligence - Report Scheduling", "category_type": "Debit"}, {"id": "DEBIT_2616", "name": "Business Intelligence - Review Process", "category_type": "Debit"}, {"id": "DEBIT_2891", "name": "Business Intelligence - User Management", "category_type": "Debit"}, {"id": "DEBIT_9567", "name": "Cheque Processing", "category_type": "Debit"}, {"id": "DEBIT_22044", "name": "CI/CD Operations Cost", "category_type": "Debit"}, {"id": "DEBIT_24341", "name": "Conversion Testing Cost", "category_type": "Debit"}, {"id": "DEBIT_9665", "name": "Credit Card Processing", "category_type": "Debit"}, {"id": "DEBIT_19494", "name": "Customer Contract Management", "category_type": "Debit"}, {"id": "DEBIT_19138", "name": "Customer Lifecycle Management", "category_type": "Debit"}, {"id": "DEBIT_9143", "name": "Customer Payments", "category_type": "Debit"}, {"id": "DEBIT_20643", "name": "Customer Research Cost", "category_type": "Debit"}, {"id": "DEBIT_5564", "name": "Customer Setup Expense", "category_type": "Debit"}, {"id": "DEBIT_5772", "name": "Customer Success Implementation", "category_type": "Debit"}, {"id": "DEBIT_20625", "name": "Customer Support Salaries", "category_type": "Debit"}, {"id": "DEBIT_8030", "name": "Customer Support Services", "category_type": "Debit"}, {"id": "DEBIT_8439", "name": "Cybersecurity", "category_type": "Debit"}, {"id": "DEBIT_3031", "name": "Dashboard Management - <PERSON><PERSON>ses", "category_type": "Debit"}, {"id": "DEBIT_3234", "name": "Dashboard Management - Analysis Expenses", "category_type": "Debit"}, {"id": "DEBIT_3057", "name": "Dashboard Management - Approval Process", "category_type": "Debit"}, {"id": "DEBIT_3289", "name": "Dashboard Management - Archive Management", "category_type": "Debit"}, {"id": "DEBIT_3349", "name": "Dashboard Management - Archive Retrieval", "category_type": "Debit"}, {"id": "DEBIT_3002", "name": "Dashboard Management - Design Process", "category_type": "Debit"}, {"id": "DEBIT_3279", "name": "Dashboard Management - Optimization Planning", "category_type": "Debit"}, {"id": "DEBIT_3143", "name": "Dashboard Management - Personalization", "category_type": "Debit"}, {"id": "DEBIT_3127", "name": "Dashboard Management - Publishing", "category_type": "Debit"}, {"id": "DEBIT_3076", "name": "Dashboard Management - Real-Time Processing", "category_type": "Debit"}, {"id": "DEBIT_3061", "name": "Dashboard Management - Review Process", "category_type": "Debit"}, {"id": "DEBIT_3186", "name": "Dashboard Management - Sharing Process", "category_type": "Debit"}, {"id": "DEBIT_3136", "name": "Dashboard Management - User Access", "category_type": "Debit"}, {"id": "DEBIT_22793", "name": "Database Admin Cost", "category_type": "Debit"}, {"id": "DEBIT_21873", "name": "Database Management Expenses", "category_type": "Debit"}, {"id": "DEBIT_3495", "name": "Data Warehouse - Admin Expenses", "category_type": "Debit"}, {"id": "DEBIT_3490", "name": "Data Warehouse - Analysis Expenses", "category_type": "Debit"}, {"id": "DEBIT_3554", "name": "Data Warehouse - Approval Process", "category_type": "Debit"}, {"id": "DEBIT_3817", "name": "Data Warehouse - Archive Management", "category_type": "Debit"}, {"id": "DEBIT_3804", "name": "Data Warehouse - Archive Planning", "category_type": "Debit"}, {"id": "DEBIT_3548", "name": "Data Warehouse - Documentation", "category_type": "Debit"}, {"id": "DEBIT_3563", "name": "Data Warehouse - ETL Development", "category_type": "Debit"}, {"id": "DEBIT_3909", "name": "Data Warehouse - Implementation Process", "category_type": "Debit"}, {"id": "DEBIT_3501", "name": "Data Warehouse - Model Design", "category_type": "Debit"}, {"id": "DEBIT_3678", "name": "Data Warehouse - Operations Management", "category_type": "Debit"}, {"id": "DEBIT_3884", "name": "Data Warehouse - Performance Optimization", "category_type": "Debit"}, {"id": "DEBIT_3913", "name": "Data Warehouse - Review Process", "category_type": "Debit"}, {"id": "DEBIT_3755", "name": "Data Warehouse - Schedule Management", "category_type": "Debit"}, {"id": "DEBIT_3631", "name": "Data Warehouse - Testing Process", "category_type": "Debit"}, {"id": "DEBIT_13165", "name": "Deferred Revenue and Revenue Recognition", "category_type": "Debit"}, {"id": "DEBIT_18224", "name": "Depreciation Expense", "category_type": "Debit"}, {"id": "DEBIT_23528", "name": "Design Operations Cost", "category_type": "Debit"}, {"id": "DEBIT_24117", "name": "Design Review Expense", "category_type": "Debit"}, {"id": "DEBIT_19721", "name": "Developer Salaries", "category_type": "Debit"}, {"id": "DEBIT_20928", "name": "Development Software Licenses", "category_type": "Debit"}, {"id": "DEBIT_19833", "name": "DevOps and Infrastructure", "category_type": "Debit"}, {"id": "DEBIT_9774", "name": "Direct Debit", "category_type": "Debit"}, {"id": "DEBIT_2194", "name": "Documentation and Process Development", "category_type": "Debit"}, {"id": "DEBIT_36", "name": "Documentation Expenses", "category_type": "Debit"}, {"id": "DEBIT_9410", "name": "Electronic Funds", "category_type": "Debit"}, {"id": "DEBIT_1277", "name": "Employee Onboarding Administrative Costs", "category_type": "Debit"}, {"id": "DEBIT_1376", "name": "Employee Training and Development", "category_type": "Debit"}, {"id": "DEBIT_11260", "name": "ESG Environmental Operations", "category_type": "Debit"}, {"id": "DEBIT_16850", "name": "Event Management", "category_type": "Debit"}, {"id": "DEBIT_17333", "name": "Facilities Maintenance Operations", "category_type": "Debit"}, {"id": "DEBIT_11550", "name": "Funding Expense", "category_type": "Debit"}, {"id": "DEBIT_41", "name": "General and Administrative Expenses", "category_type": "Debit"}, {"id": "DEBIT_1527", "name": "Human Resources Expenses", "category_type": "Debit"}, {"id": "DEBIT_16553", "name": "Income Tax Expense", "category_type": "Debit"}, {"id": "DEBIT_11490", "name": "Initial Funding", "category_type": "Debit"}, {"id": "DEBIT_25577", "name": "Innovation Lab Expense", "category_type": "Debit"}, {"id": "DEBIT_26441", "name": "Innovation Management Cost", "category_type": "Debit"}, {"id": "DEBIT_24725", "name": "Innovation Operations Expense", "category_type": "Debit"}, {"id": "DEBIT_10243", "name": "Insurance", "category_type": "Debit"}, {"id": "DEBIT_10425", "name": "Intellectual Property", "category_type": "Debit"}, {"id": "DEBIT_13284", "name": "Intercompany Accounting", "category_type": "Debit"}, {"id": "DEBIT_12971", "name": "Internal Audit", "category_type": "Debit"}, {"id": "DEBIT_12990", "name": "Internal Audit Expense", "category_type": "Debit"}, {"id": "DEBIT_6620", "name": "Legal and Accounting", "category_type": "Debit"}, {"id": "DEBIT_25937", "name": "Market Intelligence Expense", "category_type": "Debit"}, {"id": "DEBIT_25168", "name": "Market Research Expense", "category_type": "Debit"}, {"id": "DEBIT_23387", "name": "Mobile Testing Expense", "category_type": "Debit"}, {"id": "DEBIT_21589", "name": "Multi-Platform Development Expense", "category_type": "Debit"}, {"id": "DEBIT_1330", "name": "New Hire Equipment and Setup", "category_type": "Debit"}, {"id": "DEBIT_17275", "name": "Office Rent", "category_type": "Debit"}, {"id": "DEBIT_4592", "name": "Office Supplies", "category_type": "Debit"}, {"id": "DEBIT_9487", "name": "Payment Analytics", "category_type": "Debit"}, {"id": "DEBIT_8951", "name": "Payment Management", "category_type": "Debit"}, {"id": "DEBIT_9896", "name": "Payment Operations", "category_type": "Debit"}, {"id": "DEBIT_16100", "name": "Payroll expense", "category_type": "Debit"}, {"id": "DEBIT_12805", "name": "Payroll Expense", "category_type": "Debit"}, {"id": "DEBIT_15924", "name": "Planning expense", "category_type": "Debit"}, {"id": "DEBIT_7559", "name": "PO Processing Expense", "category_type": "Debit"}, {"id": "DEBIT_7430", "name": "Procurement Operations", "category_type": "Debit"}, {"id": "DEBIT_20773", "name": "Product Analytics Expense", "category_type": "Debit"}, {"id": "DEBIT_21739", "name": "Product Designing Expenses", "category_type": "Debit"}, {"id": "DEBIT_20053", "name": "Product Development", "category_type": "Debit"}, {"id": "DEBIT_21062", "name": "Product Documentation Expense", "category_type": "Debit"}, {"id": "DEBIT_20427", "name": "Product Launch Expense", "category_type": "Debit"}, {"id": "DEBIT_20237", "name": "Product Management Cost", "category_type": "Debit"}, {"id": "DEBIT_21415", "name": "Product Monitoring Cost", "category_type": "Debit"}, {"id": "DEBIT_21341", "name": "Product Requirements Expense", "category_type": "Debit"}, {"id": "DEBIT_20078", "name": "Product Strategy Expense", "category_type": "Debit"}, {"id": "DEBIT_6626", "name": "Professional Fees", "category_type": "Debit"}, {"id": "DEBIT_8118", "name": "Property", "category_type": "Debit"}, {"id": "DEBIT_22723", "name": "QA Testing Expense", "category_type": "Debit"}, {"id": "DEBIT_26113", "name": "R&D Investment Cost", "category_type": "Debit"}, {"id": "DEBIT_330", "name": "Recruitment and Hiring Costs", "category_type": "Debit"}, {"id": "DEBIT_22985", "name": "Release Operations Cost", "category_type": "Debit"}, {"id": "DEBIT_21172", "name": "Release Planning Cost", "category_type": "Debit"}, {"id": "DEBIT_11971", "name": "Reporting expense", "category_type": "Debit"}, {"id": "DEBIT_3416", "name": "Report Scheduling - <PERSON><PERSON>penses", "category_type": "Debit"}, {"id": "DEBIT_3406", "name": "Report Scheduling - Catalog Access", "category_type": "Debit"}, {"id": "DEBIT_3473", "name": "Report Scheduling - Schedule Activation", "category_type": "Debit"}, {"id": "DEBIT_3410", "name": "Report Scheduling - Schedule Setup", "category_type": "Debit"}, {"id": "DEBIT_5821", "name": "Sales and Marketing Expenses", "category_type": "Debit"}, {"id": "DEBIT_17053", "name": "Security Access", "category_type": "Debit"}, {"id": "DEBIT_5752", "name": "Software and Tools", "category_type": "Debit"}, {"id": "DEBIT_22506", "name": "Software Engineering Expense", "category_type": "Debit"}, {"id": "DEBIT_22824", "name": "Software Quality Expense", "category_type": "Debit"}, {"id": "DEBIT_26268", "name": "Strategic Analytics Expense", "category_type": "Debit"}, {"id": "DEBIT_24921", "name": "Strategic Planning Cost", "category_type": "Debit"}, {"id": "DEBIT_10798", "name": "Supplier Due Diligence Expense", "category_type": "Debit"}, {"id": "DEBIT_19909", "name": "Technical Documentation", "category_type": "Debit"}, {"id": "DEBIT_11018", "name": "Technology and Infrastructure", "category_type": "Debit"}, {"id": "DEBIT_25390", "name": "Technology Evaluation Cost", "category_type": "Debit"}, {"id": "DEBIT_277", "name": "Technology expenses", "category_type": "Debit"}, {"id": "DEBIT_20", "name": "Technology Expenses", "category_type": "Debit"}, {"id": "DEBIT_22697", "name": "Testing Infrastructure Cost", "category_type": "Debit"}, {"id": "DEBIT_9765", "name": "Tokenization", "category_type": "Debit"}, {"id": "DEBIT_10202", "name": "Training and Development", "category_type": "Debit"}, {"id": "DEBIT_18882", "name": "Training Expense", "category_type": "Debit"}, {"id": "DEBIT_12520", "name": "Transaction Expense", "category_type": "Debit"}, {"id": "DEBIT_0", "name": "Travel Admin Expenses", "category_type": "Debit"}, {"id": "DEBIT_23944", "name": "Usability Testing Cost", "category_type": "Debit"}, {"id": "DEBIT_24589", "name": "User Analytics Cost", "category_type": "Debit"}, {"id": "DEBIT_17457", "name": "Utilities", "category_type": "Debit"}, {"id": "DEBIT_23701", "name": "UX Research Expense", "category_type": "Debit"}, {"id": "DEBIT_9044", "name": "Vendor Banking", "category_type": "Debit"}, {"id": "DEBIT_170", "name": "Vendor Onboarding Expense", "category_type": "Debit"}, {"id": "DEBIT_9068", "name": "Vendor Payments", "category_type": "Debit"}, {"id": "DEBIT_4868", "name": "Vendor Processing Expenses", "category_type": "Debit"}, {"id": "DEBIT_4091", "name": "<PERSON><PERSON><PERSON>up Costs", "category_type": "Debit"}, {"id": "DEBIT_16925", "name": "Visitor Management", "category_type": "Debit"}, {"id": "DEBIT_4000", "name": "Warehouse Operations", "category_type": "Debit"}]}