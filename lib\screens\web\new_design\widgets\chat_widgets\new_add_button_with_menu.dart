import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/services/new_file_upload_service.dart';
import 'package:nsl/services/new_file_upload_ocr_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

class AddButtonWithMenuNew extends StatefulWidget {
  final dynamic parentState;

  const AddButtonWithMenuNew(this.parentState, {super.key});

  @override
  State<AddButtonWithMenuNew> createState() => AddButtonWithMenuNewState();
}

class AddButtonWithMenuNewState extends State<AddButtonWithMenuNew> {
  bool isHovered = false;
  bool isMenuOpen = false;
  bool isLoading = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  // File upload state
  bool isFileUploaded = false;
  String uploadedFileName = '';
  String extractedText = '';

  @override
  void dispose() {
    _removeMenu(shouldUseState: false);
    super.dispose();
  }

  void _toggleMenu() {
    if (isMenuOpen) {
      _removeMenu();
    } else {
      _showMenu();
    }
  }

  void _removeMenu({shouldUseState = true}) {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (shouldUseState) {
      setState(() {
        isMenuOpen = false;
      });
    }
  }

  void _showMenu() {
    _removeMenu();

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Set a fixed menu height
    final menuHeight = 100; // Approximate height of the menu (increased for 3 items)

    // Get screen height to determine if menu should appear above or below
    final screenHeight = MediaQuery.of(context).size.height;

    // Check if there's enough space below the button
    final spaceBelow = screenHeight - (offset.dy + size.height);
    final showAbove = spaceBelow < menuHeight + 20; // Add some buffer space

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        // Position above or below based on available space, with minimal gap
        top: showAbove
            ? offset.dy - menuHeight + 15 // Position above with small gap
            : offset.dy + size.height - 5, // Position below with small gap
        child: Material(
          // elevation: 4.0,
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
          child: Container(
            width: 190,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
              border: Border.all(color: Color(0xffC1C1C1), width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // AddButtonMenuItem(
                //   imagePath: 'assets/images/upload_file.svg',
                //   text: 'Upload A File',
                //   onTap: () async {
                //     _removeMenu();
                //     await _uploadFile();
                //   },
                // ),
                AddButtonMenuItem(
                  imagePath: 'assets/images/upload_file.svg',
                  text: 'Upload File (New)',
                  onTap: () async {
                    _removeMenu();
                    await _newApiFileUpload();
                  },
                ),
                AddButtonMenuItem(
                  imagePath: 'assets/images/screenshot.svg',
                  text: 'Take A Screenshot',
                  onTap: () {
                    _removeMenu();
                    // Handle screenshot action
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isMenuOpen = true;
    });
  }

 

  Future<void> _newApiFileUpload({String strategy = 'auto', bool asyncProcessing = false}) async {
    final newFileUploadOcrService = NewFileUploadOcrService();
    final newFileUploadService = NewFileUploadService();

    // Show file upload loading state
    isLoading = true;

    // Update parent state file upload loading
    widget.parentState.setState(() {
      widget.parentState.isFileUploading = true;
    });

    try {
      // First, pick the file to get the file name immediately
      final file = await newFileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        isLoading = false;
        // Clear parent state file upload loading
        widget.parentState.setState(() {
          widget.parentState.isFileUploading = false;
        });
        return;
      }

      // Don't show file preview for new API - just show loading state
      setState(() {
        isLoading = true;
      });

      // Now process the file using the new API
      final uploadResult = await newFileUploadService.uploadFileNewApi(
        file,
        strategy: strategy,
        asyncProcessing: asyncProcessing,
      );

      // Hide loading state
      isLoading = false;

      // Check if widget is still mounted before updating UI
      if (!mounted) return;

      if (!uploadResult['success']) {
        // Handle upload error - remove the preview
        setState(() {
          isFileUploaded = false;
          uploadedFileName = '';
          extractedText = '';
        });

        widget.parentState.setState(() {
          widget.parentState.uploadedFileName = '';
          widget.parentState.uploadedFileText = '';
          widget.parentState.isFileUploaded = false;
          widget.parentState.isFileProcessing = false;
          widget.parentState.isFileUploading = false; // Clear file upload loading
        });

        newFileUploadOcrService.showOverlay(
           context,
          'Failed to upload file using new API: ${uploadResult['message']}',
          isError: true,
        );
        return;
      }

      // Success - directly put extracted text into chat input instead of showing preview
      final extractedTextResult = uploadResult['extracted_text'];

      // Check if there's any text content extracted
      if (extractedTextResult == null || extractedTextResult.trim().isEmpty) {
        // No text content found - show snack bar with relevant message
        widget.parentState.setState(() {
          widget.parentState.isFileUploading = false; // Clear file upload loading
          
          // Don't set file upload states since we're not showing preview
          widget.parentState.isFileUploaded = false;
          widget.parentState.isFileProcessing = false;
          widget.parentState.uploadedFileName = '';
          widget.parentState.uploadedFileText = '';
        });

        // Show message indicating no text was found
        newFileUploadOcrService.showOverlay(
          context,
          'No text content was found in the file. The file may not contain readable text or may be in an unsupported format.',
          isError: true,
        );

        Logger.info('New API - No text content found in uploaded file: ${file.name}');
        return;
      }

      // Update parent state to put text directly in chat input
      widget.parentState.setState(() {
        widget.parentState.isFileUploading = false; // Clear file upload loading
        
        // Put the extracted text directly into the chat input field
        if (widget.parentState.chatController != null) {
          widget.parentState.chatController.text = extractedTextResult;
        }
        
        // Don't set file upload states since we're not showing preview
        widget.parentState.isFileUploaded = false;
        widget.parentState.isFileProcessing = false;
        widget.parentState.uploadedFileName = '';
        widget.parentState.uploadedFileText = '';
      });

      // Show success message with additional info
      newFileUploadOcrService.showOverlay(
        context,
        'File uploaded successfully! Text content has been added to your chat input.',
      );

      // Log the extracted text and additional info
      Logger.info('New API - Extracted text: $extractedTextResult');
      Logger.info('New API - Job ID: ${uploadResult['job_id']}');
      Logger.info('New API - Processing time: ${uploadResult['processing_time']}');
      Logger.info('New API - Tools used: ${uploadResult['tools_used']}');
    } catch (e) {
      // Hide loading state and remove preview on error
      isLoading = false;

      // Log the detailed error for debugging
      Logger.error('Detailed error in _newApiFileUpload: $e');
      Logger.error('Error type: ${e.runtimeType}');
      Logger.error('Error string: ${e.toString()}');

      setState(() {
        isFileUploaded = false;
        uploadedFileName = '';
        extractedText = '';
      });

      widget.parentState.setState(() {
        widget.parentState.uploadedFileName = '';
        widget.parentState.uploadedFileText = '';
        widget.parentState.isFileUploaded = false;
        widget.parentState.isFileProcessing = false;
        widget.parentState.isFileUploading = false; // Clear file upload loading
      });

      newFileUploadOcrService.showOverlay(
        context,
        'Error using new API: $e',
        isError: true,
      );
    }
  }

  // Show loading overlay

  // Hide loading overlay

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Container(
          height: isLoading ? 16 : 28,
          width: isLoading ? 16 : 28,
          margin: EdgeInsets.symmetric(
              horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
          padding: EdgeInsets.zero,
          decoration: BoxDecoration(
              color: isLoading
                  ? Colors.transparent
                  : isMenuOpen
                      ? Theme.of(context).colorScheme.primary
                      : isHovered
                          ? const Color(0xff0058FF)
                          : const Color(0xffE4EDFF),
              borderRadius: BorderRadius.circular(AppSpacing.lg)),
          child:
              // isLoading
              // ? CircularProgressIndicator(
              //     strokeWidth: 2,
              //     valueColor: AlwaysStoppedAnimation<Color>(
              //       Color(0xff0058FF),
              //     ),
              //   )
              // :
              IconButton(
            padding: EdgeInsets.zero,
            onPressed: _toggleMenu,
            icon: Icon(isMenuOpen ? Icons.close : Icons.add),
            iconSize: 18,
            color: isMenuOpen || isHovered ? Colors.white : null,
          ),
        ),
      ),
    );
  }
}
