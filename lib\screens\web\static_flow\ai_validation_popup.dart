import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class AiValidationPopupDemo extends StatelessWidget {
  const AiValidationPopupDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: () => showDialog(
          context: context,
          barrierDismissible: true,
          builder: (context) => const AiValidationPopup(),
        ),
        child: const Text('Show Email Uniqueness Popup'),
      ),
    );
  }
}

class AiValidationPopup extends StatelessWidget {
  const AiValidationPopup({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
      child: Container(
        width: 530,
        height: 580,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(14)),
                color: Color(0xFFF7F9FB),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x0F000000),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        const Icon(Icons.email_sharp, size: 24, color: Colors.blue),
                        const SizedBox(width: 8),
                         Text(
                          'Email Uniqueness',
                            style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                           color: Color(0xFF9AD4FF),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'SIMPLE',
                            style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.black54,
                          ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding: EdgeInsets.all(4.0),
                      child: Icon(Icons.close, size: 22, color: Color(0xFF222222)),
                    ),
                  ),
                ],
              ),
            ),
            // Content (scrollable)
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 18),
                     Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 6),
                     Text(
                      'Prevents duplicate customer accounts by making email addresses unique and validates proper email format for reliable communication.',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Color(0xFF707070),
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 8),
                     Text(
                      'Used by Amazon, Shopify, Stripe and 96% of e-commerce platforms',
                       style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Color(0xFF707070),
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 22),
                     Text(
                      'What Gets Added',
                        style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Color(0xFFF7F9FB),
                        border: Border.all(color: Color(0xFFE0E0E0)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            'Attribute Business Rules Section:',
                             style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _RuleBox(
                            title: 'Rule 1: Email uniqueness validation',
                            rule: '''Customer | email | email | IS_UNIQUE | true | Email already exists | Email is available | Email must be unique''',
                          ),
                          const SizedBox(height: 12),
                          _RuleBox(
                            title: 'Rule 2: Email format validation',
                            rule: '''Customer | email | email | IS_VALID_EMAIL | true | Please enter a valid email | Valid email format | Invalid email format''',
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 22),
                     Text(
                      'What Changes',
                       style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Color(0xFFF7F9FB),
                        border: Border.all(color: Color(0xFFE0E0E0)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            'Attributes Section - Customer Entity:',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 12),
                           Text(
                            'email field: Required flag changes from NO to YES\nemail field: Unique flag changes from NO to YES',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Color(0xFF6B6B6B),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                        Text(
                      'Before vs After',
                       style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                     const SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Color(0xFFFFF5F5),
                              border: Border.all(color: Color(0xFFE0E0E0)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.all(12),
                            
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                 Text(
                                  'Before - Attributes Table',
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.bodySmall(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Color(0xFFbb7c78),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Color(0xFFE0E0E0)),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'email | Email Address | string | NO | NO | none | - | Primary email',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily: FontManager.fontFamilyInter,
                                      color: Color(0xFF6B6B6B),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Color(0xFFF5FFF5),
                              border: Border.all(color: Color(0xFFE0E0E0)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                 Text(
                                  'After - Attributes Table',
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.bodySmall(context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Color(0xFF359c73),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Color(0xFFE0E0E0)),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                   'email | Email Address | string | YES | YES | none | - | Primary email',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily: FontManager.fontFamilyInter,
                                      color: Color(0xFF6B6B6B),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                     Center(
                       child: Text(
                        '+ 2 new entries in Attribute Business Rules section',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Color(0xFF359c73),
                        ),
                      ),
                     ),
                    const SizedBox(height: 10),
                    Container(
                      margin: EdgeInsets.only(bottom: 20),
                      width: double.infinity,
                      padding: const EdgeInsets.all(10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            'Requirements Check',
                              style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                          ),
                          const SizedBox(height: 12),
                           Container(
                            margin: EdgeInsets.only(left: 15),
                             child: Text(
                              'Email attribute exists in Customer entity\nNo conflicting validation rules found\nCompatible with current template structure',
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodySmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.black87,
                                height: 2,
                              ),
                                                       ),
                           ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Footer
           Container(
  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
  decoration: const BoxDecoration(
    color: Color(0xFFF7F9FB),
    borderRadius: BorderRadius.vertical(bottom: Radius.circular(14)),
    boxShadow: [
      BoxShadow(
        color: Color(0x0F000000),
        blurRadius: 4,
        offset: Offset(0, -2),
      ),
    ],
  ),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      // 🔹 Skip for Now Button
      OutlinedButton(
        onPressed: () => Navigator.of(context).pop(),
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF222222),
          textStyle: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontFamily: FontManager.fontFamilyInter,
          ),
          side: const BorderSide(color: Color(0xFFD1D5DB)),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        child: const Text('Skip for Now', textAlign: TextAlign.center,),
      ),
      const SizedBox(width: 12),

      // 🔵 Apply This Button
      ElevatedButton(
        onPressed: () => Navigator.of(context).pop(),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF0058FF),
          foregroundColor: Colors.white,
          textStyle: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontFamily: FontManager.fontFamilyInter,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            alignment: Alignment.center,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          elevation: 0,
            minimumSize: const Size(0, 40),
        ),
        child: const Text('Apply This', textAlign: TextAlign.center,),
      ),
    ],
  ),
),

          ],
        ),
      ),
    );
  }
}

class _RuleBox extends StatelessWidget {
  final String title;
  final String rule;
  const _RuleBox({required this.title, required this.rule});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
       
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Color(0xFFE0E0E0)),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                rule,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontFamily: FontManager.fontFamilyInter,
                  color: Color(0xFF6B6B6B),
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.2,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
