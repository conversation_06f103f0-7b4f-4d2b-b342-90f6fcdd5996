import 'dart:convert';
import 'package:flutter/services.dart';

class DummyConsolidatedService {
  static List<dynamic>? _cachedData;
  
  static Future<List<dynamic>> loadDummyData() async {
    if (_cachedData != null) {
      return _cachedData!;
    }
    
    try {
      final String jsonString = await rootBundle.loadString('assets/data/dummy_consolidated_data.json');
      _cachedData = json.decode(jsonString);
      return _cachedData!;
    } catch (e) {
      print('Error loading dummy consolidated data: $e');
      return [];
    }
  }
  
  static List<Map<String, dynamic>> getConsolidatedSummaryForNode(String nodeId) {
    // Return dummy consolidated data with 0 values for all 4 cards
    return [
      {
        "title": "Total Revenue",
        "value": "\$0",
        "change": "+0.0% vs last month"
      },
      {
        "title": "Net Margin",
        "value": "0.0%",
        "change": "+0.0% vs last month"
      },
      {
        "title": "Total Transactions",
        "value": "0",
        "change": "+0.0% vs last month"
      },
      {
        "title": "BET Efficiency",
        "value": "0.0%",
        "change": "+0.0% vs last month"
      }
    ];
  }
  
  static Future<List<Map<String, dynamic>>> getConsolidatedSummaryForNodeAsync(String nodeId) async {
    final dummyData = await loadDummyData();
    
    // Find the node data by ID in the JSON array
    for (var item in dummyData) {
      if (item is Map<String, dynamic> && item['id'] == nodeId) {
        // Return dummy consolidated data based on the node data
        return [
          {
            "title": "Total Go",
            "value": item['consolidated']?['total_gos'] ?? '0',
            "change": "+0.0% vs last month"
          },
          {
            "title": "Internal Elimination",
            "value": item['consolidated']?['internal_elimination'] ?? '0',
            "change": "+0.0% vs last month"
          },
          {
            "title": "Efficiency",
            "value": item['consolidated']?['effeciency'] ?? '0',
            "change": "+0.0% vs last month"
          },
          {
            "title": "Team Coordination",
            "value": item['consolidated']?['team_coordination'] ?? '0',
            "change": "+0.0% vs last month"
          }
        ];
      }
    }
    
    // Fallback to default dummy data
    return getConsolidatedSummaryForNode(nodeId);
  }
  
  static Map<String, dynamic>? getConsolidatedDataForNode(String nodeId) {
    // Load the JSON data synchronously from cache or return null if not loaded
    if (_cachedData == null) {
      // Try to load data asynchronously and return null for now
      loadDummyData();
      return null;
    }
    
    // Find the node data by ID in the JSON array
    for (var item in _cachedData!) {
      if (item is Map<String, dynamic> && item['id'] == nodeId) {
        return item;
      }
    }
    
    return null;
  }
}
