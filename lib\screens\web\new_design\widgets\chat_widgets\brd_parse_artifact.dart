import 'package:flutter/material.dart';
import 'dart:convert';

class BRDSolutionParser {
  static List<TextSpan> parseToTextSpans(Map<String, dynamic> jsonData) {
    List<TextSpan> spans = [];

    // Parse complete_json
    if (jsonData.containsKey('complete_json')) {
      spans.addAll(_parseCompleteJson(jsonData['complete_json']));
    }

    return spans;
  }

  static List<TextSpan> _parseCompleteJson(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    // Title
    // spans
    //     .add(_createHeaderSpan('Digital Agency CRM Solution Specification', 1));
    // spans.add(_createNewLineSpan(2));

    // Overview
    spans.add(_createHeaderSpan('Overview', 2));
    spans.add(_createNewLineSpan());

    if (data.containsKey('section_1_user_persona_and_solution_archetype')) {
      var metadata = data['section_1_user_persona_and_solution_archetype']
          ['solution_metadata'];
      spans.add(_createBulletSpan(
          'Solution Type: ${metadata['solution_type'] ?? 'N/A'}'));
      spans
          .add(_createBulletSpan('Industry: ${metadata['industry'] ?? 'N/A'}'));
      spans.add(
          _createBulletSpan('Team Size: ${metadata['team_size'] ?? 'N/A'}'));
      spans.add(_createBulletSpan(
          'Complexity Level: ${metadata['complexity_level'] ?? 'N/A'}/10'));
    }

    spans.add(_createDividerSpan());

    // Section 1: User Personas
    spans.addAll(_parseUserPersonas(data));

    // Section 2: Organization
    spans.addAll(_parseOrganization(data));

    // Section 3: Entities
    spans.addAll(_parseEntities(data));

    // Section 4: Functions
    spans.addAll(_parseFunctions(data));

    // Section 5: Workflows
    spans.addAll(_parseWorkflows(data));

    // Section 6: Permissions
    spans.addAll(_parsePermissions(data));

    // Section 7: Analytics
    spans.addAll(_parseAnalytics(data));

    return spans;
  }

  static List<TextSpan> _parseUserPersonas(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('1. User Personas & Solution Context', 2));
    spans.add(_createNewLineSpan(2));

    spans.add(_createHeaderSpan('Key User Roles', 3));
    spans.add(_createNewLineSpan());

    if (data.containsKey('section_1_user_persona_and_solution_archetype')) {
      var personas = data['section_1_user_persona_and_solution_archetype']
          ['user_personas'] as List<dynamic>;

      for (var persona in personas) {
        spans.add(_createHeaderSpan(persona['role'] ?? 'Unknown Role', 4));
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Description: '));
        spans.add(
            _createRegularSpan(persona['description'] ?? 'No description'));
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Key Needs: '));
        if (persona['needs'] != null) {
          spans.add(_createRegularSpan((persona['needs'] as List).join(', ')));
        }
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Pain Points: '));
        if (persona['pain_points'] != null) {
          spans.add(
              _createRegularSpan((persona['pain_points'] as List).join(', ')));
        }
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Technical Proficiency: '));
        spans.add(
            _createRegularSpan(persona['technical_proficiency'] ?? 'Unknown'));
        spans.add(_createNewLineSpan(2));
      }
    }

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parseOrganization(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('2. Organizational Structure', 2));
    spans.add(_createNewLineSpan(2));

    if (data.containsKey('section_2_organization_information')) {
      var orgData = data['section_2_organization_information'];

      spans.add(_createHeaderSpan(
          'Team Composition (${orgData['team_structure']['total_size']} Members)',
          3));
      spans.add(_createNewLineSpan());

      if (orgData['roles'] != null) {
        var roles = orgData['roles'] as List<dynamic>;

        for (var role in roles) {
          spans.add(_createBoldSpan('${role['name']} '));
          spans.add(_createItalicSpan('(${role['authority_level']} level)'));
          spans.add(_createNewLineSpan());

          spans.add(_createIndentSpan('Department: ${role['department']}'));
          spans.add(_createIndentSpan(
              'Reports to: ${role['reports_to'] ?? 'None (Top level)'}'));

          if (role['key_functions'] != null) {
            spans.add(_createIndentSpan(
                'Key Functions: ${(role['key_functions'] as List).join(', ')}'));
          }

          if (role['permissions'] != null) {
            spans.add(_createIndentSpan(
                'Permissions: ${(role['permissions'] as List).join(', ')}'));
          }

          spans.add(_createNewLineSpan());
        }
      }
    }

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parseEntities(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('3. Core Business Entities', 2));
    spans.add(_createNewLineSpan(2));

    spans.add(_createHeaderSpan('Primary Data Models', 3));
    spans.add(_createNewLineSpan());

    if (data.containsKey('section_3_entities_and_attributes')) {
      var entities = data['section_3_entities_and_attributes']
          ['core_business_entities'] as List<dynamic>;

      for (var entity in entities) {
        spans.add(_createHeaderSpan('${entity['name']} Entity', 4));
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Attributes: '));
        if (entity['attributes'] != null) {
          List<String> attrStrings = [];
          for (var attr in entity['attributes']) {
            attrStrings.add('${attr['name']} (${attr['type']})');
          }
          spans.add(_createRegularSpan(attrStrings.join(', ')));
        }
        spans.add(_createNewLineSpan());

        if (entity['lifecycle_states'] != null) {
          spans.add(_createBoldSpan('Lifecycle States: '));
          spans.add(_createRegularSpan(
              (entity['lifecycle_states'] as List).join(' → ')));
          spans.add(_createNewLineSpan());
        }

        spans.add(_createNewLineSpan());
      }

      spans.add(_createHeaderSpan('Data Flow Patterns', 3));
      spans.add(_createNewLineSpan());

      if (data['section_3_entities_and_attributes']['data_flow_patterns'] !=
          null) {
        var flows = data['section_3_entities_and_attributes']
            ['data_flow_patterns'] as List<dynamic>;
        var index = 1;

        for (var flow in flows) {
          spans.add(_createNumberedSpan('$index. ${flow['function']}: '));

          String inputEntities = (flow['input_entities'] as List).join(' + ');
          String outputEntities = (flow['output_entities'] as List).join(', ');

          spans.add(_createRegularSpan('$inputEntities → $outputEntities'));
          spans.add(_createNewLineSpan());
          index++;
        }
      }
    }

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parseFunctions(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('4. Core Functions (Local Objectives)', 2));
    spans.add(_createNewLineSpan(2));

    if (data.containsKey('section_4_functions_local_objectives')) {
      var functions = data['section_4_functions_local_objectives']['lo_slots']
          as List<dynamic>;
      var index = 1;

      for (var function in functions) {
        spans.add(_createHeaderSpan('$index. ${function['name']}', 3));
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Purpose: '));
        spans.add(
            _createRegularSpan(function['description'] ?? 'No description'));
        spans.add(_createNewLineSpan());

        spans.add(_createBoldSpan('Complexity: '));
        spans
            .add(_createRegularSpan(function['complexity_level'] ?? 'Unknown'));
        spans.add(_createNewLineSpan());

        if (function['assigned_roles'] != null) {
          spans.add(_createBoldSpan('Assigned Roles: '));
          spans.add(_createRegularSpan(
              (function['assigned_roles'] as List).join(', ')));
          spans.add(_createNewLineSpan());
        }

        if (function['business_rules'] != null) {
          spans.add(_createBoldSpan('Business Rules:'));
          spans.add(_createNewLineSpan());
          for (var rule in function['business_rules']) {
            spans.add(_createIndentSpan('• $rule'));
          }
        }

        if (function['validation_criteria'] != null) {
          spans.add(_createBoldSpan('Validation Requirements:'));
          spans.add(_createNewLineSpan());
          for (var criteria in function['validation_criteria']) {
            spans.add(_createIndentSpan('• $criteria'));
          }
        }

        spans.add(_createNewLineSpan(2));
        index++;
      }
    }

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parseWorkflows(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('5. Workflows (Global Objectives)', 2));
    spans.add(_createNewLineSpan(2));

    if (data.containsKey('section_5_workflows_global_objectives')) {
      var workflows = data['section_5_workflows_global_objectives']['go_slots']
          as List<dynamic>;
      var index = 1;

      for (var workflow in workflows) {
        spans.add(_createHeaderSpan('$index. ${workflow['name']}', 3));
        spans.add(_createNewLineSpan());

        if (workflow['trigger_conditions'] != null) {
          spans.add(_createBoldSpan('Trigger: '));
          spans.add(_createRegularSpan(
              (workflow['trigger_conditions'] as List).join(', ')));
          spans.add(_createNewLineSpan());
        }

        spans.add(_createBoldSpan('Complexity: '));
        spans
            .add(_createRegularSpan(workflow['complexity_level'] ?? 'Unknown'));
        spans.add(_createNewLineSpan());

        if (workflow['roles_involved'] != null) {
          spans.add(_createBoldSpan('Involved Roles: '));
          spans.add(_createRegularSpan(
              (workflow['roles_involved'] as List).join(', ')));
          spans.add(_createNewLineSpan());
        }

        if (workflow['pathways'] != null) {
          spans.add(_createNewLineSpan());
          spans.add(_createHeaderSpan('Pathways:', 4));
          spans.add(_createNewLineSpan());

          for (var pathway in workflow['pathways']) {
            spans.add(_createBoldSpan('${pathway['name']}:'));
            spans.add(_createNewLineSpan());

            if (pathway['steps'] != null) {
              var stepIndex = 1;
              for (var step in pathway['steps']) {
                spans.add(_createIndentSpan('$stepIndex. $step'));
                stepIndex++;
              }
            }
            spans.add(_createNewLineSpan());
          }
        }

        if (workflow['success_criteria'] != null) {
          spans.add(_createBoldSpan('Success Criteria: '));
          spans.add(_createRegularSpan(
              (workflow['success_criteria'] as List).join(', ')));
          spans.add(_createNewLineSpan());
        }

        spans.add(_createNewLineSpan(2));
        index++;
      }
    }

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parsePermissions(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('6. Role Permissions & Access Control', 2));
    spans.add(_createNewLineSpan(2));

    spans.add(_createHeaderSpan('Permission Summary', 3));
    spans.add(_createNewLineSpan());

    spans.add(_createRegularSpan(
        'All roles (Digital Account Manager, Creative Lead, Project Manager, Analytics Manager, Operations Manager) have:'));
    spans.add(_createNewLineSpan());

    spans.add(_createBulletSpan(
        'Entity Access: Full access to User, Record, and Transaction entities'));
    spans.add(_createBulletSpan(
        'Function Execution: Can execute all core functions'));
    spans.add(
        _createBulletSpan('Workflow Initiation: Can initiate all workflows'));
    spans.add(_createBulletSpan('Authority Level: Write access'));
    spans.add(_createBulletSpan(
        'Escalation Authority: False (except Operations Manager has system admin rights)'));

    spans.add(_createNewLineSpan());
    spans.add(_createHeaderSpan('Access Control Hierarchy', 3));
    spans.add(_createNewLineSpan());

    spans.add(_createBulletSpan(
        'Operations Manager: Full system administration and oversight'));
    spans.add(_createBulletSpan(
        'All Other Roles: Equal functional access with role-specific responsibilities'));

    spans.add(_createDividerSpan());
    return spans;
  }

  static List<TextSpan> _parseAnalytics(Map<String, dynamic> data) {
    List<TextSpan> spans = [];

    spans.add(_createHeaderSpan('7. Analytics & Intelligence Requirements', 2));
    spans.add(_createNewLineSpan(2));

    if (data.containsKey('section_7_intelligence')) {
      var intelligence = data['section_7_intelligence'];

      if (intelligence['analytics_requirements'] != null) {
        spans.add(_createHeaderSpan('Key Metrics to Track', 3));
        spans.add(_createNewLineSpan());

        for (var metric in intelligence['analytics_requirements']) {
          spans.add(_createBulletSpan(
              '${metric['metric']} (${metric['type']}) - ${metric['frequency']} reporting'));
        }
        spans.add(_createNewLineSpan());
      }

      if (intelligence['reporting_needs'] != null) {
        spans.add(_createHeaderSpan('Dashboard Requirements', 3));
        spans.add(_createNewLineSpan());

        spans.add(_createRegularSpan(
            'Each role receives a ${intelligence['reporting_needs'][0]['frequency']} dashboard containing:'));
        spans.add(_createNewLineSpan());

        if (intelligence['reporting_needs'].isNotEmpty) {
          var content = intelligence['reporting_needs'][0]['content'] as List;
          for (var item in content) {
            spans.add(_createBulletSpan(item));
          }
        }

        spans.add(_createNewLineSpan());
        spans.add(_createBoldSpan('Target Roles for Dashboards:'));
        spans.add(_createNewLineSpan());

        for (var report in intelligence['reporting_needs']) {
          spans.add(_createBulletSpan(report['target_role']));
        }
      }
    }

    spans.add(_createNewLineSpan(2));
    spans.add(_createDividerSpan());

    spans.add(_createHeaderSpan('Implementation Notes', 2));
    spans.add(_createNewLineSpan(2));

    spans.add(_createRegularSpan(
        'This CRM solution is designed for a small digital agency with enterprise clients, focusing on:'));
    spans.add(_createNewLineSpan());

    spans.add(_createBulletSpan(
        'Streamlined client onboarding and campaign management'));
    spans.add(_createBulletSpan(
        'Comprehensive project tracking and deliverable management'));
    spans.add(_createBulletSpan('Robust analytics and reporting capabilities'));
    spans.add(_createBulletSpan('Clear role-based permissions and workflows'));
    spans.add(_createBulletSpan(
        'Integration-ready architecture to reduce manual processes'));

    spans.add(_createNewLineSpan(2));
    spans.add(_createRegularSpan(
        'The system emphasizes collaboration between account management, creative, and project management teams while maintaining clear accountability and approval processes for client work.'));

    return spans;
  }

  // Helper methods for creating styled TextSpans
  static TextSpan _createHeaderSpan(String text, int level) {
    double fontSize;
    FontWeight fontWeight;
    Color color;

    switch (level) {
      case 1:
        fontSize = 24.0;
        fontWeight = FontWeight.bold;
        color = Colors.blue.shade800;
        break;
      case 2:
        fontSize = 20.0;
        fontWeight = FontWeight.bold;
        color = Colors.blue.shade700;
        break;
      case 3:
        fontSize = 18.0;
        fontWeight = FontWeight.w600;
        color = Colors.blue.shade600;
        break;
      case 4:
        fontSize = 16.0;
        fontWeight = FontWeight.w600;
        color = Colors.blue.shade500;
        break;
      default:
        fontSize = 16.0;
        fontWeight = FontWeight.w500;
        color = Colors.black87;
    }

    return TextSpan(
      text: text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
    );
  }

  static TextSpan _createBoldSpan(String text) {
    return TextSpan(
      text: text,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        color: Colors.black87,
        fontSize: 14.0,
      ),
    );
  }

  static TextSpan _createItalicSpan(String text) {
    return TextSpan(
      text: text,
      style: const TextStyle(
        fontStyle: FontStyle.italic,
        color: Colors.grey,
        fontSize: 14.0,
      ),
    );
  }

  static TextSpan _createRegularSpan(String text) {
    return TextSpan(
      text: text,
      style: const TextStyle(
        color: Colors.black87,
        fontSize: 14.0,
      ),
    );
  }

  static TextSpan _createBulletSpan(String text) {
    return TextSpan(
      children: [
        const TextSpan(
          text: '• ',
          style: TextStyle(
            color: Colors.blue,
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextSpan(
          text: text,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 14.0,
          ),
        ),
        const TextSpan(text: '\n'),
      ],
    );
  }

  static TextSpan _createNumberedSpan(String text) {
    return TextSpan(
      text: text,
      style: const TextStyle(
        color: Colors.blue,
        fontSize: 14.0,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  static TextSpan _createIndentSpan(String text) {
    return TextSpan(
      children: [
        const TextSpan(text: '  '),
        TextSpan(
          text: text,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 14.0,
          ),
        ),
        const TextSpan(text: '\n'),
      ],
    );
  }

  static TextSpan _createNewLineSpan([int count = 1]) {
    return TextSpan(text: '\n' * count);
  }

  static TextSpan _createDividerSpan() {
    return TextSpan(
      children: [
        const TextSpan(text: '\n'),
        TextSpan(
          text: '─' * 25,
          style: TextStyle(
            color: Colors.grey.shade400,
            fontSize: 12.0,
          ),
        ),
        const TextSpan(text: '\n\n'),
      ],
    );
  }
}

// Usage Example Widget
class BRDSolutionDisplay extends StatelessWidget {
  final Map<String, dynamic> jsonData;

  const BRDSolutionDisplay({Key? key, required this.jsonData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('CRM Solution Specification'),
      //   backgroundColor: Colors.blue.shade700,
      //   foregroundColor: Colors.white,
      // ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: RichText(
          text: TextSpan(
            children: BRDSolutionParser.parseToTextSpans(jsonData),
          ),
        ),
      ),
    );
  }
}

// Example usage:
// final Map<String, dynamic> crmData = json.decode(yourJsonString);
