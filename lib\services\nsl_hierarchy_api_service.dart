import 'dart:convert';
import 'package:http/http.dart' as http;
import '../screens/web/nsl_hierarchy_web/models/nsl_tree_hierarchy_model_new.dart';
import '../screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import '../screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import '../screens/web/nsl_hierarchy_web/models/go_lo_list_model.dart';

class NslHierarchyApiService {
  static const String baseUrl = 'http://***********:8085';
  static const String modulesEndpoint = '/nsl-prescriptor/api/v1/modules';
  
  static Future<NslTreeHierarchy?> fetchModules() async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint');
      print('Fetching NSL hierarchy data from API...');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = NslTreeHierarchy.fromJson(jsonData);
        print('Successfully loaded ${result.result?.nodes?.length ?? 0} nodes from API');
        return result;
      } else {
        print('API Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching modules: $e');
      return null;
    }
  }

  static Future<NslTreeSidePanel?> fetchNodeDetails(String nodeId) async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint/$nodeId');
      print('Fetching node details for nodeId: $nodeId');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = NslTreeSidePanel.fromJson(jsonData);
        print('Successfully loaded node details for $nodeId - GOs: ${result.result?.goCount}, LOs: ${result.result?.loCount}');
        return result;
      } else {
        print('API Error for node $nodeId: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching node details for $nodeId: $e');
      return null;
    }
  }

  static Future<MetricsInfo?> fetchNodeTransactions(String nodeId, int fromUnixSeconds, int toUnixSeconds) async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint/$nodeId/transactions?from=$fromUnixSeconds&to=$toUnixSeconds');
      print('Fetching transactions for nodeId: $nodeId from $fromUnixSeconds to $toUnixSeconds');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = MetricsInfo.fromJson(jsonData);
        print('Successfully loaded transactions for $nodeId - Transactions: ${result.result?.totalTransactions}, Revenue: ${result.result?.revenue}');
        return result;
      } else {
        print('API Error for transactions $nodeId: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching transactions for $nodeId: $e');
      return null;
    }
  }

  static Future<GoLoListModel?> fetchGoLoList({String? nodeId, int pageNumber = 1, int pageSize = 50}) async {
    try {
      // Use the provided nodeId or default to 'owner'
      final targetNodeId = nodeId ?? 'owner';
      final url = Uri.parse('$baseUrl$modulesEndpoint/$targetNodeId/golo?pageNumber=$pageNumber&pageSize=$pageSize');
      print('Fetching GO/LO list for nodeId: $targetNodeId - Page: $pageNumber, Size: $pageSize');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = GoLoListModel.fromJson(jsonData);
        print('Successfully loaded GO/LO list - Page: $pageNumber, GOs: ${result.result?.data?.length ?? 0}, Total: ${result.result?.totalHits}');
        return result;
      } else {
        print('API Error for GO/LO list: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching GO/LO list: $e');
      return null;
    }
  }
}
