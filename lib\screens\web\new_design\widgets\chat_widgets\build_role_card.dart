import 'package:flutter/material.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/user_profile_card.dart';
import 'package:nsl/utils/constants.dart';
import 'package:nsl/utils/mobile_tooltip_utils.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';

/// A reusable widget that displays a role card with selection state, checkbox, and tooltip.
///
/// This widget is used to display role information in a list, with support for
/// selection, hover effects, and detailed information in a tooltip.
class BuildRoleCard extends StatelessWidget {
  /// The role information to display
  final RoleInfo role;

  /// Whether this role is currently selected
  final bool isSelected;

  /// Callback when the role is tapped
  final Function(RoleInfo) onRoleTap;

  final bool? isBorderRequired;

  final bool? isHoverCardRequired;

  final Color? selectedColor;

  const BuildRoleCard({
    super.key,
    required this.role,
    required this.isSelected,
    required this.onRoleTap,
    this.isBorderRequired = true,
    this.isHoverCardRequired = true,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        // Highlight for selected role
        color: isSelected
            ? (selectedColor ?? Color.fromARGB(255, 239, 166, 9))
            : Colors.white,
        border: (isBorderRequired ?? false)
            ? Border(
                top: BorderSide(color: Colors.grey.shade200),
                left: isSelected
                    ? BorderSide(color: Colors.blue.shade700, width: 4)
                    : BorderSide.none,
              )
            : Border(),
      ),
      child: InkWell(
        onTap: () => onRoleTap(role),
        hoverColor: isSelected
            ? Color(0xFFBBDEFB) // Slightly darker blue on hover
            : Colors.grey.shade100,
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Check if we're on a mobile screen
            final isMobile = MediaQuery.of(context).size.width <
                AppConstants.mobileBreakpoint;

            if (isMobile) {
              // Mobile layout: Column
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Role information
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Role title with tooltip
                        isHoverCardRequired ?? false
                            ? _buildRoleTitleWithTooltip(context)
                            : Text(
                                "${role.title} ",
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontManager.bold,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                  decorationColor: Colors.blue.shade700,
                                  decorationThickness: 1,
                                ),
                              ),
                        SizedBox(height: 4),

                        // Role description
                        Text(
                          role.description,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2, // Allow more lines on mobile
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            color: Colors.grey.shade700,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: isSelected
                                ? FontManager.medium
                                : FontManager.regular,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            } else {
              // Desktop/Tablet layout: Row (original layout)
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Role information
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          isHoverCardRequired ?? false
                              ?
                              // Role title with tooltip
                              _buildRoleTitleWithTooltip(context)
                              : Text(
                                  "${role.title} ",
                                  style: TextStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontManager.bold,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                    // color: isSelected
                                    //     ? Colors.blue.shade800
                                    //     : Colors.black,
                                    decorationColor: Colors.blue.shade700,
                                    decorationThickness: 1,
                                  ),
                                ),
                          SizedBox(height: 4),

                          // Role description
                          Expanded(
                            child: Text(
                              role.description,
                              overflow: TextOverflow.ellipsis,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                color: Colors.grey.shade700,
                                // color: isSelected
                                //     ? Colors.blue.shade700
                                //     : Colors.grey.shade700,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                // height: 1.2,
                                fontWeight: isSelected
                                    ? FontManager.medium
                                    : FontManager.regular,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  /// Builds the checkbox column on the left side of the card
  Widget _buildCheckboxColumn() {
    return Column(
      children: [
        SizedBox(height: AppSpacing.xs),
        SizedBox(
          width: 20,
          child: Checkbox(
            value: false,
            onChanged: (_) {},
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
            side: BorderSide(
              color: Color(0xFFBEBEBE), // Custom border color
              width: 1, // Custom border width
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the role title with tooltip showing detailed information
  Widget _buildRoleTitleWithTooltip(BuildContext context) {
    final tooltipContent = UserProfileCard(
      id: role.id ?? 'so008',
      version: role.version ?? 'V00019',
      displayName: role.title,
      createdBy: role.createdBy ?? 'John Smith',
      createdDate: role.createdDate ?? '10/04/2025',
      modifiedBy: role.modifiedBy ?? 'Jane Doe',
      modifiedDate: role.modifiedDate ?? '28/04/2025',
      roleTitle: role.title,
      roleDescription: role.description,
      width: MobileTooltipUtils.isMobile(context)
          ? double.infinity
          : MediaQuery.of(context).size.width / 3,
      leftMargin: MobileTooltipUtils.isMobile(context) ? 0 : 170,
    );

    final titleText = Text(
      "${role.title} ",
      style: TextStyle(
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontWeight: FontManager.bold,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
        decorationColor: Colors.blue.shade700,
        decorationThickness: 1,
      ),
    );

    return MobileTooltipUtils.createResponsiveTooltipWrapper(
      context: context,
      child: titleText,
      tooltipContent: tooltipContent,
      webTooltipChild: MouseRegion(
        child: Tooltip(
          richMessage: WidgetSpan(child: tooltipContent),
          preferBelow: true,
          verticalOffset: 20,
          padding: EdgeInsets.zero,
          margin: EdgeInsets.zero,
          showDuration: Duration(seconds: 10),
          decoration: BoxDecoration(
            color: Colors.transparent,
            boxShadow: [],
          ),
          child: titleText,
        ),
      ),
    );
  }
}
