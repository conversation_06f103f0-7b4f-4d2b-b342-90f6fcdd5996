class NslTreeSidePanel {
    int? status;
    String? message;
    Result? result;

    NslTreeSidePanel({
        this.status,
        this.message,
        this.result,
    });

    factory NslTreeSidePanel.fromJson(Map<String, dynamic> json) => NslTreeSidePanel(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
    };

    NslTreeSidePanel copyWith({
        int? status,
        String? message,
        Result? result,
    }) => 
        NslTreeSidePanel(
            status: status ?? this.status,
            message: message ?? this.message,
            result: result ?? this.result,
        );
}

class Result {
    String? id;
    String? nodeType;
    String? name;
    List<Go>? gos;
    int? goCount;
    int? loCount;

    Result({
        this.id,
        this.nodeType,
        this.name,
        this.gos,
        this.goCount,
        this.loCount,
    });

    factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"],
        nodeType: json["nodeType"],
        name: json["name"],
        gos: json["gos"] == null ? [] : List<Go>.from(json["gos"]!.map((x) => Go.fromJson(x))),
        goCount: json["goCount"],
        loCount: json["loCount"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "nodeType": nodeType,
        "name": name,
        "gos": gos == null ? [] : List<dynamic>.from(gos!.map((x) => x.toJson())),
        "goCount": goCount,
        "loCount": loCount,
    };

    Result copyWith({
        String? id,
        String? nodeType,
        String? name,
        List<Go>? gos,
        int? goCount,
        int? loCount,
    }) => 
        Result(
            id: id ?? this.id,
            nodeType: nodeType ?? this.nodeType,
            name: name ?? this.name,
            gos: gos ?? this.gos,
            goCount: goCount ?? this.goCount,
            loCount: loCount ?? this.loCount,
        );
}

class Go {
    String? id;
    String? name;
    List<Lo>? los;

    Go({
        this.id,
        this.name,
        this.los,
    });

    factory Go.fromJson(Map<String, dynamic> json) => Go(
        id: json["id"],
        name: json["name"],
        los: json["los"] == null ? [] : List<Lo>.from(json["los"]!.map((x) => Lo.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "los": los == null ? [] : List<dynamic>.from(los!.map((x) => x.toJson())),
    };

    Go copyWith({
        String? id,
        String? name,
        List<Lo>? los,
    }) => 
        Go(
            id: id ?? this.id,
            name: name ?? this.name,
            los: los ?? this.los,
        );
}

class Lo {
    String? id;
    String? name;

    Lo({
        this.id,
        this.name,
    });

    factory Lo.fromJson(Map<String, dynamic> json) => Lo(
        id: json["id"],
        name: json["name"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
    };

    Lo copyWith({
        String? id,
        String? name,
    }) => 
        Lo(
            id: id ?? this.id,
            name: name ?? this.name,
        );
}
