import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';

class GlobalLibraryAccordionFlow extends StatefulWidget {
  const GlobalLibraryAccordionFlow({super.key});

  @override
  State<GlobalLibraryAccordionFlow> createState() =>
      _GlobalLibraryAccordionFlowState();
}

class _GlobalLibraryAccordionFlowState
    extends State<GlobalLibraryAccordionFlow> {
  late AccordionController _accordionController;
  String? _selectedIndustry;
  final TextEditingController _searchController = TextEditingController();
  List<AccordionItem> _filteredAccordionItems = [];
  String _selectedTab = 'Books'; // Added this line for navigation tab tracking

  final List<String> _industries = [
    'Select Industry',
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Manufacturing',
    'Retail',
    'Real Estate'
  ];

  // Navigation tabs
  final List<String> _navigationTabs = [
    'Books',
    'Solutions',
    'Objects',
    'Agents'
  ];

  final List<AccordionItem> _accordionItems = [
    AccordionItem(
      id: 'employee',
      title: 'Employee',
      subtitle:
          'Employee is in Sale, Finance and Development Department, inherits none, reports to Manager',
      children: ['Employee Details', 'Department Info', 'Reporting Structure'],
    ),
    AccordionItem(
      id: 'manager',
      title: 'Manager',
      subtitle: null,
      children: [
        'Manager Responsibilities',
        'Team Management',
        'Performance Reviews'
      ],
    ),
    AccordionItem(
      id: 'senior_manager',
      title: 'Senior Manager',
      subtitle: null,
      children: [
        'Strategic Planning',
        'Budget Management',
        'Leadership Development'
      ],
    ),
    AccordionItem(
      id: 'go_name_1',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 1', 'Configuration 1', 'Settings 1'],
    ),
    AccordionItem(
      id: 'go_name_2',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 2', 'Configuration 2', 'Settings 2'],
    ),
    AccordionItem(
      id: 'go_name_3',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 3', 'Configuration 3', 'Settings 3'],
    ),
    AccordionItem(
      id: 'go_name_4',
      title: 'GO Name',
      subtitle: null,
      children: ['GO Details 4', 'Configuration 4', 'Settings 4'],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _selectedIndustry = _industries.first;
    _filteredAccordionItems = _accordionItems;
    _searchController.addListener(_filterAccordionItems);
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  void _filterAccordionItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredAccordionItems = _accordionItems;
      } else {
        _filteredAccordionItems = _accordionItems.where((item) {
          return item.title.toLowerCase().contains(query) ||
              (item.subtitle?.toLowerCase().contains(query) ?? false);
        }).toList();
      }
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SizedBox(
        // width: 534,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Navigation bar with icon and tabs in one line
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  // Logout/Exit Icon on the left
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          // Handle hide/collapse by toggling showGlobalLibrary
                          final provider = Provider.of<WebHomeProviderStatic>(
                              context,
                              listen: false);
                          provider.showGlobalLibrary = false;
                        },
                        child: SvgPicture.asset(
                          'assets/images/expand-arrow-left-new.svg',
                          width: 20,
                          height: 20,
                          colorFilter: const ColorFilter.mode(
                            Colors.black,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Navigation tabs in the center
                  Expanded(
                    child: _buildNavigationTabs(),
                  ),
                ],
              ),
            ),

            // Tab Content
            Expanded(
              child: _getTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopRow() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Row(
        children: [
          // Select Industry Dropdown
          Expanded(
            flex: 2,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedIndustry,
                  isExpanded: true,
                  icon: Icon(Icons.keyboard_arrow_down,
                      color: Colors.grey.shade600),
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey.shade700,
                  ),
                  items: _industries.map((String industry) {
                    return DropdownMenuItem<String>(
                      value: industry,
                      child: Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                        child: Text(
                          industry,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedIndustry = newValue;
                    });
                  },
                ),
              ),
            ),
          ),

          SizedBox(width: AppSpacing.xs),

          // Search Field
          Expanded(
            flex: 2,
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextField(
                controller: _searchController,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey.shade500,
                  ),
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: AppSpacing.xs,
                  ),
                  suffixIcon: Icon(
                    Icons.search,
                    color: Colors.grey.shade500,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationTabs() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment
              .center, // Center the tabs within the available space
          children: _navigationTabs.map((tab) {
            bool isSelected = tab == _selectedTab;
            return MouseRegion(
              cursor: SystemMouseCursors.click, // Add cursor pointer for web
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTab = tab;
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Text(
                    tab,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontWeight:
                          isSelected ? FontWeight.w500 : FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: isSelected ? Colors.black : Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  // Method to return content based on selected tab
  Widget _getTabContent() {
    // Return different content based on the selected tab
    switch (_selectedTab) {
      case 'Books':
        return _buildBooksTabContent();
      case 'Solutions':
        return _buildSolutionsTabContent();
      case 'Objects':
        return _buildObjectsTabContent();
      case 'Agents':
        return _buildAgentsTabContent();
      default:
        return _buildBooksTabContent();
    }
  }

  // Books tab content
  Widget _buildBooksTabContent() {
    // Define a list of book accordion items
    final List<AccordionItem> bookItems = [
      AccordionItem(
        id: 'book_main',
        title: 'Books & References Library',
        subtitle:
            'Browse through our comprehensive collection of books, publications, and references materials for your business needs',
        children: [
          'Business Fiction Books',
          'Non-Fiction & Technical Publications',
          'Educational & Training Materials',
          'Business Reference Guides & Handbooks'
        ],
      ),
      // Duplicated accordion item
      AccordionItem(
        id: 'book_technical',
        title: 'Technical Documentation Library',
        subtitle:
            'Access our technical documentation, manuals, and guides for implementing business solutions',
        children: [
          'API Documentation',
          'Technical Manuals',
          'Implementation Guides',
          'System Architecture References'
        ],
      ),
    ];

    return Column(
      children: [
        _buildTopRow(), // Top row with search and industry dropdown
        // Use flexible with scroll view to accommodate multiple items
        Flexible(
          child: SingleChildScrollView(
            child: Column(
              children: bookItems
                  .map((item) => Padding(
                        padding: EdgeInsets.symmetric(vertical: 4),
                        child: _buildBooksAccordionItem(item),
                      ))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  // Solutions tab content
  Widget _buildSolutionsTabContent() {
    // Define a list of solution accordion items - only 2 items
    final List<AccordionItem> solutionItems = [
      AccordionItem(
        id: 'solution_enterprise',
        title: 'Enterprise Solutions',
        subtitle:
            'Enterprise-grade solutions for large organizations with complex requirements and multi-department needs',
        children: [
          'Enterprise Resource Planning',
          'Customer Relationship Management',
          'Business Intelligence & Analytics',
          'Supply Chain Management'
        ],
      ),
      AccordionItem(
        id: 'solution_smb',
        title: 'Small Business Solutions',
        subtitle:
            'Tailored solutions for small and medium businesses with cost-effective implementation and faster deployment',
        children: [
          'Accounting & Finance',
          'Inventory Management',
          'Point of Sale Systems',
          'Customer Management'
        ],
      ),
    ];

    return Column(
      children: [
        _buildTopRow(),

        // Accordion Items for Solutions - use solutionItems instead of _filteredAccordionItems
        Flexible(
          child: Container(
            constraints: BoxConstraints(maxHeight: 400),
            child: SingleChildScrollView(
              child: Column(
                children: solutionItems
                    .map((item) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: _buildSolutionsAccordionItem(item),
                        ))
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Objects tab content
  Widget _buildObjectsTabContent() {
    return Column(
      children: [
        _buildTopRow(),

        // Accordion Items for Objects
        Flexible(
          child: Container(
            constraints: BoxConstraints(maxHeight: 400),
            child: SingleChildScrollView(
              child: Column(
                children: _filteredAccordionItems
                    .map((item) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: _buildObjectsAccordionItem(item),
                        ))
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Agents tab content
  Widget _buildAgentsTabContent() {
    return Column(
      children: [
        _buildTopRow(),

        // Accordion Items for Agents
        Flexible(
          child: Container(
            constraints: BoxConstraints(maxHeight: 400),
            child: SingleChildScrollView(
              child: Column(
                children: _filteredAccordionItems
                    .map((item) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: _buildAgentsAccordionItem(item),
                        ))
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Books accordion style
  Widget _buildBooksAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);
    return Container(
      margin: EdgeInsets.only(top: 2, bottom: 2, left: 10, right: 10),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xff707070), width: .5),
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight:
                            isExpanded ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),

                  // Plus icon
                  Icon(
                    Icons.add,
                    color: Colors.black,
                    size: 18,
                  ),

                  SizedBox(width: 8),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xff707070), width: .5),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children
                        .map(
                          (child) => Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 6),
                            child: Row(
                              children: [
                                SizedBox(width: AppSpacing.xs),
                                Text(
                                  child,
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.titleMedium(
                                        context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Solutions accordion style
  Widget _buildSolutionsAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);
    return Container(
      margin: EdgeInsets.only(top: 2, bottom: 2, left: 10, right: 10),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xff707070), width: .5),
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight:
                            isExpanded ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  // Plus icon
                  Icon(
                    Icons.add,
                    color: Colors.black,
                    size: 18,
                  ),

                  SizedBox(width: 8),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xff707070), width: .5),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children
                        .map(
                          (child) => Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 6),
                            child: Row(
                              children: [
                                SizedBox(width: AppSpacing.xs),
                                Text(
                                  child,
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.titleMedium(
                                        context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Objects accordion style
  Widget _buildObjectsAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);
    return Container(
      margin: EdgeInsets.only(top: 2, bottom: 2, left: 10, right: 10),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xff707070), width: .5),
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight:
                            isExpanded ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  // Plus icon
                  Icon(
                    Icons.add,
                    color: Colors.black,
                    size: 18,
                  ),

                  SizedBox(width: 8),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xff707070), width: .5),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children
                        .map(
                          (child) => Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 6),
                            child: Row(
                              children: [
                                SizedBox(width: AppSpacing.xs),
                                Text(
                                  child,
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.titleMedium(
                                        context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Agents accordion style
  Widget _buildAgentsAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);
    return Container(
      margin: EdgeInsets.only(top: 2, bottom: 2, left: 10, right: 10),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xff707070), width: .5),
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight:
                            isExpanded ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  // Plus icon
                  Icon(
                    Icons.add,
                    color: Colors.black,
                    size: 18,
                  ),

                  SizedBox(width: 8),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xff707070), width: .5),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children
                        .map(
                          (child) => Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 6),
                            child: Row(
                              children: [
                                SizedBox(width: AppSpacing.xs),
                                Text(
                                  child,
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.titleMedium(
                                        context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}
