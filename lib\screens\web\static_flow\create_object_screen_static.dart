import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class CreateObjectScreenStatic extends StatefulWidget {
  const CreateObjectScreenStatic({super.key});

  @override
  State<CreateObjectScreenStatic> createState() =>
      _CreateObjectScreenStaticState();
}

class _CreateObjectScreenStaticState extends State<CreateObjectScreenStatic> {
  late TextEditingController chatController;
  late MultimediaService _multimediaService;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    chatController = TextEditingController();
    _multimediaService = MultimediaService();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (chatController.text.trim().isNotEmpty) {
      // Handle message sending logic here
      Logger.info('Sending message: ${chatController.text}');
      // You can add your API call logic here
      chatController.clear();
      Provider.of<WebHomeProvider>(context, listen: false)
          .currentScreenIndex =
       ScreenConstants.aiObjectScreenStatic;

    }
  }

  void _cancelRequest() {
    // Handle cancel request logic here
    setState(() {
      isLoading = false;
    });
  }

  void _handleFileSelection(String fileName, String filePath) {
    // Handle file selection logic here
    Logger.info('File selected: $fileName at $filePath');
  }

  void _toggleRecording() {
    // Handle recording toggle logic here
    Logger.info('Toggle recording');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //backgroundColor: Color(0xfff6f6f6),
      body: Column(
        children: [
          // Top navigation bar
        if(  Provider.of<WebHomeProvider>(context, listen: false)
          .currentScreenIndex !=
       ScreenConstants.aiObjectScreenStatic) _buildTopNavigation(context),
          // Main content area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffF7F9FB),
              ),
       padding: Provider.of<WebHomeProvider>(context, listen: false)
            .currentScreenIndex != ScreenConstants.aiObjectScreenStatic
        ? const EdgeInsets.symmetric(horizontal: 94)
        : const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Spacer to push content down
                  const Spacer(),

                  // Text above chat field
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: Text(
                      'I will help you to create your entity. Please tell me what entity you want to create?',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: "TiemposText",
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.start,
                    ),
                  ),

                  // Chat field at the bottom
                  ChatField(
                    isGeneralLoading: isLoading,
                    isFileLoading: false,
                    isSpeechLoading: false,
                    onSendMessage: _sendMessage,
                    onCancelRequest: _cancelRequest,
                    onFileSelected: _handleFileSelection,
                    onToggleRecording: _toggleRecording,
                    controller: chatController,
                    multimediaService: _multimediaService,
                    height: 56,
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopNavigation(BuildContext context) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: const [
      Center(
        child: HoverNavItems(),
      ),
    ],
  );
}

}