import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/ai_validation_popup.dart';

class AiObjectRightPanel extends StatefulWidget {
  const AiObjectRightPanel({super.key});

  @override
  State<AiObjectRightPanel> createState() => _AiObjectRightPanelState();
}

class _AiObjectRightPanelState extends State<AiObjectRightPanel> {
  int currentPage = 1;
  int itemsPerPage = 5;

  // Pagination for Industry Bundles
  int currentBundlePage = 1;
  int bundleItemsPerPage = 2;

  // Sample data for validation rules
  final List<Map<String, String>> validationRules = [
    {
      'title': 'Email Validation Rules',
      'description': 'Add REGEX EMAIL and IS_UNIQUE operators for email field',
      'category': 'Business Rules',
      'complexity': 'SIMPLE',
    },
    {
      'title': 'Phone Format Validation',
      'description':
          'Apply MATCHES_PATTERN operator for international phone numbers',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Customer-Address Relationship',
      'description':
          'Configure one-to-many with CASCADE delete for address cleanup',
      'category': 'Entity Relationship',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Title of the Issue',
      'description': 'Description of the Issue',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Address Auto-Complete',
      'description': 'Implement auto-complete for the field',
      'category': 'Entity Relationship',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Title of the Issue',
      'description': 'Description of the Issue',
      'category': 'Business Rules',
      'complexity': 'MODERATE',
    },
    {
      'title': 'Data Validation Rules',
      'description': 'Additional validation for data integrity',
      'category': 'Business Rules',
      'complexity': 'SIMPLE',
    },
    {
      'title': 'Security Validation',
      'description': 'Implement security checks for sensitive data',
      'category': 'Security',
      'complexity': 'COMPLEX',
    },
  ];

  // Sample data for industry bundles
  final List<Map<String, String>> industryBundles = [
    {
      'title': 'E-Commerce Complete',
      'match': '92% match',
      'modules': '23 resolutions',
      'description':
          'Full e-commerce customer entity with privacy, UX, and business intelligence',
    },
    {
      'title': 'Security First',
      'match': '92% match',
      'modules': '23 resolutions',
      'description': 'Enhanced security and compliance for sensitive data',
    },
    {
      'title': 'Healthcare Bundle',
      'match': '88% match',
      'modules': '18 resolutions',
      'description':
          'HIPAA compliant healthcare data management with patient privacy controls',
    },
    {
      'title': 'Financial Services',
      'match': '95% match',
      'modules': '31 resolutions',
      'description':
          'Banking and financial compliance with fraud detection and audit trails',
    },
  ];

  int get totalPages => (validationRules.length / itemsPerPage).ceil();
  int get totalBundlePages =>
      (industryBundles.length / bundleItemsPerPage).ceil();

  List<Map<String, String>> get currentPageItems {
    final startIndex = (currentPage - 1) * itemsPerPage;
    final endIndex =
        (startIndex + itemsPerPage).clamp(0, validationRules.length);
    return validationRules.sublist(startIndex, endIndex);
  }

  List<Map<String, String>> get currentBundlePageItems {
    final startIndex = (currentBundlePage - 1) * bundleItemsPerPage;
    final endIndex =
        (startIndex + bundleItemsPerPage).clamp(0, industryBundles.length);
    return industryBundles.sublist(startIndex, endIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      height: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section 1: Validation Rules with Header + Body
          Expanded(child: _buildValidationRulesSection()),
          const SizedBox(height: 10),
          // Section 2: Industry Bundles with Header + Body
          Expanded(child: _buildIndustryBundlesSection()),
        ],
      ),
    );
  }

  // Reusable header widget for both sections
  Widget _buildSectionHeader({
    required BuildContext context,
    required String title,
    IconData? icon, // Made optional
    required List<Color> gradientColors,
    Widget? actionButton,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: gradientColors),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
          ],
          if (icon == null && actionButton == null)
            // Center the text when no icon and no action button
            Expanded(
              child: Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontManager.semiBold,
                  color: Colors.white,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            )
          else
            Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontManager.semiBold,
                color: Colors.white,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
          if (icon != null || actionButton != null) const Spacer(),
          if (actionButton != null) actionButton,
        ],
      ),
    );
  }

  Widget _buildValidationRulesSection() {
    return Builder(
      builder: (context) => Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1 Header
            _buildSectionHeader(
              context: context,
              title: 'AI Smart Resolution (${validationRules.length})',
              icon: Icons.auto_awesome,
              gradientColors: const [Color(0xff0058FF), Color(0xff0B3A91)],
              actionButton: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'BULK APPLY',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontManager.bold,
                    color: const Color(0xFF0058FF),
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ),
            ),
            // Section 1 Body (Expanded)
            Expanded(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // ListView for rule items
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: ListView.builder(
                          itemCount: currentPageItems.length,
                          itemBuilder: (context, index) {
                            final item = currentPageItems[index];
                            return Padding(
                              padding: EdgeInsets.only(
                                bottom:
                                    index < currentPageItems.length - 1 ? 4 : 0,
                              ),
                              child: _buildValidationRuleItem(
                                item['title']!,
                                item['description']!,
                                item['category']!,
                                item['complexity']!,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    // Pagination at bottom right
                    if (totalPages > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _buildPaginationControls(),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationRuleItem(
    String title,
    String description,
    String category,
    String complexity,
  ) {
    return Builder(
      builder: (context) => InkWell(
        onTap: () {
          // Show AiValidationPopup when clicked
          showDialog(
            context: context,
            barrierDismissible: true,
            builder: (context) => const AiValidationPopup(),
          );
        },
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              left: BorderSide(
                color: Colors.blue, // Blue vertical line
                width: 2,
              ),
              top: BorderSide(
                color: Colors.grey.shade300,
                width: 1,
              ),
              right: BorderSide(
                color: Colors.grey.shade300,
                width: 1,
              ),
              bottom: BorderSide(
                color: Colors.grey.shade300,
                width: 1,
              ),
            ),
            // borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // First row - Title and Category
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                title,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.titleSmall(context),
                                  fontWeight: FontManager.semiBold,
                                  color: Colors.black,
                                  fontFamily: FontManager.fontFamilyInter,
                                ),
                              ),
                            ),
                            Text(
                              category,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontManager.regular,
                                color: const Color(0xFF6B7280),
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        // Second row - Description
                        Text(
                          description,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontManager.regular,
                            color: const Color(0xFF6B7280),
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 28),
                  // Complexity badge on the right side
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getComplexityColor(complexity),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      complexity,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontManager.semiBold,
                        color: _getComplexityTextColor(complexity),
                        fontFamily: FontManager.fontFamilyInter,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndustryBundlesSection() {
    return Builder(
      builder: (context) => Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 2 Header
            _buildSectionHeader(
              context: context,
              title: 'Industry Bundles',
              // No icon for Industry Bundles section
              gradientColors: const [Color(0xff835BED), Color(0xff7540E5)],
            ),
            // Section 2 Body (Expanded)
            Expanded(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // ListView for bundle items
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: ListView.builder(
                          itemCount: currentBundlePageItems.length,
                          itemBuilder: (context, index) {
                            final item = currentBundlePageItems[index];
                            return Padding(
                              padding: EdgeInsets.only(
                                bottom:
                                    index < currentBundlePageItems.length - 1
                                        ? 4
                                        : 0,
                              ),
                              child: _buildIndustryBundleItem(
                                item['title']!,
                                item['match']!,
                                item['modules']!,
                                item['description']!,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    // Pagination at bottom right
                    if (totalBundlePages > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _buildBundlePaginationControls(),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndustryBundleItem(
    String title,
    String match,
    String modules,
    String description,
  ) {
    return Builder(
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(
              color: Color(0xff7846E7), // Blue vertical line
              width: 2,
            ),
            top: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            right: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            bottom: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
                Text(
                  match,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontManager.regular,
                    color: const Color(0xFF6B7280),
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              modules,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontManager.regular,
                color: const Color(0xFF6B7280),
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontManager.regular,
                color: const Color(0xFF6B7280),
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0058FF),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'APPLY ALL',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.bold,
                      color: Colors.white,
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border:
                        Border.all(color: const Color(0xFFE5E5E5), width: 1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'VIEW',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.bold,
                      color: const Color(0xFF6B7280),
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }

  Color _getComplexityTextColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFF2D5A2D);
      case 'MODERATE':
        return const Color(0xFF8B4513);
      case 'COMPLEX':
        return const Color(0xFF8B0000);
      default:
        return const Color(0xFF666666);
    }
  }

  Widget _buildPaginationControls() {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Previous button
          InkWell(
            onTap: currentPage > 1
                ? () {
                    setState(() {
                      currentPage--;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_left,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
          // Next button
          InkWell(
            onTap: currentPage < totalPages
                ? () {
                    setState(() {
                      currentPage++;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_right,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBundlePaginationControls() {
    return Builder(
      builder: (context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Previous button
          InkWell(
            onTap: currentBundlePage > 1
                ? () {
                    setState(() {
                      currentBundlePage--;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_left,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
          // Next button
          InkWell(
            onTap: currentBundlePage < totalBundlePages
                ? () {
                    setState(() {
                      currentBundlePage++;
                    });
                  }
                : null,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Icon(
                Icons.chevron_right,
                size: 24,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
