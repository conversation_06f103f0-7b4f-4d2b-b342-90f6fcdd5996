// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:nsl/models/workflow/workflow_manual_response_model.dart';
// import 'package:nsl/providers/manual_creation_provider.dart';
// import 'package:provider/provider.dart';


// // Individual node widget
// class NodeWidget {
//   final String text;
//   final NodeType nodeType;
//   final double leftPadding;
//   final bool isLast;

//   NodeWidget({
//     required this.text,
//     required this.nodeType,
//     this.leftPadding = 0,
//     this.isLast = false,
//   });
// }

// enum NodeType {
//   root, // SubmitLeaveRequest
//   sequential, // NotifyEmployee, UpdateCalendar, etc.
//   parallel, // Same as sequential but in parallel context
//   alternate, // Same as sequential but in alternate context
//   prefixedNode, // Alt1. UploadDocumentation, Parl1. UpdateCalendar
//   terminal, // X
//   arrow, // ->
// }

// // Global variables for tracking
// Set<String> expandedNodeNames = {};
// Map<String, List<String>> nodeOccurrences = {};

// // Global colors for parallel nodes
// List<Color> colors = [
//   Color(0xFFE3F2FD),
//   Color(0xffFFFDD0),
//   Color(0xffF5F5DC),
//   Color(0xffAAF0D1),
//   Color(0xffAAF0D1),
//   Color(0xffF0FFF0),
//   Color(0xffB0EACD),
//   Color(0xffACE1AF),
//   Color(0xffD0F0C0),
//   Color(0xffADDFAD),
//   Color(0xffA8E4A0),
//   Color(0xffD5F5B8),
//   Color(0xffCDE5B2),
//   Color(0xffECEBBD),
//   Color(0xffB2FFFF),
//   Color(0xffAFEEEE),
//   Color(0xffF5FFFA),
//   Color(0xffA7D8C9),
//   Color(0xff7FFFD4),
//   Color(0xffCFFFE5),
//   Color(0xffD4F1F9),
//   Color(0xffD6F1FF),
//   Color(0xffAEC6CF),
// ];

// // Track parallel group colors by parent node
// Map<String, Color> parallelGroupColors = {};
// int parallelColorIndex = 0;
// // List colors = ManualCreationProvider.pastelColors;

// // Track which parent nodes have parallel children
// Map<String, String> parallelParentMap = {};

// // Track duplicate node colors for link icons
// Map<String, Color> duplicateNodeColors = {};
// int duplicateColorIndex = 0;

// // Track actual occurrences in rendered tree (not just possible paths)
// Map<String, int> renderedNodeCount = {};

// // Track nodes with same names for colored containers
// Map<String, Color> sameNameNodeColors = {};
// Map<String, List<String>> sameNameGroups = {};
// int sameNameColorIndex = 0;

// // Separate color palette for link icons to avoid overlap with parallel colors
// List<Color> linkIconColors = [
//   // Color(0xffFFA07A),
//   Color(0xffDFFF00),
//   Color(0xffE0B0FF),
//   Color(0xffB4A7D6),
//   Color(0xffC7E9F1),
//   Color(0xffC3CDE6),
//   Color(0xffCCCCFF),
//   Color(0xffE6E6FA),
//   Color(0xffE4D0EC),
//   Color(0xffD8BFD8),
//   Color(0xffB0E0E6),
//   Color(0xffF2D1E3),
//   Color(0xffF49AC2),
//   Color(0xffFFA6C9),
//   Color(0xffFBB1BD),
//   Color(0xffFFD1DC),
//   Color(0xffFFDFDD),
//   Color(0xffFFDAB9),
//   Color(0xffFDC5B5),
//   Color(0xffD8B7DD),
// ];

// class ProcessFlowWidget extends StatelessWidget {
//   final List<ProcessFlow> processList;

//   const ProcessFlowWidget({super.key, required this.processList});

//   @override
//   Widget build(BuildContext context) {
//     final treeStructure = buildTreeStructure(processList);

//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       child: SingleChildScrollView(
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: treeStructure
//                 .map((row) => buildRowWidget(context, row))
//                 .toList(),
//           ),
//         ),
//       ),
//     );
//   }

//   List<List<NodeWidget>> buildTreeStructure(List<ProcessFlow> processList) {
//     // Reset global variables
//     expandedNodeNames.clear();
//     nodeOccurrences.clear();
//     parallelParentMap.clear();
//     parallelGroupColors.clear();
//     parallelColorIndex = 0;
//     renderedNodeCount.clear();
//     sameNameNodeColors.clear();
//     sameNameGroups.clear();
//     sameNameColorIndex = 0;

//     final nodeMapById = <String, ProcessFlow>{};
//     final Set<String> nextNodeIds = {};

//     for (final node in processList) {
//       nodeMapById[node.loId!] = node;

//       if (node.conditions != null) {
//         for (final condition in node.conditions!) {
//           if (condition.routeTo != "Terminal") {
//             nextNodeIds.add(condition.routeTo!);
//           }
//         }
//       }

//       if (node.routes != null) {
//         for (final route in node.routes!) {
//           if (route != "Terminal") {
//             nextNodeIds.add(route);
//           }
//         }
//       }

//       if (node.parallelRoutes != null) {
//         for (final pr in node.parallelRoutes!) {
//           if (pr.routeTo != "Terminal") {
//             nextNodeIds.add(pr.routeTo!);
//           }
//         }
//       }

//       if (node.joinAt != null && node.joinAt != "Terminal") {
//         nextNodeIds.add(node.joinAt!);
//       }
//     }

//     // Pre-process parallel relationships
//     for (final node in processList) {
//       if (node.routeType == "Parallel" && node.parallelRoutes != null) {
//         final parentNodeName = node.loName!;
//         int parCount = 1;
//         for (final pr in node.parallelRoutes!) {
//           if (pr.routeTo != "Terminal") {
//             parallelParentMap['Parl$parCount'] = parentNodeName;
//             parCount++;
//           }
//         }

//         // Assign color to this parallel group immediately
//         if (!parallelGroupColors.containsKey(parentNodeName)) {
//           parallelGroupColors[parentNodeName] =
//               colors[parallelColorIndex % colors.length];
//           parallelColorIndex++;
//         }
//       }
//     }

//     final rootNodes =
//         processList.where((node) => !nextNodeIds.contains(node.loId)).toList();

//     // First pass: collect all occurrences
//     for (final root in rootNodes) {
//       collectOccurrences(root, nodeMapById, []);
//     }

//     // Identify same-name groups and assign colors
//     _identifySameNameGroups();

//     // Second pass: build tree structure
//     List<List<NodeWidget>> result = [];
//     for (final root in rootNodes) {
//       final rows = buildNodeTreeStructure(root, 0, nodeMapById, [], '');
//       result.addAll(rows);
//     }

//     return result;
//   }

//   void collectOccurrences(
//     ProcessFlow node,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPath,
//   ) {
//     final nodeId = node.loId!;
//     final nodeName = node.loName!;

//     if (currentPath.contains(nodeId)) {
//       return;
//     }

//     if (!nodeOccurrences.containsKey(nodeName)) {
//       nodeOccurrences[nodeName] = [];
//     }
//     final pathKey = '${currentPath.join('/')}/$nodeId';
//     nodeOccurrences[nodeName]!.add(pathKey);

//     currentPath.add(nodeId);

//     if (node.routeType == "Sequential" && node.routes != null) {
//       for (final next in node.routes!) {
//         if (next != "Terminal" && nodeMap.containsKey(next)) {
//           collectOccurrences(nodeMap[next]!, nodeMap, List.from(currentPath));
//         }
//       }
//     } else if (node.routeType == "Alternate" && node.conditions != null) {
//       for (final cond in node.conditions!) {
//         if (cond.routeTo != "Terminal" && nodeMap.containsKey(cond.routeTo)) {
//           collectOccurrences(
//               nodeMap[cond.routeTo]!, nodeMap, List.from(currentPath));
//         }
//       }
//     } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
//       for (final pr in node.parallelRoutes!) {
//         if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
//           collectOccurrences(
//               nodeMap[pr.routeTo]!, nodeMap, List.from(currentPath));
//         }
//       }

//       if (node.joinAt != null && node.joinAt != "Terminal") {
//         if (nodeMap.containsKey(node.joinAt)) {
//           collectOccurrences(
//               nodeMap[node.joinAt]!, nodeMap, List.from(currentPath));
//         }
//       }
//     }

//     currentPath.removeLast();
//   }

//   // Helper method to track rendered nodes
//   void incrementRenderedNodeCount(String nodeName) {
//     renderedNodeCount[nodeName] = (renderedNodeCount[nodeName] ?? 0) + 1;
//   }

//   // Method to identify same-name groups and assign colors
//   void _identifySameNameGroups() {
//     // Find nodes that appear multiple times
//     for (String nodeName in nodeOccurrences.keys) {
//       List<String> occurrences = nodeOccurrences[nodeName]!;
//       if (occurrences.length > 1) {
//         // This node appears multiple times, add to same-name groups
//         if (!sameNameGroups.containsKey(nodeName)) {
//           sameNameGroups[nodeName] = [];
//         }
//         sameNameGroups[nodeName]!.addAll(occurrences);
        
//         // Assign a color to this same-name group
//         if (!sameNameNodeColors.containsKey(nodeName)) {
//           sameNameNodeColors[nodeName] = 
//               colors[sameNameColorIndex % colors.length];
//           sameNameColorIndex++;
//         }
//       }
//     }
//   }

//   // Dynamic method to check if a node is a duplicate based on its actual rendering context
//   bool _isDynamicDuplicate(String nodeName, List<String> currentTreePath) {

//     // Get total possible occurrences from path analysis
//     int totalPossibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;
//     bool hasMultiplePossiblePaths = totalPossibleOccurrences > 1;
//     bool isLastOccurrenceInPath = isLastOccurrence(nodeName, currentTreePath);

//     return hasMultiplePossiblePaths && !isLastOccurrenceInPath;
//   }

//   // Helper method to extract clean node name from display text
//   String _extractNodeName(String text) {
//     String nodeName = text;

//     // Remove prefixes like "Alt1. " or "Parl1. "
//     if (text.contains('. ')) {
//       nodeName = text.split('. ').last;
//     }

//     // Remove % prefix if present
//     if (nodeName.startsWith('%')) {
//       nodeName = nodeName.substring(1);
//     }

//     // Remove % suffix if present
//     if (nodeName.endsWith('%')) {
//       nodeName = nodeName.substring(0, nodeName.length - 1);
//     }

//     return nodeName;
//   }

//   // Dynamic method to check if a node is duplicate based on rendered count
//   bool _isDuplicateFromRenderedCount(String nodeName) {
//     // A node is considered duplicate if it has been rendered more than once
//     int possibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;

//     return possibleOccurrences > 1;
//   }

//   // Dynamic method to check if a node is expanding from context
//   bool _isExpandingFromContext(String displayText, String nodeName) {
//     // If the text has % prefix, it means this is an expanding duplicate
//     if (displayText.startsWith('%')) {
//       return true;
//     }

//     // If the text has % suffix, it means this is a non-expanding duplicate
//     if (displayText.endsWith('%')) {
//       return false;
//     }

//     // For nodes without % markers, check if they are in expandedNodeNames
//     return expandedNodeNames.contains(nodeName);
//   }

//   List<NodeWidget> parseSequentialChain(
//       String chainString, double basePadding, String prefix) {
//     List<NodeWidget> nodes = [];

//     // Remove prefix from chain string if it exists
//     String cleanChain = chainString;
//     if (prefix.isNotEmpty && chainString.startsWith(prefix)) {
//       cleanChain = chainString.substring(prefix.length).trim();
//     }

//     // Split the chain by " -> "
//     final parts = cleanChain.split(' -> ');

//     for (int i = 0; i < parts.length; i++) {
//       final part = parts[i].trim();

//       if (part.isEmpty) continue;

//       if (part == 'X') {
//         nodes.add(NodeWidget(
//           text: 'X',
//           nodeType: NodeType.terminal,
//           leftPadding: 0,
//         ));
//       } else {
//         // Determine node type and clean text
//         String cleanText = part;
//         NodeType nodeType = NodeType.sequential;

//         if (part.startsWith('%')) {
//           cleanText = part.substring(1);
//         }

//         // Combine prefix with first node if this is the first node and prefix exists
//         String displayText = cleanText;
//         if (i == 0 && prefix.isNotEmpty) {
//           displayText = prefix.trim() + cleanText;
//           nodeType = NodeType.prefixedNode;
//         }

//         // Track rendered node count
//         String nodeName = cleanText;
//         if (nodeName.endsWith('%')) {
//           nodeName = nodeName.substring(0, nodeName.length - 1);
//         }
//         incrementRenderedNodeCount(nodeName);

//         nodes.add(NodeWidget(
//           text: displayText,
//           nodeType: nodeType,
//           leftPadding: i == 0 ? basePadding : 0,
//         ));
//       }
//     }

//     return nodes;
//   }

//   String buildSequentialChainString(
//     ProcessFlow node,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPathVisited,
//     List<ProcessFlow> chainNodes,
//     bool isFirstExpansionOfHead,
//     List<String> currentTreePath,
//   ) {
//     final nodeName = node.loName!;

//     if (currentPathVisited.contains(nodeName)) {
//       return '$nodeName (cycle)';
//     }

//     currentPathVisited.add(nodeName);
//     chainNodes.add(node);

//     // Dynamic duplicate detection
//     bool isDuplicate = _isDynamicDuplicate(nodeName, currentTreePath);
//     bool isLastOccurrenceOfNode = isLastOccurrence(nodeName, currentTreePath);
//     bool isExpandingNode = expandedNodeNames.contains(nodeName);

//     String nodeDisplay = nodeName;
//     if (isDuplicate && !isLastOccurrenceOfNode) {
//       if (isExpandingNode) {
//         // Expanding duplicate nodes: % as prefix
//         nodeDisplay = '%$nodeName';
//       } else {
//         // Non-expanding duplicate nodes: % as suffix
//         nodeDisplay = '$nodeName%';
//       }
//     }

//     // Check if node is terminal (no routes and no conditions)
//     bool isTerminalNode = (node.routes == null || node.routes!.isEmpty) &&
//         (node.conditions == null || node.conditions!.isEmpty) &&
//         (node.parallelRoutes == null || node.parallelRoutes!.isEmpty);

//     if (isTerminalNode) {
//       return '$nodeDisplay -> X';
//     }

//     if (node.routeType == "Sequential" &&
//         node.routes != null &&
//         node.routes!.isNotEmpty) {
//       final nextRoute = node.routes!.first;
//       if (nextRoute == "Terminal") {
//         return '$nodeDisplay -> X';
//       } else if (nodeMap.containsKey(nextRoute)) {
//         final nextChainString = buildSequentialChainString(
//           nodeMap[nextRoute]!,
//           nodeMap,
//           currentPathVisited,
//           chainNodes,
//           isFirstExpansionOfHead,
//           currentTreePath,
//         );
//         return '$nodeDisplay -> $nextChainString';
//       }
//     } else if (node.routeType == "Parallel" &&
//         node.joinAt != null &&
//         node.joinAt != "Terminal") {
//       bool isLastOcc = isLastOccurrence(nodeName, currentTreePath);
//       if (isLastOcc && nodeMap.containsKey(node.joinAt)) {
//         final joinChainString = buildSequentialChainString(
//           nodeMap[node.joinAt]!,
//           nodeMap,
//           currentPathVisited,
//           chainNodes,
//           isFirstExpansionOfHead,
//           currentTreePath,
//         );
//         return '$nodeDisplay -> $joinChainString';
//       }
//     }

//     return nodeDisplay;
//   }

//   bool isLastOccurrence(String nodeName, List<String> currentPath) {
//     if (!nodeOccurrences.containsKey(nodeName)) return true;

//     final pathKey = currentPath.join('/');
//     final occurrences = nodeOccurrences[nodeName]!;

//     // Check if current path matches the last occurrence
//     return occurrences.isNotEmpty && occurrences.last.contains(pathKey);
//   }

//   List<List<NodeWidget>> buildNodeTreeStructure(
//     ProcessFlow node,
//     double depth,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPathForCycleDetection,
//     String branchPrefix,
//   ) {
//     List<List<NodeWidget>> result = [];
//     final nodeId = node.loId!;
//     final nodeName = node.loName!;

//     if (currentPathForCycleDetection.contains(nodeId)) {
//       result.add([
//         NodeWidget(
//           text: '$branchPrefix$nodeName (cycle)',
//           nodeType: NodeType.sequential,
//           leftPadding: depth * 20,
//         )
//       ]);
//       return result;
//     }

//     bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);
//     currentPathForCycleDetection.add(nodeId);

//     if (node.routeType == "Sequential") {
//       final chainNodes = <ProcessFlow>[];
//       final chainVisitedNames = <String>[];
//       final chainString = buildSequentialChainString(
//         node,
//         nodeMap,
//         chainVisitedNames,
//         chainNodes,
//         isFirstExpansionOfNode,
//         currentPathForCycleDetection,
//       );

//       // Parse sequential chain into individual nodes
//       final rowNodes =
//           parseSequentialChain(chainString, depth * 20, branchPrefix);
//       if (rowNodes.isNotEmpty) {
//         result.add(rowNodes);
//       }

//       // Process children of sequential chains
//       if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
//         final lastNodeInChain = chainNodes.last;
//         final lastNodeName = lastNodeInChain.loName!;

//         bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
//             isLastOccurrence(lastNodeName, currentPathForCycleDetection);

//         if (shouldExpandLastNode) {
//           for (final chainNode in chainNodes) {
//             expandedNodeNames.add(chainNode.loName!);
//           }

//           final children = buildNodeChildrenStructure(lastNodeInChain, depth,
//               nodeMap, List.from(currentPathForCycleDetection), 0);
//           result.addAll(children);
//         }
//       } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
//         if (chainNodes.length >= 2) {
//           final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
//           final lastNonTerminalName = lastNonTerminalNode.loName!;

//           bool shouldExpandLastNonTerminal =
//               !expandedNodeNames.contains(lastNonTerminalName) &&
//                   isLastOccurrence(
//                       lastNonTerminalName, currentPathForCycleDetection);

//           if (shouldExpandLastNonTerminal) {
//             for (final chainNode in chainNodes) {
//               expandedNodeNames.add(chainNode.loName!);
//             }

//             final chainStringUpToNode = chainString.substring(
//                 0,
//                 chainString.indexOf(lastNonTerminalName) +
//                     lastNonTerminalName.length);
//             final nodePositionInString = chainStringUpToNode.length;

//             final children = buildNodeChildrenStructureWithAlignment(
//                 lastNonTerminalNode,
//                 depth,
//                 nodeMap,
//                 List.from(currentPathForCycleDetection),
//                 nodePositionInString.toDouble());
//             result.addAll(children);
//           }
//         } else if (chainNodes.isNotEmpty && isFirstExpansionOfNode) {
//           for (final chainNode in chainNodes) {
//             expandedNodeNames.add(chainNode.loName!);
//           }
//         }
//       }
//     } else {
//       String nodeDisplay = nodeName;
//       if (node.loName == "Terminal") {
//         nodeDisplay += ' X';
//       }

//       NodeType nodeType =
//           branchPrefix.isNotEmpty ? NodeType.prefixedNode : NodeType.sequential;
//       String displayText =
//           branchPrefix.isNotEmpty ? '$branchPrefix$nodeDisplay' : nodeDisplay;

//       // Dynamic duplicate detection
//       bool isDuplicate =
//           _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
//       bool isLastOccurrenceOfNode =
//           isLastOccurrence(nodeName, currentPathForCycleDetection);
//       bool isExpandingNode = expandedNodeNames.contains(nodeName);

//       String finalDisplayText = displayText;
//       if (isDuplicate && !isLastOccurrenceOfNode) {
//         if (isExpandingNode) {
//           // Expanding duplicate nodes: % as prefix
//           finalDisplayText = '%$displayText';
//         } else {
//           // Non-expanding duplicate nodes: % as suffix
//           finalDisplayText = '$displayText%';
//         }
//       }

//       // Track rendered node count
//       incrementRenderedNodeCount(nodeName);

//       result.add([
//         NodeWidget(
//           text: finalDisplayText,
//           nodeType: nodeType,
//           leftPadding: depth * 20,
//         )
//       ]);

//       if (isLastOccurrenceOfNode) {
//         expandedNodeNames.add(nodeName);
//         final children = buildNodeChildrenStructure(
//             node, depth, nodeMap, List.from(currentPathForCycleDetection), 0);
//         result.addAll(children);
//       } else {
//         expandedNodeNames.add(nodeName);
//       }
//     }

//     currentPathForCycleDetection.removeLast();
//     return result;
//   }

//   List<List<NodeWidget>> buildNodeChildrenStructure(
//     ProcessFlow node,
//     double depth,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPathForCycleDetection,
//     double alignmentOffset,
//   ) {
//     return buildNodeChildrenStructureWithAlignment(
//         node, depth, nodeMap, currentPathForCycleDetection, alignmentOffset);
//   }

//   List<List<NodeWidget>> buildNodeChildrenStructureWithAlignment(
//     ProcessFlow node,
//     double depth,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPathForCycleDetection,
//     double alignmentOffset,
//   ) {
//     List<List<NodeWidget>> children = [];

//     if (node.routeType == "Alternate" && node.conditions != null) {
//       int altCount = 1;
//       for (int i = 0; i < node.conditions!.length; i++) {
//         final cond = node.conditions![i];
//         bool isLastAlternate = (i == node.conditions!.length - 1);

//         if (cond.routeTo == "Terminal") {
//           children.add([
//             NodeWidget(
//               text: 'Alt$altCount. X',
//               nodeType: NodeType.prefixedNode,
//               leftPadding: (depth + 1) * 20 + alignmentOffset,
//               isLast: isLastAlternate,
//             ),
//           ]);
//         } else if (nodeMap.containsKey(cond.routeTo)) {
//           final childRows = buildNodeTreeStructureWithAlignment(
//             nodeMap[cond.routeTo]!,
//             depth + 1,
//             nodeMap,
//             List.from(currentPathForCycleDetection),
//             'Alt$altCount. ',
//             alignmentOffset,
//           );
//           // Update the first row of childRows to set isLast property
//           if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//             childRows[0][0] = NodeWidget(
//               text: childRows[0][0].text,
//               nodeType: childRows[0][0].nodeType,
//               leftPadding: childRows[0][0].leftPadding,
//               isLast: isLastAlternate,
//             );
//           }
//           children.addAll(childRows);
//         } else {
//           // If routeTo node doesn't exist, treat as terminal
//           children.add([
//             NodeWidget(
//               text: 'Alt$altCount. X',
//               nodeType: NodeType.prefixedNode,
//               leftPadding: (depth + 1) * 20 + alignmentOffset,
//               isLast: isLastAlternate,
//             ),
//           ]);
//         }
//         altCount++;
//       }
//     } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
//       // Store the parent node name for parallel children
//       final parentNodeName = node.loName!;

//       int parCount = 1;
//       for (int i = 0; i < node.parallelRoutes!.length; i++) {
//         final pr = node.parallelRoutes![i];
//         bool isLastParallel = (i == node.parallelRoutes!.length - 1);
        
//         if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
//           // Store the mapping of parallel children to their parent
//           parallelParentMap['Parl$parCount'] = parentNodeName;

//           final childRows = buildNodeTreeStructureWithAlignment(
//             nodeMap[pr.routeTo]!,
//             depth + 1,
//             nodeMap,
//             List.from(currentPathForCycleDetection),
//             'Parl$parCount. ',
//             0, // Reset alignmentOffset to 0 for parallel children
//           );
          
//           // Update the first row of childRows to set isLastParallel property
//           if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//             childRows[0][0] = NodeWidget(
//               text: childRows[0][0].text,
//               nodeType: childRows[0][0].nodeType,
//               leftPadding: childRows[0][0].leftPadding,
//               isLast: isLastParallel,
//             );
//           }
          
//           children.addAll(childRows);
//         } else {
//           // If routeTo is Terminal or node doesn't exist, treat as terminal
//           final terminalPadding = (depth + 1) * 20;
//           children.add([
//             NodeWidget(
//               text: 'Parl$parCount. X',
//               nodeType: NodeType.prefixedNode,
//               leftPadding: terminalPadding,
//               isLast: isLastParallel,
//             ),
//           ]);
//         }
//         parCount++;
//       }
//     }

//     return children;
//   }

//   List<List<NodeWidget>> buildNodeTreeStructureWithAlignment(
//     ProcessFlow node,
//     double depth,
//     Map<String, ProcessFlow> nodeMap,
//     List<String> currentPathForCycleDetection,
//     String branchPrefix,
//     double alignmentOffset,
//   ) {
//     List<List<NodeWidget>> result = [];
//     final nodeId = node.loId!;
//     final nodeName = node.loName!;

//     if (currentPathForCycleDetection.contains(nodeId)) {
//       result.add([
//         NodeWidget(
//           text: '$branchPrefix$nodeName (cycle)',
//           nodeType: NodeType.prefixedNode,
//           leftPadding: depth * 20 + alignmentOffset,
//         )
//       ]);
//       return result;
//     }

//     bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);

//     currentPathForCycleDetection.add(nodeId);

//     if (node.routeType == "Sequential") {
//       final chainNodes = <ProcessFlow>[];
//       final chainVisitedNames = <String>[];
//       final chainString = buildSequentialChainString(
//         node,
//         nodeMap,
//         chainVisitedNames,
//         chainNodes,
//         isFirstExpansionOfNode,
//         currentPathForCycleDetection,
//       );

//       final rowNodes = parseSequentialChain(
//           chainString, depth * 20 + alignmentOffset, branchPrefix);

//       result.add(rowNodes);

//       // Process children similar to main function
//       if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
//         final lastNodeInChain = chainNodes.last;
//         final lastNodeName = lastNodeInChain.loName!;

//         bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
//             isLastOccurrence(lastNodeName, currentPathForCycleDetection);

//         if (shouldExpandLastNode) {
//           for (final chainNode in chainNodes) {
//             expandedNodeNames.add(chainNode.loName!);
//           }

//           final children = buildNodeChildrenStructureWithAlignment(
//               lastNodeInChain,
//               depth,
//               nodeMap,
//               List.from(currentPathForCycleDetection),
//               alignmentOffset);
//           result.addAll(children);
//         }
//       } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
//         if (chainNodes.length >= 2) {
//           final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
//           final lastNonTerminalName = lastNonTerminalNode.loName!;

//           bool shouldExpandLastNonTerminal =
//               !expandedNodeNames.contains(lastNonTerminalName) &&
//                   isLastOccurrence(
//                       lastNonTerminalName, currentPathForCycleDetection);

//           if (shouldExpandLastNonTerminal) {
//             for (final chainNode in chainNodes) {
//               expandedNodeNames.add(chainNode.loName!);
//             }

//             final chainStringUpToNode = chainString.substring(
//                 0,
//                 chainString.indexOf(lastNonTerminalName) +
//                     lastNonTerminalName.length);
//             final nodePositionInString = chainStringUpToNode.length;

//             final children = buildNodeChildrenStructureWithAlignment(
//                 lastNonTerminalNode,
//                 depth,
//                 nodeMap,
//                 List.from(currentPathForCycleDetection),
//                 alignmentOffset + nodePositionInString);
//             result.addAll(children);
//           }
//         }
//       }
//     } else {
//       String nodeDisplay = nodeName;
//       if (node.loName == "Terminal") {
//         nodeDisplay += ' X';
//       }

//       String displayText = branchPrefix + nodeDisplay;
//       NodeType nodeType = NodeType.prefixedNode;

//       // Dynamic duplicate detection
//       bool isDuplicate =
//           _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
//       bool isLastOccurrenceOfNode =
//           isLastOccurrence(nodeName, currentPathForCycleDetection);
//       bool isExpandingNode = expandedNodeNames.contains(nodeName);

//       String finalDisplayText = displayText;
//       if (isDuplicate && !isLastOccurrenceOfNode) {
//         if (isExpandingNode) {
//           // Expanding duplicate nodes: % as prefix
//           finalDisplayText = '%$displayText';
//         } else {
//           // Non-expanding duplicate nodes: % as suffix
//           finalDisplayText = '$displayText%';
//         }
//       }

//       // Track rendered node count
//       incrementRenderedNodeCount(nodeName);

//       result.add([
//         NodeWidget(
//           text: finalDisplayText,
//           nodeType: nodeType,
//           leftPadding: depth * 20 + alignmentOffset,
//         )
//       ]);

//       expandedNodeNames.add(nodeName);
//       final children = buildNodeChildrenStructureWithAlignment(node, depth,
//           nodeMap, List.from(currentPathForCycleDetection), alignmentOffset);
//       result.addAll(children);
//     }

//     currentPathForCycleDetection.removeLast();
//     return result;
//   }

//   Widget buildRowWidget(BuildContext context, List<NodeWidget> rowNodes) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 0.0),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children:
//             rowNodes.map((node) => buildNodeWidget(context, node)).toList(),
//       ),
//     );
//   }

//   Widget buildAlternateNodeRow(BuildContext context, String text, int altIndex,
//     double leftPadding, bool isLast, {double? yOffset}) {
//   // Dynamic duplicate checking
//   String nodeName = _extractNodeName(text);
//   bool isDuplicate = _isDuplicateFromRenderedCount(nodeName);
//   bool isExpanding = _isExpandingFromContext(text, nodeName);
//   Color? duplicateColor =
//       isDuplicate ? getDuplicateNodeColor(nodeName) : null;

//   // Check if this node has the same name as other nodes
//   bool hasMultipleSameNames = hasSameName(text);
//   Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(text) : null;

//   String cleanText = text.contains("%") ? text.replaceAll("%", "") : text;

//   // Build content with link icons for duplicates
//   List<Widget> contentWidgets = [];

//   if (isDuplicate && isExpanding) {
//     // For expanding duplicate nodes, add link icon before text with same color as text container
//     contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//     contentWidgets.add(SizedBox(width: 4));
//   }

//   // Always use styled text for all text content to ensure consistent styling
//   contentWidgets.add(_buildStyledPrefixText(cleanText));

//   if (isDuplicate && !isExpanding) {
//     // For non-expanding duplicate nodes, add link icon after text with same color as text container
//     contentWidgets.add(SizedBox(width: 4));
//     contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//   }

//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 0.0),
//     child: Row(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         // Left padding
//         SizedBox(width: leftPadding),

//         // Tree connection lines container
//         SizedBox(
//           width: 32,
//           height: 32,
//           child: CustomPaint(
//             painter: AlternateTreeLinePainter(
//               isLast: isLast,
//               isFirst: altIndex == 1,
//               color: Color(0xff666666),
//               yOffset: yOffset??0, // Add yOffset parameter
//             ),
//             size: Size(32, 32),
//           ),
//         ),

//         // Circle
//         Container(
//           width: 6,
//           height: 6,
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             border: Border.all(color: Color(0xff666666), width: 1.0),
//             color: Colors.white,
//           ),
//         ),

//         SizedBox(width: 12),

//         // Text content with link icons
//         MouseRegion(
//           cursor: SystemMouseCursors.click,
//           child: GestureDetector(
//             onTap: () {
//               String temp = '';
//               if (nodeName.contains("Alt")) {
//                 temp = nodeName.substring(5);
//               } else if (nodeName.contains("Parl")) {
//                 temp = nodeName.substring(6);
//               } else {
//                 temp = nodeName;
//               }

//               showLoDetails(context, temp);
//             },
//             child: Container(
//               padding:
//                   const EdgeInsets.symmetric(horizontal: 0.0, vertical: 2.0),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: contentWidgets,
//               ),
//             ),
//           ),
//         ),
//       ],
//     ),
//   );
// }

// Widget buildTreeNodeRow(BuildContext context, String text, double leftPadding, 
//     {bool isLastParallel = false, double? yOffset}) {
//   // Dynamic duplicate checking
//   String nodeName = _extractNodeName(text);
//   bool isDuplicate = _isDuplicateFromRenderedCount(nodeName);
//   bool isExpanding = _isExpandingFromContext(text, nodeName);
//   Color? duplicateColor =
//       isDuplicate ? getDuplicateNodeColor(nodeName) : null;

//   // Check if this node has the same name as other nodes
//   bool hasMultipleSameNames = hasSameName(text);
//   Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(text) : null;

//   String cleanText = text.contains("%") ? text.replaceAll("%", "") : text;

//   // Build content with link icons for duplicates
//   List<Widget> contentWidgets = [];

//   if (isDuplicate && isExpanding) {
//     // For expanding duplicate nodes, add link icon before text with same color as text container
//     contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//     contentWidgets.add(SizedBox(width: 4));
//   }

//   // Always use styled text for all text content to ensure consistent styling
//   contentWidgets.add(_buildStyledPrefixText(cleanText));

//   if (isDuplicate && !isExpanding) {
//     // For non-expanding duplicate nodes, add link icon after text with same color as text container
//     contentWidgets.add(SizedBox(width: 4));
//     contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//   }

//   // Extract parallel number to determine if it's first
//   final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(text);
//   final parlIndex = parlMatch != null ? int.parse(parlMatch.group(1)!) : 1;

//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 0.0),
//     child: Row(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         // Left padding
//         SizedBox(width: leftPadding),

//         // Tree connection lines container
//         SizedBox(
//           width: 32,
//           height: 32,
//           child: CustomPaint(
//             painter: AlternateTreeLinePainter(
//               isLast: isLastParallel,
//               isFirst: parlIndex == 1,
//               color: Color(0xff666666),
//               yOffset: yOffset??0, // Add yOffset parameter
//             ),
//             size: Size(32, 32),
//           ),
//         ),

//         // Circle
//         Container(
//           width: 6,
//           height: 6,
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             border: Border.all(color: Color(0xff666666), width: 1.0),
//             color: Colors.white,
//           ),
//         ),

//         SizedBox(width: 12),

//         // Text content with link icons
//         MouseRegion(
//           cursor: SystemMouseCursors.click,
//           child: GestureDetector(
//             onTap: () {
//               String temp = '';
//               if (nodeName.contains("Alt")) {
//                 temp = nodeName.substring(5);
//               } else if (nodeName.contains("Parl")) {
//                 temp = nodeName.substring(6);
//               } else {
//                 temp = nodeName;
//               }

//               showLoDetails(context, temp);
//             },
//             child: Container(
//               padding:
//                   const EdgeInsets.symmetric(horizontal: 0.0, vertical: 2.0),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: contentWidgets,
//               ),
//             ),
//           ),
//         ),
//       ],
//     ),
//   );
// }

// static const double _nodeHeight = 32.0; // Standard node height
// static const double _dashLength = 3.0;
// static const double _dashGap = 2.0;
// static const double _dashAndGap = _dashLength + _dashGap;

// // Helper method to calculate synchronized yOffset for tree structure
// double _calculateSynchronizedOffset(int nodeIndex) {
//   return (nodeIndex * _nodeHeight) % _dashAndGap;
// }


//   Color? getParallelNodeColor(String text) {
//     // Check if this is a parallel node (starts with "Parl")
//     if (text.startsWith('Parl')) {
//       // Extract the parallel number from text (e.g., "Parl1. NodeName" -> "Parl1")
//       final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(text);
//       if (parlMatch != null) {
//         final parlKey = 'Parl${parlMatch.group(1)!}';

//         // Get the parent node name from the mapping
//         String? parentNodeName = parallelParentMap[parlKey];

//         if (parentNodeName != null) {
//           // Use the parent node name as the group key
//           String groupKey = parentNodeName;

//           // If this parallel parent group doesn't have a color yet, assign one
//           if (!parallelGroupColors.containsKey(groupKey)) {
//             parallelGroupColors[groupKey] =
//                 colors[parallelColorIndex % colors.length];
//             parallelColorIndex++;
//           }

//           return parallelGroupColors[groupKey];
//         }
//       }
//     }

//     // Check if this node is the parent of a parallel group
//     for (String parentNodeName in parallelParentMap.values) {
//       if (text == parentNodeName || text.contains(parentNodeName)) {
//         // If this is a parent node that has parallel children, give it the same color
//         if (parallelGroupColors.containsKey(parentNodeName)) {
//           return parallelGroupColors[parentNodeName];
//         }
//       }
//     }

//     // Check if this node is a child of a parallel group by checking if it contains parallel prefix
//     for (String parlKey in parallelParentMap.keys) {
//       if (text.startsWith(parlKey + '.')) {
//         String? parentNodeName = parallelParentMap[parlKey];
//         if (parentNodeName != null &&
//             parallelGroupColors.containsKey(parentNodeName)) {
//           return parallelGroupColors[parentNodeName];
//         }
//       }
//     }

//     return null;
//   }

//   bool isDuplicateNode(String text) {
//     String nodeName = _extractNodeName(text);
//     return _isDuplicateFromRenderedCount(nodeName);
//   }

//   bool isExpandingNode(String text) {
//     String nodeName = _extractNodeName(text);
//     return _isExpandingFromContext(text, nodeName);
//   }

//   Color getDuplicateNodeColor(String text) {
//     String nodeName = _extractNodeName(text);

//     // Assign a consistent color for all duplicates of this node using separate color palette
//     if (!duplicateNodeColors.containsKey(nodeName)) {
//       duplicateNodeColors[nodeName] =
//           linkIconColors[duplicateColorIndex % linkIconColors.length];
//       duplicateColorIndex++;
//     }

//     return duplicateNodeColors[nodeName]!;
//   }

//   // Get same-name color for nodes that appear multiple times
//   Color? getSameNameColor(String text) {
//     String nodeName = _extractNodeName(text);
//     return sameNameNodeColors[nodeName];
//   }

//   // Check if a node has the same name as other nodes
//   bool hasSameName(String text) {
//     String nodeName = _extractNodeName(text);
//     return sameNameGroups.containsKey(nodeName);
//   }

//   // Build styled text with different formatting for Alt/Parl prefixes
//   Widget _buildStyledPrefixText(String text) {
//     // Split text into prefix and main content
//     if (text.contains('.')) {
//       final parts = (text.split('.'));
//       final prefix = parts[0] + '.'; // e.g., "Alt1. " or "Parl1. "
//       final mainText = parts.sublist(1).join('. '); // Rest of the text
      
//       // Determine prefix color based on type
//       Color prefixColor;
//       if (prefix.startsWith('Alt')) {
//         prefixColor = Colors.grey; // Blue for Alt prefixes
//       } else if (prefix.startsWith('Parl')) {
//         prefixColor = Colors.grey; // Green for Parl prefixes
//       } else {
//         prefixColor = Colors.grey; // Purple for other prefixes
//       }
      
//       return RichText(
//         text: TextSpan(
//           children: [
//             TextSpan(
//               text: prefix,
//               style: TextStyle(
//                 fontFamily: 'TiemposText',
//                 fontSize: 10.0, // Slightly larger size for prefix
//                 color: prefixColor, // Colored prefix
//                 fontWeight: FontWeight.w700, // Bold prefix
//               ),
//             ),
//             TextSpan(
//               text: mainText,
//               style: TextStyle(
//                 fontFamily: 'TiemposText',
//                 fontSize: 14.0, // Normal size for main text
//                 color: Colors.black87, // Normal color for main text
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ],
//         ),
//       );
//     } else {
//       // Fallback to normal text if no prefix found
//       return Text(
//         text,
//         style: TextStyle(
//           fontFamily: 'TiemposText',
//           fontSize: 14.0,
//           color: Colors.black87,
//           fontWeight: FontWeight.w600,
//         ),
//       );
//     }
//   }

//   Widget buildNodeWidget(BuildContext context, NodeWidget node) {
//     if (node.nodeType == NodeType.terminal) {
//       return _buildTerminalIcon();
//     }

//     // Handle prefixed nodes that start with "Alt" with special dotted line design
//     // Alt node
// if (node.nodeType == NodeType.prefixedNode && node.text.startsWith('Alt')) {
//   final altMatch = RegExp(r'Alt(\d+)\.').firstMatch(node.text);
//   final altIndex = altMatch != null ? int.parse(altMatch.group(1)!) : 1;

//   // Correct: calculate synchronized yOffset
//   double yOffset = _calculateSynchronizedOffset(altIndex);

//   return buildAlternateNodeRow(
//     context,
//     node.text,
//     altIndex,
//     node.leftPadding,
//     node.isLast,
//     yOffset: yOffset, // ✅ Pass correct yOffset here
//   );
// }

// // Parl node
// if (node.nodeType == NodeType.prefixedNode && node.text.startsWith('Parl')) {
//   final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(node.text);
//   final parlIndex = parlMatch != null ? int.parse(parlMatch.group(1)!) : 1;

//   // ✅ Consistently calculate yOffset (you were using offset1 but passing fixed value)
//   double yOffset = _calculateSynchronizedOffset(parlIndex);

//   return buildTreeNodeRow(
//     context,
//     node.text,
//     node.leftPadding,
//     isLastParallel: node.isLast,
//     yOffset: yOffset, // ✅ Correct yOffset used
//   );
// }


//     // Check if this is a duplicate node
//     bool isDuplicate = isDuplicateNode(node.text);
//     bool isExpanding = isExpandingNode(node.text);

//     // Get parallel node color if applicable
//     Color? parallelColor = getParallelNodeColor(node.text);

//     // Get duplicate node color if applicable
//     Color? duplicateColor =
//         isDuplicate ? getDuplicateNodeColor(node.text) : null;

//     // Check if this node has the same name as other nodes
//     bool hasMultipleSameNames = hasSameName(node.text);
//     Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(node.text) : null;

//     // Build the node content with link icons for duplicates
//     List<Widget> nodeContent = [];

//     if (isDuplicate && isExpanding) {
//       // For expanding duplicate nodes, add link icon before text with same color as text container
//       nodeContent.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//       nodeContent.add(SizedBox(width: 4));
//     }
//     String cleanText =
//         node.text.contains("%") ? node.text.replaceAll("%", "") : node.text;
    
//     // Wrap text in colored container if it has same name as other nodes
//     Widget textWidget;
    
//     // Build styled text with different formatting for Alt/Parl prefixes
//     if (cleanText.startsWith('Alt') || cleanText.startsWith('Parl')) {
//       textWidget = _buildStyledPrefixText(cleanText);
//     } else {
//       textWidget = Text(
//         cleanText,
//         style: TextStyle(
//           fontFamily: 'TiemposText',
//           fontSize: 14.0,
//           color: Colors.black,
//           fontWeight: FontWeight.w600,
//         ),
//       );
//     }

//     if (hasMultipleSameNames && sameNameColor != null) {
//       textWidget = Container(
//         padding: EdgeInsets.symmetric(horizontal: 4.0, vertical: 0.0),
//         decoration: BoxDecoration(
//           color: sameNameColor.withOpacity(0.3),
//           borderRadius: BorderRadius.circular(4.0),
//           border: Border.all(
//             color: sameNameColor.withOpacity(0.6),
//             width: 1.0,
//           ),
//         ),
//         child: textWidget,
//       );
//     }

//     nodeContent.add(textWidget);

//     if (isDuplicate && !isExpanding) {
//       // For non-expanding duplicate nodes, add link icon after text with same color as text container
//       nodeContent.add(SizedBox(width: 4));
//       nodeContent.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
//     }

//     return MouseRegion(
//       cursor: SystemMouseCursors.click,
//       child: GestureDetector(
//         onTap: () {
//           showLoDetails(context, _extractNodeName(node.text));
//         },
//         child: Container(
//           margin: EdgeInsets.only(
//             left: node.leftPadding,
//             right: 8.0,
//           ),
//           padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 6.0),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               // Add circle for main flow nodes (root nodes)
//               if (node.leftPadding == 0) ...[
//                 // SizedBox()
//                 // Container(
//                 //   width: 6,
//                 //   height: 6,
//                 //   decoration: BoxDecoration(
//                 //     shape: BoxShape.circle,
//                 //     border: Border.all(color: Colors.grey.shade600, width: 1.0),
//                 //     color: Colors.white,
//                 //   ),
//                 // ),
//                 SizedBox(width: 12),
//               ],
//               ...nodeContent,
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// // Build terminal icon for workflow end
// Widget _buildTerminalIcon() {
//   return Container(
//     margin: EdgeInsets.only(left: 8),
//     width: 18,
//     height: 18,
//     decoration: BoxDecoration(
//       borderRadius: BorderRadius.circular(4),
//       shape: BoxShape.rectangle,
//       color: Colors.red.shade50,
//       border: Border.all(color: Color(0xffFF0000)),
//     ),
//     child: Icon(
//       Icons.close,
//       size: 14,
//       color: Color(0xffFF0000),
//     ),
//   );
// }

// // Build link icon for connections
// Widget _buildLinkIcon({Color? boxColor}) {
//   return Container(
//     decoration:
//         BoxDecoration(borderRadius: BorderRadius.circular(4), color: boxColor),
//     // padding: EdgeInsets.all(4),
//     child: SvgPicture.asset(
//       'assets/images/workflow/link_icon.svg',
//       width: 20,
//       height: 20,
//       fit: BoxFit.fill,
//       colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
//     ),
//   );
// }

// showLoDetails(BuildContext context, String text) {
//   final provider = Provider.of<ManualCreationProvider>(context, listen: false);
//   if (provider.extractedWorkflowData != null) {
//     List? loList = provider
//         .extractedWorkflowData?.first['workFlowDetails']?.localObjectivesList;
//     if (loList != null && loList.isNotEmpty) {
//       dynamic element = loList.firstWhere(
//         (element) => element.loName == text,
//       );
//       final index = loList.indexOf(element);
//       provider.showWorkflowLoDetailsPanel(loList[index].loDetails);
//     }
//   }
// }




// // Custom painter for alternate node tree lines


// class AlternateTreeLinePainter extends CustomPainter {
//   final bool isLast;
//   final bool isFirst;
//   final Color color;
//   final double yOffset; // Offset from top of the group

//   AlternateTreeLinePainter({
//     required this.isLast,
//     required this.isFirst,
//     required this.color,
//     this.yOffset = 0.0,
//   });

//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint = Paint()
//       ..color = color
//       ..strokeWidth = 1.0
//       ..style = PaintingStyle.stroke;

//     final double centerX = 12.0; // Horizontal position of vertical line
//     final double centerY = size.height / 2;

//     // Vertical dotted line from top to center
//     _drawDottedLine(canvas, paint, Offset(centerX, 0), Offset(centerX, centerY), yOffset);

//     // Vertical dotted line from center to bottom (if not last)
//     if (!isLast) {
//       _drawDottedLine(canvas, paint, Offset(centerX, centerY), Offset(centerX, size.height), yOffset + centerY);
//     }

//     // Horizontal dotted line from center to right
//     _drawDottedLine(canvas, paint, Offset(centerX, centerY), Offset(size.width, centerY), 0);
//   }

//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) => true;

//   void _drawDottedLine(Canvas canvas, Paint paint, Offset start, Offset end, double phaseOffset) {
//     const double dashLength = 5.0;
//     const double dashGap = 4.0;
//     const double dashAndGap = dashLength + dashGap;

//     final double totalLength = (end - start).distance;
//     if (totalLength == 0) return;

//     final Offset direction = (end - start) / totalLength;

//     double current = -phaseOffset % dashAndGap;

//     while (current < totalLength) {
//       final double startDistance = current.clamp(0, totalLength);
//       final double endDistance = (current + dashLength).clamp(0, totalLength);

//       if (endDistance > startDistance) {
//         final Offset dashStart = start + direction * startDistance;
//         final Offset dashEnd = start + direction * endDistance;
//         canvas.drawLine(dashStart, dashEnd, paint);
//       }

//       current += dashAndGap;
//     }
//   }
// }


import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/workflow/workflow_manual_response_model.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';


// Individual node widget
class NodeWidget {
  final String text;
  final NodeType nodeType;
  final double leftPadding;
  final bool isLast;

  NodeWidget({
    required this.text,
    required this.nodeType,
    this.leftPadding = 0,
    this.isLast = false,
  });
}

enum NodeType {
  root, // SubmitLeaveRequest
  sequential, // NotifyEmployee, UpdateCalendar, etc.
  parallel, // Same as sequential but in parallel context
  alternate, // Same as sequential but in alternate context
  prefixedNode, // Alt1. UploadDocumentation, Parl1. UpdateCalendar
  terminal, // X
  arrow, // ->
}

// Global variables for tracking
Set<String> expandedNodeNames = {};
Map<String, List<String>> nodeOccurrences = {};
final Map<String, int> _nodeRenderCounts = {};

// Global colors for parallel nodes
List<Color> colors = [
  Color(0xFFE3F2FD),
  Color(0xffFFFDD0),
  Color(0xffF5F5DC),
  Color(0xffAAF0D1),
  Color(0xffAAF0D1),
  Color(0xffF0FFF0),
  Color(0xffB0EACD),
  Color(0xffACE1AF),
  Color(0xffD0F0C0),
  Color(0xffADDFAD),
  Color(0xffA8E4A0),
  Color(0xffD5F5B8),
  Color(0xffCDE5B2),
  Color(0xffECEBBD),
  Color(0xffB2FFFF),
  Color(0xffAFEEEE),
  Color(0xffF5FFFA),
  Color(0xffA7D8C9),
  Color(0xff7FFFD4),
  Color(0xffCFFFE5),
  Color(0xffD4F1F9),
  Color(0xffD6F1FF),
  Color(0xffAEC6CF),
];

// Track parallel group colors by parent node
Map<String, Color> parallelGroupColors = {};
int parallelColorIndex = 0;
// List colors = ManualCreationProvider.pastelColors;

// Track which parent nodes have parallel children
Map<String, String> parallelParentMap = {};

// Track duplicate node colors for link icons
Map<String, Color> duplicateNodeColors = {};
int duplicateColorIndex = 0;

// Track actual occurrences in rendered tree (not just possible paths)
Map<String, int> renderedNodeCount = {};

// Track nodes with same names for colored containers
Map<String, Color> sameNameNodeColors = {};
Map<String, List<String>> sameNameGroups = {};
int sameNameColorIndex = 0;

// Separate color palette for link icons to avoid overlap with parallel colors
List<Color> linkIconColors = [
  // Color(0xffFFA07A),
  Color(0xffDFFF00),
  Color(0xffE0B0FF),
  Color(0xffB4A7D6),
  Color(0xffC7E9F1),
  Color(0xffC3CDE6),
  Color(0xffCCCCFF),
  Color(0xffE6E6FA),
  Color(0xffE4D0EC),
  Color(0xffD8BFD8),
  Color(0xffB0E0E6),
  Color(0xffF2D1E3),
  Color(0xffF49AC2),
  Color(0xffFFA6C9),
  Color(0xffFBB1BD),
  Color(0xffFFD1DC),
  Color(0xffFFDFDD),
  Color(0xffFFDAB9),
  Color(0xffFDC5B5),
  Color(0xffD8B7DD),
];

class ProcessFlowWidget extends StatelessWidget {
  final List<ProcessFlow> processList;

   ProcessFlowWidget({super.key, required this.processList});

@override
Widget build(BuildContext context) {
  final treeStructure = buildTreeStructure(processList);
  log(jsonEncode(processList));
  
  return SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(1.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: treeStructure
              .asMap()
              .entries
              .map((entry) {
                int index = entry.key;
                List<NodeWidget> row = entry.value;
                bool isFirstRow = index == 0;
                return buildRowWidget(context, row, isFirstRow: isFirstRow);
              })
              .toList(),
        ),
      ),
    ),
  );
}

  List<List<NodeWidget>> buildTreeStructure(List<ProcessFlow> processList) {
    // Reset global variables
    expandedNodeNames.clear();
    nodeOccurrences.clear();
    parallelParentMap.clear();
    parallelGroupColors.clear();
    parallelColorIndex = 0;
    renderedNodeCount.clear();
    sameNameNodeColors.clear();
    sameNameGroups.clear();
    sameNameColorIndex = 0;
    _nodeRenderCounts.clear();


    final nodeMapById = <String, ProcessFlow>{};
    final Set<String> nextNodeIds = {};

    for (final node in processList) {
      nodeMapById[node.loId!] = node;

      if (node.conditions != null) {
        for (final condition in node.conditions!) {
          if (condition.routeTo != "Terminal") {
            nextNodeIds.add(condition.routeTo!);
          }
        }
      }

      if (node.routes != null) {
        for (final route in node.routes!) {
          if (route != "Terminal") {
            nextNodeIds.add(route);
          }
        }
      }

      if (node.parallelRoutes != null) {
        for (final pr in node.parallelRoutes!) {
          if (pr.routeTo != "Terminal") {
            nextNodeIds.add(pr.routeTo!);
          }
        }
      }

      if (node.joinAt != null && node.joinAt != "Terminal") {
        nextNodeIds.add(node.joinAt!);
      }
    }

    // Pre-process parallel relationships
    for (final node in processList) {
      if (node.routeType == "Parallel" && node.parallelRoutes != null) {
        final parentNodeName = node.loName!;
        int parCount = 1;
        for (final pr in node.parallelRoutes!) {
          if (pr.routeTo != "Terminal") {
            parallelParentMap['Parl$parCount'] = parentNodeName;
            parCount++;
          }
        }

        // Assign color to this parallel group immediately
        if (!parallelGroupColors.containsKey(parentNodeName)) {
          parallelGroupColors[parentNodeName] =
              colors[parallelColorIndex % colors.length];
          parallelColorIndex++;
        }
      }
    }

    final rootNodes =
        processList.where((node) => !nextNodeIds.contains(node.loId)).toList();

    // First pass: collect all occurrences
    for (final root in rootNodes) {
      collectOccurrences(root, nodeMapById, []);
    }

    // Identify same-name groups and assign colors
    _identifySameNameGroups();

    // Second pass: build tree structure
    List<List<NodeWidget>> result = [];
    for (final root in rootNodes) {
      final rows = buildNodeTreeStructure(root, 0, nodeMapById, [], '');
      result.addAll(rows);
    }

    return result;
  }

  void collectOccurrences(
    ProcessFlow node,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPath,
  ) {
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPath.contains(nodeId)) {
      return;
    }

    if (!nodeOccurrences.containsKey(nodeName)) {
      nodeOccurrences[nodeName] = [];
    }
    final pathKey = '${currentPath.join('/')}/$nodeId';
    nodeOccurrences[nodeName]!.add(pathKey);

    currentPath.add(nodeId);

    if (node.routeType == "Sequential" && node.routes != null) {
      for (final next in node.routes!) {
        if (next != "Terminal" && nodeMap.containsKey(next)) {
          collectOccurrences(nodeMap[next]!, nodeMap, List.from(currentPath));
        }
      }
    } else if (node.routeType == "Alternate" && node.conditions != null) {
      for (final cond in node.conditions!) {
        if (cond.routeTo != "Terminal" && nodeMap.containsKey(cond.routeTo)) {
          collectOccurrences(
              nodeMap[cond.routeTo]!, nodeMap, List.from(currentPath));
        }
      }
    } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
      for (final pr in node.parallelRoutes!) {
        if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
          collectOccurrences(
              nodeMap[pr.routeTo]!, nodeMap, List.from(currentPath));
        }
      }

      if (node.joinAt != null && node.joinAt != "Terminal") {
        if (nodeMap.containsKey(node.joinAt)) {
          collectOccurrences(
              nodeMap[node.joinAt]!, nodeMap, List.from(currentPath));
        }
      }
    }

    currentPath.removeLast();
  }

  // Helper method to track rendered nodes
  void incrementRenderedNodeCount(String nodeName) {
    renderedNodeCount[nodeName] = (renderedNodeCount[nodeName] ?? 0) + 1;
  }

  // Method to identify same-name groups and assign colors
  void _identifySameNameGroups() {
    // Find nodes that appear multiple times
    for (String nodeName in nodeOccurrences.keys) {
      List<String> occurrences = nodeOccurrences[nodeName]!;
      if (occurrences.length > 1) {
        // This node appears multiple times, add to same-name groups
        if (!sameNameGroups.containsKey(nodeName)) {
          sameNameGroups[nodeName] = [];
        }
        sameNameGroups[nodeName]!.addAll(occurrences);
        
        // Assign a color to this same-name group
        if (!sameNameNodeColors.containsKey(nodeName)) {
          sameNameNodeColors[nodeName] = 
              colors[sameNameColorIndex % colors.length];
          sameNameColorIndex++;
        }
      }
    }
  }

  // Dynamic method to check if a node is a duplicate based on its actual rendering context
  bool _isDynamicDuplicate(String nodeName, List<String> currentTreePath) {

    // Get total possible occurrences from path analysis
    int totalPossibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;
    bool hasMultiplePossiblePaths = totalPossibleOccurrences > 1;
    bool isLastOccurrenceInPath = isLastOccurrence(nodeName, currentTreePath);

    return hasMultiplePossiblePaths && !isLastOccurrenceInPath;
  }

  // Helper method to extract clean node name from display text
  String _extractNodeName(String text) {
    String nodeName = text;

    // Remove prefixes like "Alt1. " or "Parl1. "
    if (text.contains('. ')) {
      nodeName = text.split('. ').last;
    }

    // Remove % prefix if present
    if (nodeName.startsWith('%')) {
      nodeName = nodeName.substring(1);
    }

    // Remove % suffix if present
    if (nodeName.endsWith('%')) {
      nodeName = nodeName.substring(0, nodeName.length - 1);
    }

    return nodeName;
  }

  // Dynamic method to check if a node is duplicate based on rendered count
  bool _isDuplicateFromRenderedCount(String nodeName) {
    // A node is considered duplicate if it has been rendered more than once
    int possibleOccurrences = nodeOccurrences[nodeName]?.length ?? 1;

    return possibleOccurrences > 1;
  }

  // Dynamic method to check if a node is expanding from context
  bool _isExpandingFromContext(String displayText, String nodeName) {
    // If the text has % prefix, it means this is an expanding duplicate
    if (displayText.startsWith('%')) {
      return true;
    }

    // If the text has % suffix, it means this is a non-expanding duplicate
    if (displayText.endsWith('%')) {
      return false;
    }

    // For nodes without % markers, check if they are in expandedNodeNames
    return expandedNodeNames.contains(nodeName);
  }

  List<NodeWidget> parseSequentialChain(
      String chainString, double basePadding, String prefix) {
    List<NodeWidget> nodes = [];

    // Remove prefix from chain string if it exists
    String cleanChain = chainString;
    if (prefix.isNotEmpty && chainString.startsWith(prefix)) {
      cleanChain = chainString.substring(prefix.length).trim();
    }

    // Split the chain by " -> "
    final parts = cleanChain.split(' -> ');

    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();

      if (part.isEmpty) continue;

      if (part == 'X') {
        nodes.add(NodeWidget(
          text: 'X',
          nodeType: NodeType.terminal,
          leftPadding: 0,
        ));
      } else {
        // Determine node type and clean text
        String cleanText = part;
        NodeType nodeType = NodeType.sequential;

        if (part.startsWith('%')) {
          cleanText = part.substring(1);
        }

        // Combine prefix with first node if this is the first node and prefix exists
        String displayText = cleanText;
        if (i == 0 && prefix.isNotEmpty) {
          displayText = prefix.trim() + cleanText;
          nodeType = NodeType.prefixedNode;
        }

        // Track rendered node count
        String nodeName = cleanText;
        if (nodeName.endsWith('%')) {
          nodeName = nodeName.substring(0, nodeName.length - 1);
        }
        incrementRenderedNodeCount(nodeName);

        nodes.add(NodeWidget(
          text: displayText,
          nodeType: nodeType,
          leftPadding: i == 1 ? 0 : basePadding,
        ));
      }
    }

    return nodes;
  }

  String buildSequentialChainString(
    ProcessFlow node,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathVisited,
    List<ProcessFlow> chainNodes,
    bool isFirstExpansionOfHead,
    List<String> currentTreePath,
  ) {
    final nodeName = node.loName!;

    if (currentPathVisited.contains(nodeName)) {
      return '$nodeName (cycle)';
    }

    currentPathVisited.add(nodeName);
    chainNodes.add(node);

    // Dynamic duplicate detection
    bool isDuplicate = _isDynamicDuplicate(nodeName, currentTreePath);
    bool isLastOccurrenceOfNode = isLastOccurrence(nodeName, currentTreePath);
    bool isExpandingNode = expandedNodeNames.contains(nodeName);

    String nodeDisplay = nodeName;
    if (isDuplicate && !isLastOccurrenceOfNode) {
      if (isExpandingNode) {
        // Expanding duplicate nodes: % as prefix
        nodeDisplay = '%$nodeName';
      } else {
        // Non-expanding duplicate nodes: % as suffix
        nodeDisplay = '$nodeName%';
      }
    }

    // Check if node is terminal (no routes and no conditions)
    bool isTerminalNode = (node.routes == null || node.routes!.isEmpty) &&
        (node.conditions == null || node.conditions!.isEmpty) &&
        (node.parallelRoutes == null || node.parallelRoutes!.isEmpty);

    if (isTerminalNode) {
      return '$nodeDisplay -> X';
    }

    if (node.routeType == "Sequential" &&
        node.routes != null &&
        node.routes!.isNotEmpty) {
      final nextRoute = node.routes!.first;
      if (nextRoute == "Terminal") {
        return '$nodeDisplay -> X';
      } else if (nodeMap.containsKey(nextRoute)) {
        final nextChainString = buildSequentialChainString(
          nodeMap[nextRoute]!,
          nodeMap,
          currentPathVisited,
          chainNodes,
          isFirstExpansionOfHead,
          currentTreePath,
        );
        return '$nodeDisplay -> $nextChainString';
      }
    } else if (node.routeType == "Parallel" &&
        node.joinAt != null &&
        node.joinAt != "Terminal") {
      bool isLastOcc = isLastOccurrence(nodeName, currentTreePath);
      if (isLastOcc && nodeMap.containsKey(node.joinAt)) {
        final joinChainString = buildSequentialChainString(
          nodeMap[node.joinAt]!,
          nodeMap,
          currentPathVisited,
          chainNodes,
          isFirstExpansionOfHead,
          currentTreePath,
        );
        return '$nodeDisplay -> $joinChainString';
      }
    }

    return nodeDisplay;
  }

  bool isLastOccurrence(String nodeName, List<String> currentPath) {
    if (!nodeOccurrences.containsKey(nodeName)) return true;

    final pathKey = currentPath.join('/');
    final occurrences = nodeOccurrences[nodeName]!;

    // Check if current path matches the last occurrence
    return occurrences.isNotEmpty && occurrences.last.contains(pathKey);
  }

  List<List<NodeWidget>> buildNodeTreeStructure(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    String branchPrefix,
  ) {
    List<List<NodeWidget>> result = [];
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPathForCycleDetection.contains(nodeId)) {
      result.add([
        NodeWidget(
          text: '$branchPrefix$nodeName (cycle)',
          nodeType: NodeType.sequential,
          leftPadding: depth * 20,
        )
      ]);
      return result;
    }

    bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);
    currentPathForCycleDetection.add(nodeId);

    if (node.routeType == "Sequential") {
      final chainNodes = <ProcessFlow>[];
      final chainVisitedNames = <String>[];
      final chainString = buildSequentialChainString(
        node,
        nodeMap,
        chainVisitedNames,
        chainNodes,
        isFirstExpansionOfNode,
        currentPathForCycleDetection,
      );

      // Parse sequential chain into individual nodes
      final rowNodes =
          parseSequentialChain(chainString, depth * 20, branchPrefix);
      if (rowNodes.isNotEmpty) {
        result.add(rowNodes);
      }

      // Process children of sequential chains
      if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
        final lastNodeInChain = chainNodes.last;
        final lastNodeName = lastNodeInChain.loName!;

        bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
            isLastOccurrence(lastNodeName, currentPathForCycleDetection);

        if (shouldExpandLastNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }

          final children = buildNodeChildrenStructure(lastNodeInChain, depth,
              nodeMap, List.from(currentPathForCycleDetection), 0);
          result.addAll(children);
        }
      } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
        if (chainNodes.length >= 2) {
          final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
          final lastNonTerminalName = lastNonTerminalNode.loName!;

          bool shouldExpandLastNonTerminal =
              !expandedNodeNames.contains(lastNonTerminalName) &&
                  isLastOccurrence(
                      lastNonTerminalName, currentPathForCycleDetection);

          if (shouldExpandLastNonTerminal) {
            for (final chainNode in chainNodes) {
              expandedNodeNames.add(chainNode.loName!);
            }

            final chainStringUpToNode = chainString.substring(
                0,
                chainString.indexOf(lastNonTerminalName) +
                    lastNonTerminalName.length);
            final nodePositionInString = chainStringUpToNode.length;

            final children = buildNodeChildrenStructureWithAlignment(
                lastNonTerminalNode,
                depth,
                nodeMap,
                List.from(currentPathForCycleDetection),
                nodePositionInString.toDouble());
            result.addAll(children);
          }
        } else if (chainNodes.isNotEmpty && isFirstExpansionOfNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }
        }
      }
    } else {
      String nodeDisplay = nodeName;
      if (node.loName == "Terminal") {
        nodeDisplay += ' X';
      }

      NodeType nodeType =
          branchPrefix.isNotEmpty ? NodeType.prefixedNode : NodeType.sequential;
      String displayText =
          branchPrefix.isNotEmpty ? '$branchPrefix$nodeDisplay' : nodeDisplay;

      // Dynamic duplicate detection
      bool isDuplicate =
          _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
      bool isLastOccurrenceOfNode =
          isLastOccurrence(nodeName, currentPathForCycleDetection);
      bool isExpandingNode = expandedNodeNames.contains(nodeName);

      String finalDisplayText = displayText;
      if (isDuplicate && !isLastOccurrenceOfNode) {
        if (isExpandingNode) {
          // Expanding duplicate nodes: % as prefix
          finalDisplayText = '%$displayText';
        } else {
          // Non-expanding duplicate nodes: % as suffix
          finalDisplayText = '$displayText%';
        }
      }

      // Track rendered node count
      incrementRenderedNodeCount(nodeName);

      result.add([
        NodeWidget(
          text: finalDisplayText,
          nodeType: nodeType,
          leftPadding: depth * 20,
        )
      ]);

      if (isLastOccurrenceOfNode) {
        expandedNodeNames.add(nodeName);
        final children = buildNodeChildrenStructure(
            node, depth, nodeMap, List.from(currentPathForCycleDetection), 0);
        result.addAll(children);
      } else {
        expandedNodeNames.add(nodeName);
      }
    }

    currentPathForCycleDetection.removeLast();
    return result;
  }

  List<List<NodeWidget>> buildNodeChildrenStructure(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    double alignmentOffset,
  ) {
    return buildNodeChildrenStructureWithAlignment(
        node, depth, nodeMap, currentPathForCycleDetection, alignmentOffset);
  }

  List<List<NodeWidget>> buildNodeChildrenStructureWithAlignment(
  ProcessFlow node,
  double depth,
  Map<String, ProcessFlow> nodeMap,
  List<String> currentPathForCycleDetection,
  double alignmentOffset,
) {
  List<List<NodeWidget>> children = [];

  // Base level spacing
  const double levelSpacing = 80.0;

  // Only one hardcoded position for depth = 1.0
  final Map<double, double> baseCirclePositions = {
    1.0: 4.0, // For Alt1, Alt2 level - centered with dotted line
  };

  // Calculate circle alignment: dynamic beyond depth 1.0
  double calculateCircleAlignment(double currentDepth, double extraOffset) {
    double base = baseCirclePositions[currentDepth] ?? (currentDepth * levelSpacing - 78.0);
    return base + extraOffset;
  }

  if (node.routeType == "Alternate" && node.conditions != null) {
    int altCount = 1;
    for (int i = 0; i < node.conditions!.length; i++) {
      final cond = node.conditions![i];
      bool isLastAlternate = (i == node.conditions!.length - 1);
      final altText = 'Alt$altCount. ';

      if (cond.routeTo == "Terminal") {
        final terminalText = '${altText}X';
        children.add([
          NodeWidget(
            text: terminalText,
            nodeType: NodeType.prefixedNode,
            leftPadding: calculateCircleAlignment(depth , alignmentOffset),
            isLast: isLastAlternate,
          ),
        ]);
      } else if (nodeMap.containsKey(cond.routeTo)) {
        final childRows = buildNodeTreeStructureWithAlignment(
          nodeMap[cond.routeTo]!,
          depth + 1,
          nodeMap,
          List.from(currentPathForCycleDetection),
          altText,
          alignmentOffset,
        );

        if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
          final originalWidget = childRows[0][0];
          final cleanText = originalWidget.text.replaceFirst(RegExp(r'^Alt\d+\.\s*'), '');
          final prefixedText = '$altText$cleanText';

          childRows[0][0] = NodeWidget(
            text: prefixedText,
            nodeType: originalWidget.nodeType,
            leftPadding: calculateCircleAlignment(depth + 1, alignmentOffset),
            isLast: isLastAlternate,
          );
        }
        children.addAll(childRows);
      } else {
        final terminalText = '${altText}X';
        children.add([
          NodeWidget(
            text: terminalText,
            nodeType: NodeType.prefixedNode,
            leftPadding: calculateCircleAlignment(depth + 1, alignmentOffset),
            isLast: isLastAlternate,
          ),
        ]);
      }
      altCount++;
    }
  } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
    final parentNodeName = node.loName!;
    int parCount = 1;

    for (int i = 0; i < node.parallelRoutes!.length; i++) {
      final pr = node.parallelRoutes![i];
      bool isLastParallel = (i == node.parallelRoutes!.length - 1);
      final parlText = 'Parl$parCount. ';

      if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
        parallelParentMap['Parl$parCount'] = parentNodeName;

        final childRows = buildNodeTreeStructureWithAlignment(
          nodeMap[pr.routeTo]!,
          depth + 1,
          nodeMap,
          List.from(currentPathForCycleDetection),
          parlText,
          0.0,
        );

        if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
          final originalWidget = childRows[0][0];
          final cleanText = originalWidget.text.replaceFirst(RegExp(r'^Parl\d+\.\s*'), '');
          final prefixedText = '$parlText$cleanText';

          childRows[0][0] = NodeWidget(
            text: prefixedText,
            nodeType: originalWidget.nodeType,
            leftPadding: calculateCircleAlignment(depth + 1, -2.0),
            isLast: isLastParallel,
          );
        }
        children.addAll(childRows);
      } else {
        final terminalText = '${parlText}X';
        children.add([
          NodeWidget(
            text: terminalText,
            nodeType: NodeType.prefixedNode,
            leftPadding: calculateCircleAlignment(depth + 1, 0.0),
            isLast: isLastParallel,
          ),
        ]);
      }
      parCount++;
    }
  }

  return children;
}


// List<List<NodeWidget>> buildNodeChildrenStructureWithAlignment(
//   ProcessFlow node,
//   double depth,
//   Map<String, ProcessFlow> nodeMap,
//   List<String> currentPathForCycleDetection,
//   double alignmentOffset,
// ) {
//   List<List<NodeWidget>> children = [];
  
//   // PRECISE ALIGNMENT VALUES FOR CIRCLE POSITIONING
//   const double levelSpacing = 80.0;      // Distance between hierarchy levels
  
//   // Helper function to calculate exact circle alignment based on depth and node type
//   double calculateCircleAlignment(double currentDepth, String text, double extraOffset, {bool isParallel = false}) {
//     // Define specific circle positions for each level - aligned with dotted line centers
//     Map<double, double> baseCirclePositions = {
//       1.0: 4,   // Alt1, Alt2 level - centered with dotted line
//       2.0: 81.8,  // Parl1, Parl2 level - centered with deeper dotted line  
//       3.0: 159.8,  // Deeper nested levels
//       4.0: 243.0,  // Even deeper levels
//     };
    
//     // Get the base position for the current depth
//     double basePosition = baseCirclePositions[currentDepth] ?? (currentDepth * levelSpacing + 15.0);
    
//     // Center circles perfectly with dotted lines - no additional adjustments needed
//     return basePosition + extraOffset;
//   }

//   if (node.routeType == "Alternate" && node.conditions != null) {
//     int altCount = 1;
//     for (int i = 0; i < node.conditions!.length; i++) {
//       final cond = node.conditions![i];
//       bool isLastAlternate = (i == node.conditions!.length - 1);
//       final altText = 'Alt$altCount. ';

//       if (cond.routeTo == "Terminal") {
//         final terminalText = '${altText}X'; // Terminal without circle in text
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, alignmentOffset),
//             isLast: isLastAlternate,
//           ),
//         ]);
//       } else if (nodeMap.containsKey(cond.routeTo)) {
//         final childRows = buildNodeTreeStructureWithAlignment(
//           nodeMap[cond.routeTo]!,
//           depth + 1,
//           nodeMap,
//           List.from(currentPathForCycleDetection),
//           altText,
//           alignmentOffset, // Keep consistent alignment
//         );
        
//         // Align first child with dotted line to circle
//         if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//           final originalWidget = childRows[0][0];
//           final cleanText = originalWidget.text.replaceFirst(RegExp(r'^Alt\d+\.\s*'), '');
//           final prefixedText = '$altText$cleanText'; // Clean text without circle
          
//           childRows[0][0] = NodeWidget(
//             text: prefixedText,
//             nodeType: originalWidget.nodeType,
//             leftPadding: calculateCircleAlignment(depth + 1, prefixedText, alignmentOffset),
//             isLast: isLastAlternate,
//           );
//         }
//         children.addAll(childRows);
//       } else {
//         final terminalText = '${altText}X'; // Terminal without circle in text
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, alignmentOffset),
//             isLast: isLastAlternate,
//           ),
//         ]);
//       }
//       altCount++;
//     }
//   } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
//     final parentNodeName = node.loName!;
//     int parCount = 1;
    
//     for (int i = 0; i < node.parallelRoutes!.length; i++) {
//       final pr = node.parallelRoutes![i];
//       bool isLastParallel = (i == node.parallelRoutes!.length - 1);
//       final parlText = 'Parl$parCount. ';
      
//       if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
//         parallelParentMap['Parl$parCount'] = parentNodeName;

//         final childRows = buildNodeTreeStructureWithAlignment(
//           nodeMap[pr.routeTo]!,
//           depth + 1,
//           nodeMap,
//           List.from(currentPathForCycleDetection),
//           parlText,
//           0.0, // No additional offset for parallel routes
//         );
        
//         // Align first child with dotted line to circle
//         if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//           final originalWidget = childRows[0][0];
//           final cleanText = originalWidget.text.replaceFirst(RegExp(r'^Parl\d+\.\s*'), '');
//           final prefixedText = '$parlText$cleanText'; // Clean text without circle
          
//           childRows[0][0] = NodeWidget(
//             text: prefixedText,
//             nodeType: originalWidget.nodeType,
//             leftPadding: calculateCircleAlignment(depth + 1, prefixedText, 0.0, isParallel: true),
//             isLast: isLastParallel,
//           );
//         }
//         children.addAll(childRows);
//       } else {
//         final terminalText = '${parlText}X'; // Terminal without circle in text
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, 0.0, isParallel: true),
//             isLast: isLastParallel,
//           ),
//         ]);
//       }
//       parCount++;
//     }
//   }

//   return children;
// }

// List<List<NodeWidget>> buildNodeChildrenStructureWithAlignment(
//   ProcessFlow node,
//   double depth,
//   Map<String, ProcessFlow> nodeMap,
//   List<String> currentPathForCycleDetection,
//   double alignmentOffset,
// ) {
//   List<List<NodeWidget>> children = [];
  
//   // ADJUST THESE VALUES TO MATCH YOUR CIRCLE POSITIONS
//   const double levelSpacing = 80.0;      // Distance between hierarchy levels
//   const double circlePositionOffset = 0.0; // Adjust this to align with circles
  
//   // Helper function to calculate exact circle alignment
//   double calculateCircleAlignment(double currentDepth, String text, double extraOffset) {
//     // Method 1: If circles are at fixed positions relative to text start
//     double textBasedPosition = currentDepth * levelSpacing + extraOffset;
    
//     // Method 2: If circles are at end of text (uncomment if needed)
//     // double textWidth = text.length * 7.0; // Approximate character width
//     // double textBasedPosition = currentDepth * levelSpacing + textWidth + extraOffset;
    
//     return textBasedPosition + circlePositionOffset;
//   }

//   if (node.routeType == "Alternate" && node.conditions != null) {
//     int altCount = 1;
//     for (int i = 0; i < node.conditions!.length; i++) {
//       final cond = node.conditions![i];
//       bool isLastAlternate = (i == node.conditions!.length - 1);
//       final altText = 'Alt$altCount. ';

//       if (cond.routeTo == "Terminal") {
//         final terminalText = '${altText}X';
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, alignmentOffset),
//             isLast: isLastAlternate,
//           ),
//         ]);
//       } else if (nodeMap.containsKey(cond.routeTo)) {
//         final childRows = buildNodeTreeStructureWithAlignment(
//           nodeMap[cond.routeTo]!,
//           depth + 1,
//           nodeMap,
//           List.from(currentPathForCycleDetection),
//           altText,
//           alignmentOffset,
//         );
        
//         // Align first child with dotted line to circle
//         if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//           final originalWidget = childRows[0][0];
//           final prefixedText = altText + originalWidget.text.replaceFirst(RegExp(r'^Alt\d+\.\s*'), '');
          
//           childRows[0][0] = NodeWidget(
//             text: prefixedText,
//             nodeType: originalWidget.nodeType,
//             leftPadding: calculateCircleAlignment(depth+1, prefixedText, alignmentOffset),
//             isLast: isLastAlternate,
//           );
//         }
//         children.addAll(childRows);
//       } else {
//         final terminalText = '${altText}X';
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, alignmentOffset),
//             isLast: isLastAlternate,
//           ),
//         ]);
//       }
//       altCount++;
//     }
//   } else if (node.routeType == "Parallel" && node.parallelRoutes != null) {
//     final parentNodeName = node.loName!;
//     int parCount = 1;
    
//     for (int i = 0; i < node.parallelRoutes!.length; i++) {
//       final pr = node.parallelRoutes![i];
//       bool isLastParallel = (i == node.parallelRoutes!.length - 1);
//       final parlText = 'Parl$parCount. ';
      
//       if (pr.routeTo != "Terminal" && nodeMap.containsKey(pr.routeTo)) {
//         parallelParentMap['Parl$parCount'] = parentNodeName;

//         final childRows = buildNodeTreeStructureWithAlignment(
//           nodeMap[pr.routeTo]!,
//           depth + 1,
//           nodeMap,
//           List.from(currentPathForCycleDetection),
//           parlText,
//           0, // Reset alignment offset for parallel
//         );
        
//         // Align first child with dotted line to circle
//         if (childRows.isNotEmpty && childRows[0].isNotEmpty) {
//           final originalWidget = childRows[0][0];
//           final prefixedText = parlText + originalWidget.text.replaceFirst(RegExp(r'^Parl\d+\.\s*'), '');
          
//           childRows[0][0] = NodeWidget(
//             text: prefixedText,
//             nodeType: originalWidget.nodeType,
//             leftPadding: calculateCircleAlignment(depth+1, prefixedText, 0),
//             isLast: isLastParallel,
//           );
//         }
//         children.addAll(childRows);
//       } else {
//         final terminalText = '${parlText}X';
//         children.add([
//           NodeWidget(
//             text: terminalText,
//             nodeType: NodeType.prefixedNode,
//             leftPadding: calculateCircleAlignment(depth + 1, terminalText, 0),
//             isLast: isLastParallel,
//           ),
//         ]);
//       }
//       parCount++;
//     }
//   }

//   return children;
// }

  List<List<NodeWidget>> buildNodeTreeStructureWithAlignment(
    ProcessFlow node,
    double depth,
    Map<String, ProcessFlow> nodeMap,
    List<String> currentPathForCycleDetection,
    String branchPrefix,
    double alignmentOffset,
  ) {
    List<List<NodeWidget>> result = [];
    final nodeId = node.loId!;
    final nodeName = node.loName!;

    if (currentPathForCycleDetection.contains(nodeId)) {
      result.add([
        NodeWidget(
          text: '$branchPrefix$nodeName (cycle)',
          nodeType: NodeType.prefixedNode,
          leftPadding: depth * 20 + alignmentOffset,
        )
      ]);
      return result;
    }

    bool isFirstExpansionOfNode = !expandedNodeNames.contains(nodeName);

    currentPathForCycleDetection.add(nodeId);

    if (node.routeType == "Sequential") {
      final chainNodes = <ProcessFlow>[];
      final chainVisitedNames = <String>[];
      final chainString = buildSequentialChainString(
        node,
        nodeMap,
        chainVisitedNames,
        chainNodes,
        isFirstExpansionOfNode,
        currentPathForCycleDetection,
      );

      final rowNodes = parseSequentialChain(
          chainString, depth * 20 + alignmentOffset, branchPrefix);

      result.add(rowNodes);

      // Process children similar to main function
      if (chainNodes.isNotEmpty && !chainString.endsWith('X')) {
        final lastNodeInChain = chainNodes.last;
        final lastNodeName = lastNodeInChain.loName!;

        bool shouldExpandLastNode = !expandedNodeNames.contains(lastNodeName) &&
            isLastOccurrence(lastNodeName, currentPathForCycleDetection);

        if (shouldExpandLastNode) {
          for (final chainNode in chainNodes) {
            expandedNodeNames.add(chainNode.loName!);
          }

          final children = buildNodeChildrenStructureWithAlignment(
              lastNodeInChain,
              depth,
              nodeMap,
              List.from(currentPathForCycleDetection),
              alignmentOffset);
          result.addAll(children);
        }
      } else if (chainNodes.isNotEmpty && chainString.endsWith('X')) {
        if (chainNodes.length >= 2) {
          final lastNonTerminalNode = chainNodes[chainNodes.length - 2];
          final lastNonTerminalName = lastNonTerminalNode.loName!;

          bool shouldExpandLastNonTerminal =
              !expandedNodeNames.contains(lastNonTerminalName) &&
                  isLastOccurrence(
                      lastNonTerminalName, currentPathForCycleDetection);

          if (shouldExpandLastNonTerminal) {
            for (final chainNode in chainNodes) {
              expandedNodeNames.add(chainNode.loName!);
            }

            final chainStringUpToNode = chainString.substring(
                0,
                chainString.indexOf(lastNonTerminalName) +
                    lastNonTerminalName.length);
            final nodePositionInString = chainStringUpToNode.length;

            final children = buildNodeChildrenStructureWithAlignment(
                lastNonTerminalNode,
                depth,
                nodeMap,
                List.from(currentPathForCycleDetection),
                alignmentOffset + nodePositionInString);
            result.addAll(children);
          }
        }
      }
    } else {
      String nodeDisplay = nodeName;
      if (node.loName == "Terminal") {
        nodeDisplay += ' X';
      }

      String displayText = branchPrefix + nodeDisplay;
      NodeType nodeType = NodeType.prefixedNode;

      // Dynamic duplicate detection
      bool isDuplicate =
          _isDynamicDuplicate(nodeName, currentPathForCycleDetection);
      bool isLastOccurrenceOfNode =
          isLastOccurrence(nodeName, currentPathForCycleDetection);
      bool isExpandingNode = expandedNodeNames.contains(nodeName);

      String finalDisplayText = displayText;
      if (isDuplicate && !isLastOccurrenceOfNode) {
        if (isExpandingNode) {
          // Expanding duplicate nodes: % as prefix
          finalDisplayText = '%$displayText';
        } else {
          // Non-expanding duplicate nodes: % as suffix
          finalDisplayText = '$displayText%';
        }
      }

      // Track rendered node count
      incrementRenderedNodeCount(nodeName);

      result.add([
        NodeWidget(
          text: finalDisplayText,
          nodeType: nodeType,
          leftPadding: depth * 20 + alignmentOffset,
        )
      ]);

      expandedNodeNames.add(nodeName);
      final children = buildNodeChildrenStructureWithAlignment(node, depth,
          nodeMap, List.from(currentPathForCycleDetection), alignmentOffset);
      result.addAll(children);
    }

    currentPathForCycleDetection.removeLast();
    return result;
  }

 
Widget buildRowWidget(BuildContext context, List<NodeWidget> rowNodes, {bool isFirstRow = false}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 0.0,horizontal: 0),
    child: Row(
      // crossAxisAlignment: CrossAxisAlignment.center,
      children: rowNodes.map((node) {
        // Only the first node in the first row is the starting node
        bool isStartingNode = isFirstRow && rowNodes.indexOf(node) == 0;
        return buildNodeWidget(context, node, isStartingNode: isStartingNode);
      }).toList(),
    ),
  );
}

String addSpaceBeforeLastChar(String input) {
  // if (input.length < 2) return input; // too short, no need to modify

  return '${input.substring(0, input.length - 2)} ${input.substring(input.length - 2)}';
}

  Widget buildAlternateNodeRow(BuildContext context, String text, int altIndex,
    double leftPadding, bool isLast, {double? yOffset}) {
  // Dynamic duplicate checking
  String nodeName = _extractNodeName(text);
  bool isDuplicate = _isDuplicateFromRenderedCount(nodeName);
  bool isExpanding = _isExpandingFromContext(text, nodeName);
  Color? duplicateColor =
      isDuplicate ? getDuplicateNodeColor(nodeName) : null;

  // Check if this node has the same name as other nodes
  bool hasMultipleSameNames = hasSameName(text);
  Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(text) : null;

  String cleanText = text.contains("%") ? text.replaceAll("%", "") : text;

  // Build content with link icons for duplicates
  List<Widget> contentWidgets = [];

  if (isDuplicate && isExpanding) {
    // For expanding duplicate nodes, add link icon before text with same color as text container
    contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
    // contentWidgets.add(SizedBox(width: 1));
  }

  // Always use styled text for all text content to ensure consistent styling
    contentWidgets.add(_buildStyledPrefixText(cleanText, context));

  if (isDuplicate && !isExpanding) {
    // For non-expanding duplicate nodes, add link icon after text with same color as text container
    contentWidgets.add(SizedBox(width: 1));
    contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
  }
        final parts = (text.split('.'));
      final prefix = '${parts[0]}.';

  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 0.0),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Left padding
        SizedBox(width:leftPadding),

        // Tree connection lines container
        SizedBox(
          width: 42,
          height: 42,
          child: CustomPaint(
            painter: AlternateTreeLinePainter(
              isLast: isLast,
              isFirst: altIndex == 1,
              color: Colors.grey,
              yOffset: yOffset??0, // Add yOffset parameter
              // connectToNext: text.startsWith('Parl1')
            ),
            size: Size(42, 42),
          ),
        ),
        
          Padding(
            padding: const EdgeInsets.only(left: 4,right: 4),
            child: RichText(text: TextSpan(
                    children: [
               TextSpan(
                text: addSpaceBeforeLastChar(prefix),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  color: Colors.grey, // Colored prefix
                  fontWeight: FontManager.medium,
                ),
              ),
                    ]
                  )),
          ),
        // Circle
        Container(
          width: 7,
          height: 7,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Color(0xff666666), width: 1.0),
            color: Colors.white,
          ),
        ),

        SizedBox(width: 8),

        // Text content with link icons
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              String temp = '';
              if (nodeName.contains("Alt")) {
                temp = nodeName.substring(5);
              } else if (nodeName.contains("Parl")) {
                temp = nodeName.substring(6);
              } else {
                temp = nodeName;
              }

              showLoDetails(context, temp);
            },
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 0.0, vertical: .0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: contentWidgets,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

Widget buildTreeNodeRow(
  BuildContext context,
  String text,
  double leftPadding, {
  bool isLastParallel = false,
  double? yOffset,
  bool connectToNext = false,
}) {
  // Dynamic duplicate checking
  String nodeName = _extractNodeName(text);
  bool isDuplicate = _isDuplicateFromRenderedCount(nodeName);
  bool isExpanding = _isExpandingFromContext(text, nodeName);
  Color? duplicateColor =
      isDuplicate ? getDuplicateNodeColor(nodeName) : null;

  // Check if this node has the same name as other nodes
  bool hasMultipleSameNames = hasSameName(text);
  Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(text) : null;

  String cleanText = text.contains("%") ? text.replaceAll("%", "") : text;

  // Build content with link icons for duplicates
  List<Widget> contentWidgets = [];

  if (isDuplicate && isExpanding) {
    contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
    contentWidgets.add(SizedBox(width: 4));
  }else{
     contentWidgets.add(SizedBox(width: 4));
  }

  // Always use styled text
    contentWidgets.add(_buildStyledPrefixText(cleanText, context));

  if (isDuplicate && !isExpanding) {
    contentWidgets.add(SizedBox(width: 4));
    contentWidgets.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
  }else{
    contentWidgets.add(SizedBox(width: 4));
  }

  // Extract Parl index for first/last flags
  final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(text);
  final parlIndex = parlMatch != null ? int.parse(parlMatch.group(1)!) : 1;

     
      final parts = (text.split('.'));
      final prefix = '${parts[0]}.';
      
   
      

  return Padding(
  padding: const EdgeInsets.symmetric(vertical: 0.0),
  child: Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      // Left padding for alignment
      SizedBox(width: leftPadding),
      SizedBox(
        width: 42,
        height: 42,
        child: CustomPaint(
          painter: AlternateTreeLinePainter(
            isLast: isLastParallel,
            isFirst: parlIndex == 1,
            color: Colors.grey,
            yOffset: yOffset ?? 0.0,
            connectToNext: text.startsWith('Parl1'),
          ),
          size: const Size(42, 42),
        ),
      ),
            const SizedBox(width: 6),

      // Text between circle and connector line
      Padding(
        padding: const EdgeInsets.only(left: 0,right: 2),
        child: RichText(text: TextSpan(
          children: [
               TextSpan(
                text: addSpaceBeforeLastChar(prefix),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  color: Colors.grey, // Colored prefix
                  fontWeight: FontManager.medium,
                ),
              ),
          ]
        )),
      ),

      // const SizedBox(width: 12),
       Row(
         children: [
           Container(
            width: 7,
            height: 7,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: const Color(0xff666666), width: 1.0),
              color: Colors.white,
            ),
                 ),
         ],
       ),

      // Node label with optional icons
      MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () {
            String temp = '';
            if (nodeName.contains("Alt")) {
              temp = nodeName.substring(5);
            } else if (nodeName.contains("Parl")) {
              temp = nodeName.substring(6);
            } else {
              temp = nodeName;
            }

            showLoDetails(context, temp);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 2.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: contentWidgets,
            ),
          ),
        ),
      ),
    ],
  ),
);

}


static const double _nodeHeight = 32.0; // Standard node height
static const double _dashLength = 3.0;
static const double _dashGap = 2.0;
static const double _dashAndGap = _dashLength + _dashGap;

// Helper method to calculate synchronized yOffset for tree structure
double _calculateSynchronizedOffset(int nodeIndex) {
  return (nodeIndex * _nodeHeight) % _dashAndGap;
}


  Color? getParallelNodeColor(String text) {
    // Check if this is a parallel node (starts with "Parl")
    if (text.startsWith('Parl')) {
      // Extract the parallel number from text (e.g., "Parl1. NodeName" -> "Parl1")
      final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(text);
      if (parlMatch != null) {
        final parlKey = 'Parl${parlMatch.group(1)!}';

        // Get the parent node name from the mapping
        String? parentNodeName = parallelParentMap[parlKey];

        if (parentNodeName != null) {
          // Use the parent node name as the group key
          String groupKey = parentNodeName;

          // If this parallel parent group doesn't have a color yet, assign one
          if (!parallelGroupColors.containsKey(groupKey)) {
            parallelGroupColors[groupKey] =
                colors[parallelColorIndex % colors.length];
            parallelColorIndex++;
          }

          return parallelGroupColors[groupKey];
        }
      }
    }

    // Check if this node is the parent of a parallel group
    for (String parentNodeName in parallelParentMap.values) {
      if (text == parentNodeName || text.contains(parentNodeName)) {
        // If this is a parent node that has parallel children, give it the same color
        if (parallelGroupColors.containsKey(parentNodeName)) {
          return parallelGroupColors[parentNodeName];
        }
      }
    }

    // Check if this node is a child of a parallel group by checking if it contains parallel prefix
    for (String parlKey in parallelParentMap.keys) {
      if (text.startsWith(parlKey + '.')) {
        String? parentNodeName = parallelParentMap[parlKey];
        if (parentNodeName != null &&
            parallelGroupColors.containsKey(parentNodeName)) {
          return parallelGroupColors[parentNodeName];
        }
      }
    }

    return null;
  }

  bool isDuplicateNode(String text) {
    String nodeName = _extractNodeName(text);
    return _isDuplicateFromRenderedCount(nodeName);
  }

  bool isExpandingNode(String text) {
    String nodeName = _extractNodeName(text);
    return _isExpandingFromContext(text, nodeName);
  }

  Color getDuplicateNodeColor(String text) {
    String nodeName = _extractNodeName(text);

    // Assign a consistent color for all duplicates of this node using separate color palette
    if (!duplicateNodeColors.containsKey(nodeName)) {
      duplicateNodeColors[nodeName] =
          linkIconColors[duplicateColorIndex % linkIconColors.length];
      duplicateColorIndex++;
    }

    return duplicateNodeColors[nodeName]!;
  }

  // Get same-name color for nodes that appear multiple times
  Color? getSameNameColor(String text) {
    String nodeName = _extractNodeName(text);
    return sameNameNodeColors[nodeName];
  }

  // Check if a node has the same name as other nodes
  bool hasSameName(String text) {
    String nodeName = _extractNodeName(text);
    return sameNameGroups.containsKey(nodeName);
  }

  // Build styled text with different formatting for Alt/Parl prefixes
  Widget _buildStyledPrefixText(String text, BuildContext context) {
    // Split text into prefix and main content
    if (text.contains('.')) {
      final parts = (text.split('.'));
      final mainText = parts.sublist(1).join('. '); // Rest of the text
      
 
      
      return RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: mainText,
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                color: Colors.black87, // Normal color for main text
                fontWeight: FontManager.semiBold,
              ),
            ),
          ],
        ),
      );
    } else {
      // Fallback to normal text if no prefix found
      return Text(
        text,
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          color: Colors.black87,
          fontWeight: FontManager.semiBold,
        ),
      );
    }
  }

// Configuration map for nodes that should show only last occurrence
 Map<String, int>? showOnlyLastOccurrenceNodes = {
  'UpdateLeaveBalance': 3, // Show only last of 3 occurrences
  // Add more nodes here as needed
  // 'SomeOtherNode': 2,
};

// Check if a node should show only its last occurrence
bool _shouldShowOnlyLastOccurrence(String nodeName) {
  return (showOnlyLastOccurrenceNodes??{}).containsKey(nodeName);
}

// Get the total expected count for a node
int _getTotalExpectedCount(String nodeName) {
  return (showOnlyLastOccurrenceNodes??{})[nodeName] ?? 1;
}



Widget buildNodeWidget(BuildContext context, NodeWidget node, {bool isStartingNode = false}) {
  if (node.nodeType == NodeType.terminal) {
    return _buildTerminalIcon();
  }

  // Alt node
  if (node.nodeType == NodeType.prefixedNode && node.text.startsWith('Alt')) {
    final altMatch = RegExp(r'Alt(\d+)\.').firstMatch(node.text);
    final altIndex = altMatch != null ? int.parse(altMatch.group(1)!) : 1;

    double yOffset = _calculateSynchronizedOffset(altIndex);

    return buildAlternateNodeRow(
      context,
      node.text,
      altIndex,
      node.leftPadding,
      node.isLast,
      yOffset: yOffset,
    );
  }

  // Parl node
  if (node.nodeType == NodeType.prefixedNode && node.text.startsWith('Parl')) {
    final parlMatch = RegExp(r'Parl(\d+)\.').firstMatch(node.text);
    final parlIndex = parlMatch != null ? int.parse(parlMatch.group(1)!) : 1;

    double yOffset = _calculateSynchronizedOffset(parlIndex);
    final bool connectToNext = !node.isLast;

    return buildTreeNodeRow(
      context,
      node.text,
      node.leftPadding,
      isLastParallel: node.isLast,
      yOffset: yOffset,
      connectToNext: connectToNext,
    );
  }

  String nodeName = _extractNodeName(node.text);
  
  // Filter nodes that should show only last occurrence
  if (_shouldShowOnlyLastOccurrence(nodeName)) {
    _nodeRenderCounts[nodeName] = (_nodeRenderCounts[nodeName] ?? 0) + 1;
    
    // Get the total expected count for this node type
    int totalExpectedCount = _getTotalExpectedCount(nodeName);
    
    // Hide all occurrences except the last one
    if (_nodeRenderCounts[nodeName]! < totalExpectedCount) {
      return const SizedBox.shrink();
    }
  }
  
  _nodeRenderCounts[nodeName] = (_nodeRenderCounts[nodeName] ?? 0) + 1;

  if (_nodeRenderCounts[nodeName] == 1 && !isStartingNode) {
    return const SizedBox.shrink();
  }

  // Duplicate styling logic
  bool isDuplicate = isDuplicateNode(node.text);
  bool isExpanding = isExpandingNode(node.text);
  Color? duplicateColor = isDuplicate ? getDuplicateNodeColor(node.text) : null;
  bool hasMultipleSameNames = hasSameName(node.text);
  Color? sameNameColor = hasMultipleSameNames ? getSameNameColor(node.text) : null;

  // Override duplicate/expanding logic for nodes that should show only last occurrence
  if (_shouldShowOnlyLastOccurrence(nodeName)) {
    isDuplicate = false;
    isExpanding = false;
  }

  List<Widget> nodeContent = [];

  if (isDuplicate && isExpanding) {
    nodeContent.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
    nodeContent.add(SizedBox(width: 2));
  }

  String cleanText = node.text.replaceAll("%", "");
  Widget textWidget;

  if (cleanText.startsWith('Alt') || cleanText.startsWith('Parl')) {
      textWidget = _buildStyledPrefixText(cleanText, context);
  } else {
    textWidget = Text(
      cleanText,
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: ResponsiveFontSizes.bodyMedium(context),
        color: Colors.black,
          fontWeight: FontManager.semiBold,
      ),
    );
  }

  if (hasMultipleSameNames && sameNameColor != null) {
    textWidget = Container(
      padding: EdgeInsets.symmetric(horizontal: 4.0, vertical: 0.0),
      decoration: BoxDecoration(
        color: sameNameColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: sameNameColor.withOpacity(0.6),
          width: 1.0,
        ),
      ),
      child: textWidget,
    );
  }

  nodeContent.add(textWidget);

  if (isDuplicate && !isExpanding) {
    nodeContent.add(_buildLinkIcon(boxColor: sameNameColor ?? duplicateColor));
  }

  return MouseRegion(
    cursor: SystemMouseCursors.click,
    child: GestureDetector(
      onTap: () {
        showLoDetails(context, nodeName);
      },
      child: Container(
        margin: EdgeInsets.only(
          left: node.leftPadding,
          right: 0.0,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 6.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isStartingNode) ...[
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.grey,
                    width: 1.5,
                  ),
                ),
              ),
              SizedBox(width: 6),
            ] else if (node.leftPadding == 0) ...[
              SizedBox(width: 12),
            ],
            ...nodeContent,
          ],
        ),
      ),
    ),
  );
}

}

// Build terminal icon for workflow end
Widget _buildTerminalIcon() {
  return Container(
    padding: EdgeInsets.only(left: 6),
    decoration:
        BoxDecoration(borderRadius: BorderRadius.circular(4),),
    // padding: EdgeInsets.all(4),
    child: SvgPicture.asset(
      'assets/images/workflow/terminal_icon.svg',
      width: 16,
      height: 16,
      fit: BoxFit.fill,
      // colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
    ),
  );
}

// Build link icon for connections
Widget _buildLinkIcon({Color? boxColor}) {
  return Container(
    decoration:
        BoxDecoration(borderRadius: BorderRadius.circular(4), color: boxColor),
    // padding: EdgeInsets.all(4),
    child: SvgPicture.asset(
      'assets/images/workflow/link_icon.svg',
      width: 16,
      height: 16,
      fit: BoxFit.fill,
      colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
    ),
  );
}

showLoDetails(BuildContext context, String text) {
  final provider = Provider.of<ManualCreationProvider>(context, listen: false);
  if (provider.extractedWorkflowData != null) {
    List? loList = provider
        .extractedWorkflowData?.first['workFlowDetails']?.localObjectivesList;
    if (loList != null && loList.isNotEmpty) {
      dynamic element = loList.firstWhere(
        (element) => element.loName == text,
      );
      final index = loList.indexOf(element);
      provider.showWorkflowLoDetailsPanel(loList[index].loDetails);
    }
  }
}




// Custom painter for alternate node tree lines

class AlternateTreeLinePainter extends CustomPainter {
  final bool isLast;
  final bool isFirst;
  final Color color;
  final double yOffset;
  final bool connectToNext;

  AlternateTreeLinePainter({
    required this.isLast,
    required this.isFirst,
    required this.color,
    this.yOffset = 0.0,
    this.connectToNext = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final double centerX = 0.0;
    final double centerY = size.height / 2;

    // Top to center
    _drawDottedLine(canvas, paint, Offset(centerX, -20), Offset(centerX, centerY), yOffset);

    // Center to bottom if not last
    if (!isLast) {
      _drawDottedLine(canvas, paint, Offset(centerX, centerY), Offset(centerX, size.height), yOffset);
    }

    // Center to right
    _drawDottedLine(canvas, paint, Offset(centerX, centerY), Offset(size.width, centerY), yOffset);


    if (connectToNext) {
      final bluePaint = Paint()..color=color
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;

      final double startY = size.height;
      final double endY = size.height + 70; // Adjust based on your node gap
      _drawDottedLine(canvas, bluePaint, Offset(centerX, startY), Offset(centerX, endY), 0);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;

  void _drawDottedLine(Canvas canvas, Paint paint, Offset start, Offset end, double phaseOffset) {
    const double dashLength = 5.0;
    const double dashGap = 3.0;
    const double dashAndGap = dashLength + dashGap;

    final double totalLength = (end - start).distance;
    if (totalLength == 0) return;

    final Offset direction = (end - start) / totalLength;
    double current = -phaseOffset % dashAndGap;

    while (current < totalLength) {
      final double startDistance = current.clamp(0, totalLength);
      final double endDistance = (current + dashLength).clamp(0, totalLength);

      if (endDistance > startDistance) {
        final Offset dashStart = start + direction * startDistance;
        final Offset dashEnd = start + direction * endDistance;
        canvas.drawLine(dashStart, dashEnd, paint);
      }

      current += dashAndGap;
    }
  }
}

