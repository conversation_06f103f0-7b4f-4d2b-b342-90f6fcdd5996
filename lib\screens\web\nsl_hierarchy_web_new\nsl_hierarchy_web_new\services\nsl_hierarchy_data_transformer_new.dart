

import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_hierarchy_model_new.dart';

class NslHierarchyDataTransformerNew {
  
  /// Transforms API nodes to NSLHierarchyData1 objects and builds the tree structure
  static NSLNode? transformApiDataToHierarchy(NslTreeHierarchy apiResponse) {
    if (apiResponse.result?.nodes == null || apiResponse.result!.nodes!.isEmpty) {
      return null;
    }

    final apiNodes = apiResponse.result!.nodes!;
    final Map<String, NSLHierarchyData1> transformedNodes = {};
    
    // Transform each API node to NSLHierarchyData1
    for (final apiNode in apiNodes) {
      final transformedNode = _transformSingleNode(apiNode);
      if (transformedNode != null) {
        transformedNodes[transformedNode.id] = transformedNode;
      }
    }

    // Find root node (M4 level)
    NSLHierarchyData1? rootNode;
    for (final node in transformedNodes.values) {
      if (node.level == 'M4') {
        rootNode = node;
        break;
      }
    }

    if (rootNode == null) {
      // Try to find root by looking for node with no parents or the "owner" node
      for (final node in transformedNodes.values) {
        if (node.id == 'owner' || node.id == '1' || (node.parent == null || node.parent!.isEmpty)) {
          print('Using node ${node.id} as root node');
          rootNode = node;
          break;
        }
      }
    }

    if (rootNode == null) {
      print('No root node found in API data');
      return null;
    }

    // Build parent-child relationships
    final completeTree = _buildTreeStructure(rootNode, transformedNodes);
    return NSLNode.fromNSLData(completeTree);
  }

  /// Transform a single API node to NSLHierarchyData1
  static NSLHierarchyData1? _transformSingleNode(Node apiNode) {
    if (apiNode.id == null || apiNode.displayId == null || apiNode.level == null) {
      return null;
    }

    // Map level enum to string
    final levelString = apiNode.level!.name;
    
    // Create level name based on level and node type
    String levelName;
    switch (levelString) {
      case 'M4':
        levelName = 'Executive Level';
        break;
      case 'M3':
        levelName = 'Department Level';
        break;
      case 'M2':
        levelName = 'Team Level';
        break;
      case 'M1':
        levelName = 'Individual Level';
        break;
      default:
        levelName = 'Unknown Level';
    }

    // Get parent ID (first parent if multiple)
    String? parentId;
    if (apiNode.parentIds != null && apiNode.parentIds!.isNotEmpty) {
      parentId = apiNode.parentIds!.first;
    }

    // Create default financial summary using npValue
    final financialSummary = FinancialSummary(
      revenue: '\$${(apiNode.npValue ?? 0) * 1000}', // Mock revenue calculation
      cost: '\$${(apiNode.npValue ?? 0) * 800}', // Mock cost calculation
      margin: '${((apiNode.npValue ?? 0) * 0.2).toStringAsFixed(1)}%', // Mock margin
    );

    // Create default bet breakdown
    final betBreakdown = BetBreakdown(
      gos: apiNode.level?.name == 'M4' ? 45 : (apiNode.level?.name == 'M3' ? 15 : 5),
      los: apiNode.level?.name == 'M4' ? 234 : (apiNode.level?.name == 'M3' ? 78 : 26),
      npFunctions: apiNode.npValue ?? 1200,
      inputOutputStacks: 10,
      subordinateNsl: apiNode.children?.length ?? 0,
    );

    // Create node metrics based on level
    NodeMetrics? metrics;
    switch (levelString) {
      case 'M4':
        metrics = NodeMetrics(
          m3Nodes: apiNode.children?.length ?? 0,
          totalGos: 45,
          totalLos: 234,
          transactions: '15,234',
          betEfficiency: '39.2%',
        );
        break;
      case 'M3':
        metrics = NodeMetrics(
          m2Nodes: apiNode.children?.length ?? 0,
          gos: 15,
          los: 78,
        );
        break;
      case 'M2':
        metrics = NodeMetrics(
          m1Employees: apiNode.children?.length ?? 0,
          teamEfficiency: '85.5%',
        );
        break;
      case 'M1':
        metrics = NodeMetrics(
          localObjectives: 12,
          personalBets: 8,
          loEfficiency: '92.3%',
        );
        break;
    }

    return NSLHierarchyData1(
      id: apiNode.id!, // Use original API id for internal relationships
      title: apiNode.name ?? apiNode.displayId ?? 'Unknown', // Use name field as the title shown in tree
      type: apiNode.nodeType?.name ?? 'MODULE',
      parent: parentId,
      totalBets: apiNode.npValue ?? 1200, // Use npValue for NP field
      betBreakdown: betBreakdown,
      financialSummary: financialSummary,
      metrics: metrics,
      children: apiNode.children ?? [],
      levelName: levelName,
      level: levelString,
      isExpanded: levelString == 'M4', // Only M4 expanded by default
      employeeId: apiNode.displayId, // Store displayId for access in UI
      childNodes: const [], // Will be populated in tree building
    );
  }

  /// Build the complete tree structure with parent-child relationships
  static NSLHierarchyData1 _buildTreeStructure(
    NSLHierarchyData1 rootNode,
    Map<String, NSLHierarchyData1> allNodes,
  ) {
    // Create a map to track children for each node
    final Map<String, List<NSLHierarchyData1>> childrenMap = {};
    
    // Initialize children map
    for (final node in allNodes.values) {
      childrenMap[node.id] = [];
    }

    // Build parent-child relationships
    for (final node in allNodes.values) {
      for (final childId in node.children) {
        if (allNodes.containsKey(childId)) {
          childrenMap[node.id]!.add(allNodes[childId]!);
        }
      }
    }

    // Recursively build the tree
    NSLHierarchyData1 buildNodeWithChildren(NSLHierarchyData1 node) {
      final children = childrenMap[node.id] ?? [];
      final childNodes = children.map((child) => buildNodeWithChildren(child)).toList();
      return node.copyWith(childNodes: childNodes);
    }

    return buildNodeWithChildren(rootNode);
  }
}
