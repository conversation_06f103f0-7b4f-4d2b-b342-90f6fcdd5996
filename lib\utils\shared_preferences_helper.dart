import 'package:shared_preferences/shared_preferences.dart';
import 'package:nsl/utils/logger.dart';

/// # SharedPreferences Helper Utility
/// 
/// A centralized utility class for managing all SharedPreferences operations.
/// This class provides a consistent interface for storing and retrieving
/// user preferences across the application.
/// 
/// ## Features:
/// - Centralized key management
/// - Type-safe getter/setter methods
/// - Error handling and logging
/// - Consistent API across the app
/// 
/// ## Usage:
/// ```dart
/// // Save data
/// await SharedPreferencesHelper.setSelectedModel('claude-3');
/// await SharedPreferencesHelper.setUserTheme('dark');
/// 
/// // Load data
/// String? modelId = await SharedPreferencesHelper.getSelectedModel();
/// String? theme = await SharedPreferencesHelper.getUserTheme();
/// ```
/// 
/// <AUTHOR> Development Team
/// @version 1.0.0
/// @since 2024
class SharedPreferencesHelper {
  // ============================================================================
  // PREFERENCE KEYS
  // ============================================================================
  
  /// Model selection preferences
  static const String _selectedModelKey = 'selected_model_id';

  static const String _selectedModelAPIKey = 'selected_model_api_key';
  
  /// User interface preferences
  static const String _userThemeKey = 'user_theme';
  static const String _languageKey = 'user_language';
  static const String _fontSizeKey = 'user_font_size';
  
  /// Chat preferences
  static const String _chatHistoryEnabledKey = 'chat_history_enabled';
  static const String _autoSaveChatsKey = 'auto_save_chats';
  static const String _defaultChatModeKey = 'default_chat_mode';
  
  /// User session preferences
  static const String _lastLoginTimeKey = 'last_login_time';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  
  /// Application settings
  static const String _firstLaunchKey = 'first_launch';
  static const String _appVersionKey = 'app_version';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _analyticsEnabledKey = 'analytics_enabled';
  
  /// Development and debug settings
  static const String _debugModeKey = 'debug_mode';
  static const String _apiEndpointKey = 'api_endpoint';
  static const String _logLevelKey = 'log_level';

  // ============================================================================
  // CORE HELPER METHODS
  // ============================================================================
  
  /// Get SharedPreferences instance with error handling
  static Future<SharedPreferences?> _getPrefs() async {
    try {
      return await SharedPreferences.getInstance();
    } catch (e) {
      Logger.error('Failed to get SharedPreferences instance: $e');
      return null;
    }
  }
  
  /// Generic method to save string value
  static Future<bool> _setString(String key, String value) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.setString(key, value);
        Logger.info('Saved preference: $key = $value');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error saving string preference $key: $e');
      return false;
    }
  }
  
  /// Generic method to get string value
  static Future<String?> _getString(String key, {String? defaultValue}) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final value = prefs.getString(key) ?? defaultValue;
        Logger.info('Loaded preference: $key = $value');
        return value;
      }
      return defaultValue;
    } catch (e) {
      Logger.error('Error loading string preference $key: $e');
      return defaultValue;
    }
  }
  
  /// Generic method to save boolean value
  static Future<bool> _setBool(String key, bool value) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.setBool(key, value);
        Logger.info('Saved preference: $key = $value');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error saving boolean preference $key: $e');
      return false;
    }
  }
  
  /// Generic method to get boolean value
  static Future<bool> _getBool(String key, {bool defaultValue = false}) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final value = prefs.getBool(key) ?? defaultValue;
        Logger.info('Loaded preference: $key = $value');
        return value;
      }
      return defaultValue;
    } catch (e) {
      Logger.error('Error loading boolean preference $key: $e');
      return defaultValue;
    }
  }
  
  /// Generic method to save integer value
  static Future<bool> _setInt(String key, int value) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.setInt(key, value);
        Logger.info('Saved preference: $key = $value');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error saving integer preference $key: $e');
      return false;
    }
  }
  
  /// Generic method to get integer value
  static Future<int> _getInt(String key, {int defaultValue = 0}) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final value = prefs.getInt(key) ?? defaultValue;
        Logger.info('Loaded preference: $key = $value');
        return value;
      }
      return defaultValue;
    } catch (e) {
      Logger.error('Error loading integer preference $key: $e');
      return defaultValue;
    }
  }
  
  /// Generic method to save double value
  static Future<bool> _setDouble(String key, double value) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.setDouble(key, value);
        Logger.info('Saved preference: $key = $value');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error saving double preference $key: $e');
      return false;
    }
  }
  
  /// Generic method to get double value
  static Future<double> _getDouble(String key, {double defaultValue = 0.0}) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final value = prefs.getDouble(key) ?? defaultValue;
        Logger.info('Loaded preference: $key = $value');
        return value;
      }
      return defaultValue;
    } catch (e) {
      Logger.error('Error loading double preference $key: $e');
      return defaultValue;
    }
  }

  // ============================================================================
  // MODEL SELECTION PREFERENCES
  // ============================================================================
  
  /// Save the selected AI model ID
  static Future<bool> setSelectedModel(String modelId) async {
    return await _setString(_selectedModelKey, modelId);
  }
  
  /// Get the selected AI model ID
  static Future<String?> getSelectedModel() async {
    return await _getString(_selectedModelKey);
  }

  // ============================================================================
  // USER INTERFACE PREFERENCES
  // ============================================================================
  
  /// Save user theme preference (light/dark/system)
  static Future<bool> setUserTheme(String theme) async {
    return await _setString(_userThemeKey, theme);
  }
  
  /// Get user theme preference
  static Future<String?> getUserTheme() async {
    return await _getString(_userThemeKey, defaultValue: 'system');
  }
  
  /// Save user language preference
  static Future<bool> setUserLanguage(String languageCode) async {
    return await _setString(_languageKey, languageCode);
  }
  
  /// Get user language preference
  static Future<String?> getUserLanguage() async {
    return await _getString(_languageKey, defaultValue: 'en');
  }
  
  /// Save user font size preference
  static Future<bool> setUserFontSize(double fontSize) async {
    return await _setDouble(_fontSizeKey, fontSize);
  }
  
  /// Get user font size preference
  static Future<double> getUserFontSize() async {
    return await _getDouble(_fontSizeKey, defaultValue: 14.0);
  }

  // ============================================================================
  // CHAT PREFERENCES
  // ============================================================================
  
  /// Save chat history enabled preference
  static Future<bool> setChatHistoryEnabled(bool enabled) async {
    return await _setBool(_chatHistoryEnabledKey, enabled);
  }
  
  /// Get chat history enabled preference
  static Future<bool> getChatHistoryEnabled() async {
    return await _getBool(_chatHistoryEnabledKey, defaultValue: true);
  }
  
  /// Save auto-save chats preference
  static Future<bool> setAutoSaveChats(bool enabled) async {
    return await _setBool(_autoSaveChatsKey, enabled);
  }
  
  /// Get auto-save chats preference
  static Future<bool> getAutoSaveChats() async {
    return await _getBool(_autoSaveChatsKey, defaultValue: true);
  }
  
  /// Save default chat mode
  static Future<bool> setDefaultChatMode(String mode) async {
    return await _setString(_defaultChatModeKey, mode);
  }
  
  /// Get default chat mode
  static Future<String?> getDefaultChatMode() async {
    return await _getString(_defaultChatModeKey, defaultValue: 'normal');
  }

  // ============================================================================
  // USER SESSION PREFERENCES
  // ============================================================================
  
  /// Save last login timestamp
  static Future<bool> setLastLoginTime(int timestamp) async {
    return await _setInt(_lastLoginTimeKey, timestamp);
  }
  
  /// Get last login timestamp
  static Future<int> getLastLoginTime() async {
    return await _getInt(_lastLoginTimeKey);
  }
  
  /// Save user ID
  static Future<bool> setUserId(String userId) async {
    return await _setString(_userIdKey, userId);
  }
  
  /// Get user ID
  static Future<String?> getUserId() async {
    return await _getString(_userIdKey);
  }
  
  /// Save user name
  static Future<bool> setUserName(String userName) async {
    return await _setString(_userNameKey, userName);
  }
  
  /// Get user name
  static Future<String?> getUserName() async {
    return await _getString(_userNameKey);
  }
  
  /// Save user email
  static Future<bool> setUserEmail(String email) async {
    return await _setString(_userEmailKey, email);
  }
  
  /// Get user email
  static Future<String?> getUserEmail() async {
    return await _getString(_userEmailKey);
  }

  // ============================================================================
  // APPLICATION SETTINGS
  // ============================================================================
  
  /// Save first launch flag
  static Future<bool> setFirstLaunch(bool isFirstLaunch) async {
    return await _setBool(_firstLaunchKey, isFirstLaunch);
  }
  
  /// Get first launch flag
  static Future<bool> getFirstLaunch() async {
    return await _getBool(_firstLaunchKey, defaultValue: true);
  }
  
  /// Save app version
  static Future<bool> setAppVersion(String version) async {
    return await _setString(_appVersionKey, version);
  }
  
  /// Get app version
  static Future<String?> getAppVersion() async {
    return await _getString(_appVersionKey);
  }
  
  /// Save notifications enabled preference
  static Future<bool> setNotificationsEnabled(bool enabled) async {
    return await _setBool(_notificationsEnabledKey, enabled);
  }
  
  /// Get notifications enabled preference
  static Future<bool> getNotificationsEnabled() async {
    return await _getBool(_notificationsEnabledKey, defaultValue: true);
  }
  
  /// Save analytics enabled preference
  static Future<bool> setAnalyticsEnabled(bool enabled) async {
    return await _setBool(_analyticsEnabledKey, enabled);
  }
  
  /// Get analytics enabled preference
  static Future<bool> getAnalyticsEnabled() async {
    return await _getBool(_analyticsEnabledKey, defaultValue: true);
  }

  // ============================================================================
  // DEVELOPMENT AND DEBUG SETTINGS
  // ============================================================================
  
  /// Save debug mode preference
  static Future<bool> setDebugMode(bool enabled) async {
    return await _setBool(_debugModeKey, enabled);
  }
  
  /// Get debug mode preference
  static Future<bool> getDebugMode() async {
    return await _getBool(_debugModeKey, defaultValue: false);
  }
  
  /// Save API endpoint
  static Future<bool> setApiEndpoint(String endpoint) async {
    return await _setString(_apiEndpointKey, endpoint);
  }
  
  /// Get API endpoint
  static Future<String?> getApiEndpoint() async {
    return await _getString(_apiEndpointKey);
  }
  
  /// Save log level
  static Future<bool> setLogLevel(String level) async {
    return await _setString(_logLevelKey, level);
  }
  
  /// Get log level
  static Future<String?> getLogLevel() async {
    return await _getString(_logLevelKey, defaultValue: 'info');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Clear all preferences (useful for logout or reset)
  static Future<bool> clearAll() async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.clear();
        Logger.info('Cleared all preferences');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error clearing all preferences: $e');
      return false;
    }
  }
  
  /// Remove a specific preference
  static Future<bool> remove(String key) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        final result = await prefs.remove(key);
        Logger.info('Removed preference: $key');
        return result;
      }
      return false;
    } catch (e) {
      Logger.error('Error removing preference $key: $e');
      return false;
    }
  }
  
  /// Check if a preference exists
  static Future<bool> containsKey(String key) async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        return prefs.containsKey(key);
      }
      return false;
    } catch (e) {
      Logger.error('Error checking preference key $key: $e');
      return false;
    }
  }
  
  /// Get all preference keys
  static Future<Set<String>> getAllKeys() async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        return prefs.getKeys();
      }
      return <String>{};
    } catch (e) {
      Logger.error('Error getting all preference keys: $e');
      return <String>{};
    }
  }
  
  /// Clear user session data (useful for logout)
  static Future<bool> clearUserSession() async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        await prefs.remove(_userIdKey);
        await prefs.remove(_userNameKey);
        await prefs.remove(_userEmailKey);
        await prefs.remove(_lastLoginTimeKey);
        Logger.info('Cleared user session data');
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error clearing user session data: $e');
      return false;
    }
  }
  
  /// Reset to default settings (keeps user session)
  static Future<bool> resetToDefaults() async {
    try {
      final prefs = await _getPrefs();
      if (prefs != null) {
        // Save user session data
        final userId = await getUserId();
        final userName = await getUserName();
        final userEmail = await getUserEmail();
        final lastLogin = await getLastLoginTime();
        
        // Clear all
        await prefs.clear();
        
        // Restore user session data
        if (userId != null) await setUserId(userId);
        if (userName != null) await setUserName(userName);
        if (userEmail != null) await setUserEmail(userEmail);
        if (lastLogin > 0) await setLastLoginTime(lastLogin);
        
        Logger.info('Reset preferences to defaults');
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error resetting preferences to defaults: $e');
      return false;
    }
  }

  static setSelectedModelApiKey(String apiKey) async {
    return await _setString(_selectedModelAPIKey, apiKey);
  }

  static Future<String?> getSelectedModelApiKey() async {
    return await _getString(_selectedModelAPIKey,defaultValue: "claude");
  }
}
