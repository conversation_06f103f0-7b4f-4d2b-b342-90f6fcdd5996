import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/go_lo_list_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/nsl_hierarchy_provider_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/services/nsl_hierarchy_api_service_new.dart';
import 'package:provider/provider.dart';

import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class EnhancedGoLoListPanelNew extends StatelessWidget {
  final String panelId;
  final String metricType; // 'GOs' or 'LOs' to determine display mode
  final VoidCallback onClose;

  const EnhancedGoLoListPanelNew({
    super.key,
    required this.panelId,
    required this.metricType,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProviderNew>(
      builder: (context, provider, child) {
        return _EnhancedGoLoListPanelNewView(
          panelId: panelId,
          metricType: metricType,
          onClose: onClose,
          provider: provider,
        );
      },
    );
  }
}

class _EnhancedGoLoListPanelNewView extends StatefulWidget {
  final String panelId;
  final String metricType;
  final VoidCallback onClose;
  final NslHierarchyProviderNew provider;

  const _EnhancedGoLoListPanelNewView({
    required this.panelId,
    required this.metricType,
    required this.onClose,
    required this.provider,
  });

  @override
  State<_EnhancedGoLoListPanelNewView> createState() => _EnhancedGoLoListPanelNewViewState();
}

class _EnhancedGoLoListPanelNewViewState extends State<_EnhancedGoLoListPanelNewView> {
  final ScrollController _scrollController = ScrollController();

  // Data management
  List<Datum> _allGos = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize = 50;
  String? _errorMessage;

  // Expansion states for hierarchy
  Map<String, bool> _goExpandedStates = {};
  Map<String, bool> _loExpandedStates = {};
  Map<String, bool> _entityExpandedStates = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadInitialData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // Get the selected node ID from the provider, fallback to 'owner' if none selected
      final selectedNodeId = widget.provider.selectedNodeId ?? 'owner';
      
      final result = await NslHierarchyApiServiceNew.fetchGoLoList(
        nodeId: selectedNodeId,
        pageNumber: 1,
        pageSize: _pageSize,
      );

      if (result != null && result.result != null) {
        if (mounted) {
          setState(() {
            _allGos = result.result!.data ?? [];
            _currentPage = result.result!.pageNumber ?? 1;
            _hasMoreData = (_currentPage < (result.result!.totalPages ?? 0));
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load GO/LO data';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error loading data: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      // Get the selected node ID from the provider, fallback to 'owner' if none selected
      final selectedNodeId = widget.provider.selectedNodeId ?? 'owner';
      
      final result = await NslHierarchyApiServiceNew.fetchGoLoList(
        nodeId: selectedNodeId,
        pageNumber: _currentPage + 1,
        pageSize: _pageSize,
      );

      if (result != null && result.result != null) {
        if (mounted) {
          setState(() {
            _allGos.addAll(result.result!.data ?? []);
            _currentPage = result.result!.pageNumber ?? _currentPage;
            _hasMoreData = (_currentPage < (result.result!.totalPages ?? 0));
            _isLoadingMore = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
      print('Error loading more data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.20,
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFD0D0D0)),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.sm, vertical: AppSpacing.size10),
      decoration: BoxDecoration(
        color: Color(0XFFCBDDFF),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.metricType == 'GOs' ? 'GO List' : 'LO List',
              textAlign: TextAlign.center,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: widget.onClose,
            child: Container(
              child: Icon(
                Icons.close,
                size: 24,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red),
              SizedBox(height: AppSpacing.md),
              Text(
                _errorMessage!,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppSpacing.md),
              ElevatedButton(
                onPressed: _loadInitialData,
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_allGos.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No GO/LO data available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    // Show different content based on metricType
    if (widget.metricType == 'LOs') {
      return _buildFlatLoList();
    } else {
      // Default GO hierarchy view
      return ListView.builder(
        controller: _scrollController,
        itemCount: _allGos.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _allGos.length) {
            // Loading indicator at the bottom
            return Container(
              padding: EdgeInsets.all(AppSpacing.md),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final go = _allGos[index];
          return Column(
            children: [
              _buildGoItem(go, index),
              if (index != _allGos.length - 1)
                Divider(
                  height: 1,
                  thickness: 1,
                  color: Colors.grey.shade300,
                ),
            ],
          );
        },
      );
    }
  }

  Widget _buildGoItem(Datum go, int index) {
    final goId = go.id ?? 'go_$index';
    final isGoExpanded = _goExpandedStates[goId] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (mounted) {
                setState(() {
                  // 1. Collapse all other GOs first
                  _goExpandedStates.forEach((key, value) {
                    if (key != goId) {
                      _goExpandedStates[key] = false;
                      // Clear their LO/entity states (optional)
                      _loExpandedStates
                          .removeWhere((loKey, _) => loKey.startsWith(key));
                      _entityExpandedStates.removeWhere(
                          (entityKey, _) => entityKey.startsWith(key));
                    }
                  });

                  // 2. Toggle current GO
                  _goExpandedStates[goId] = !isGoExpanded;

                  // 3. If collapsing, reset its LO/entity states
                  if (!_goExpandedStates[goId]!) {
                    _loExpandedStates
                        .removeWhere((key, _) => key.startsWith(goId));
                    _entityExpandedStates
                        .removeWhere((key, _) => key.startsWith(goId));
                  }
                });
              }
            },
            child: Container(
              margin: EdgeInsets.only(
                left: AppSpacing.md,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
                right: AppSpacing.sm,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${index + 1}. ${go.name ?? 'GO Title'}',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: isGoExpanded
                            ? FontManager.bold
                            : FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isGoExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isGoExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isGoExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (go.los != null && go.los!.isNotEmpty)
                    ...go.los!.asMap().entries.map((loEntry) {
                      final loIndex = loEntry.key;
                      final lo = loEntry.value;
                      return Column(
                        children: [
                          _buildLoItem(lo, loIndex, goId),
                          if (loIndex != go.los!.length - 1)
                            Divider(
                                height: 1,
                                thickness: 1,
                                color: Colors.grey.shade300),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No LOs available',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s11,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoItem(Lo lo, int loIndex, String goId) {
    final loId = '${goId}_${lo.id ?? 'lo_$loIndex'}';
    final isLoExpanded = _loExpandedStates[loId] ?? false;

    return Container(
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (mounted) {
                setState(() {
                  // Collapse all other LOs in this GO first
                  _loExpandedStates.forEach((key, value) {
                    if (key.startsWith(goId) && key != loId) {
                      _loExpandedStates[key] = false;
                    }
                  });
                  _loExpandedStates[loId] = !isLoExpanded;
                });
              }
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: AppSpacing.md),
                      child: Text(
                        '${loIndex + 1}. ${lo.name ?? 'LO Title'}',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: isLoExpanded
                              ? FontManager.semiBold
                              : FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  Icon(
                    isLoExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isLoExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isLoExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (lo.entities != null && lo.entities!.isNotEmpty)
                    ...lo.entities!.asMap().entries.map((entityEntry) {
                      final entityIndex = entityEntry.key;
                      final entity = entityEntry.value;
                      return Column(
                        children: [
                          if (entityIndex > 0)
                            Divider(
                                height: 1,
                                thickness: 1,
                                color: Colors.grey.shade300),
                          _buildEntityItem(entity, entityIndex, loId),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No Entities available',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEntityItem(Entity entity, int entityIndex, String loId) {
    final entityId = '${loId}_${entity.id ?? 'entity_$entityIndex'}';
    final isEntityExpanded = _entityExpandedStates[entityId] ?? false;

    return Container(
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (mounted) {
                setState(() {
                  // Collapse all other entities in this LO first
                  _entityExpandedStates.forEach((key, value) {
                    if (key.startsWith(loId) && key != entityId) {
                      _entityExpandedStates[key] = false;
                    }
                  });

                  _entityExpandedStates[entityId] = !isEntityExpanded;
                });
              }
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: AppSpacing.lg),
                      child: Text(
                        '${entityIndex + 1}. ${entity.name ?? 'Entity Title'}',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: isEntityExpanded
                              ? FontManager.semiBold
                              : FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  Icon(
                    isEntityExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isEntityExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isEntityExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (entity.attributes != null &&
                      entity.attributes!.isNotEmpty)
                    ...entity.attributes!.asMap().entries.map((attributeEntry) {
                      final attributeIndex = attributeEntry.key;
                      final attribute = attributeEntry.value;
                      return Column(
                        children: [
                          if (attributeIndex > 0)
                            Divider(
                                height: 1,
                                thickness: 1,
                                color: Colors.grey.shade300),
                          _buildAttributeItem(attribute, attributeIndex),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No Attributes available',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAttributeItem(Attribute attribute, int attributeIndex) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: AppSpacing.xs, horizontal: AppSpacing.xl),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: AppSpacing.md),
              child: Text(
                '${attributeIndex + 1}. ${attribute.name ?? 'Attribute Name'}',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build flat list of LOs (without GO hierarchy)
  Widget _buildFlatLoList() {
    List<Lo> allLos = [];

    // Collect all LOs from all GOs
    for (var go in _allGos) {
      if (go.los != null && go.los!.isNotEmpty) {
        allLos.addAll(go.los!);
      }
    }

    if (allLos.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No LOs available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: allLos.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == allLos.length) {
          // Loading indicator at the bottom
          return Container(
            padding: EdgeInsets.all(AppSpacing.md),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final lo = allLos[index];
        return Column(
          children: [
            _buildFlatLoItem(lo, index),
            if (index != allLos.length - 1)
              Divider(
                height: 1,
                thickness: 1,
                color: Colors.grey.shade300,
              ),
          ],
        );
      },
    );
  }

  // Build individual LO item for flat list
  Widget _buildFlatLoItem(Lo lo, int index) {
    final loId = 'flat_${lo.id ?? 'lo_$index'}';
    final isLoExpanded = _loExpandedStates[loId] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (mounted) {
                setState(() {
                  // 1. Collapse all other LOs first (flat list behavior)
                  _loExpandedStates.forEach((key, value) {
                    if (key != loId) {
                      _loExpandedStates[key] = false;
                      // (Optional) Clear their entity states to save memory
                      _entityExpandedStates.removeWhere(
                          (entityKey, _) => entityKey.startsWith(key));
                    }
                  });

                  // 2. Toggle current LO
                  _loExpandedStates[loId] = !isLoExpanded;

                  // 3. If collapsing, reset its entity states (clean slate)
                  if (!_loExpandedStates[loId]!) {
                    _entityExpandedStates
                        .removeWhere((key, _) => key.startsWith(loId));
                  }
                });
              }
            },
            child: Container(
              margin: EdgeInsets.only(
                left: AppSpacing.md,
                top: AppSpacing.xs,
                bottom: AppSpacing.xs,
                right: AppSpacing.sm,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${index + 1}. ${lo.name ?? 'LO Title'}',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: isLoExpanded
                            ? FontManager.bold
                            : FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isLoExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isLoExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isLoExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  if (lo.entities != null && lo.entities!.isNotEmpty)
                    ...lo.entities!.asMap().entries.map((entityEntry) {
                      final entityIndex = entityEntry.key;
                      final entity = entityEntry.value;
                      return Column(
                        children: [
                          _buildEntityItem(entity, entityIndex, loId),
                          if (entityIndex != lo.entities!.length - 1)
                            Divider(
                                height: 1,
                                thickness: 1,
                                color: Colors.grey.shade300),
                        ],
                      );
                    }).toList()
                  else
                    Padding(
                      padding: EdgeInsets.all(AppSpacing.sm),
                      child: Text(
                        'No Entities available',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s11,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
