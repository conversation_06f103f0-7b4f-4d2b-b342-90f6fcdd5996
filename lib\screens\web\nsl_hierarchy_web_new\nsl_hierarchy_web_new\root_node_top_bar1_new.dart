import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_side_details_new.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class RootNodeTopBar1New extends StatelessWidget {
  final NSLNode rootNode;
  final NslTreeSidePanel? rootNodeDetails; // API node details
  final MetricsInfo? nodeTransactions;

  const RootNodeTopBar1New({
    super.key,
    required this.rootNode,
    this.rootNodeDetails, // Add API data
    this.nodeTransactions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF5F8FF),
        border: Border(
          bottom: BorderSide(color: Color(0xFF797676), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Left section - Organisation Departments (matching left panel width)
          Container(
            width: MediaQuery.of(context).size.width * 0.0695,
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.xs,
            ),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Color(0xffD0D0D0), width: 1),
              ),
            ),
            child: Text(
              'Organisation Departments',
              maxLines: 2,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Right section - Metrics
          Expanded(
            child: Row(
              children: [
                _buildMetricItem('Total Transactions', _getTotalTransactions()),
                // Divider(color: Color(0xffD0D0D0), ),
                _buildMetricItem('Total GOs', _getTotalGOs()),
                _buildMetricItem('Total LOs', _getTotalLOs()),
                _buildMetricItem('Revenue', _getRevenue()),
                _buildMetricItem('Cost', _getCost()),
                _buildMetricItem('Margin', _getMargin()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String label, String value) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            right: BorderSide(color: Color(0xffD0D0D0), width: 1),
          ),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.sm,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //  mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 2),
              Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTotalTransactions() {
// Tru to get Transactions data first
    if (nodeTransactions?.result?.totalTransactions != null) {
      return nodeTransactions!.result!.totalTransactions.toString();
    }
    return '0';
  }

  String _getTotalGOs() {
    // Priority 1: Use API data if available
    if (rootNodeDetails?.result?.goCount != null) {
      return rootNodeDetails!.result!.goCount.toString();
    }

    // Priority 2: Use static data from metrics
    if (rootNode.originalData.metrics?.totalGos != null) {
      return rootNode.originalData.metrics!.totalGos.toString();
    }

    // Fallback
    return '0';
  }

  String _getTotalLOs() {
    // Priority 1: Use API data if available
    if (rootNodeDetails?.result?.loCount != null) {
      return rootNodeDetails!.result!.loCount.toString();
    }

    // Priority 2: Use static data from metrics
    if (rootNode.originalData.metrics?.totalLos != null) {
      return rootNode.originalData.metrics!.totalLos.toString();
    }

    // Fallback
    return '0';
  }

  String _getRevenue() {
    if (nodeTransactions?.result?.revenue != null) {
      return nodeTransactions!.result!.revenue.toString();
    }
    return '0';
  }

  String _getCost() {
    if (nodeTransactions?.result?.cost != null) {
      return nodeTransactions!.result!.cost.toString();
    }
    return '0';
  }

  String _getMargin() {
    if (nodeTransactions?.result?.margin != null) {
      return nodeTransactions!.result!.margin.toString();
    }
    return '0';
  }
}
