#!/bin/bash

###############################################################################
# 🛠 FLUTTER WEB BUILD SCRIPT
#
# 📌 Description:
#    - Increments version in `pubspec.yaml`
#    - Builds Flutter web app
#    - Modifies `index.html` and JS files to bust cache using version query
#    - Sets custom base href
#
# ✅ Compatible With:
#    - macOS
#    - Linux / Ubuntu
#    - Windows (via Git Bash or WSL)
#
# 🚀 How to Run:
#
# ▸ macOS / Linux / WSL:
#   $ chmod +x build_flutter_web.sh
#   $ ./build_flutter_web.sh
#
# ▸ Windows (Git Bash):
#   - Right-click project folder → "Git Bash Here"
#   - Run:
#     $ ./build_flutter_web.sh
#
# ▸ Not supported in:
#   - Windows CMD or PowerShell (unless using WSL or Bash-compatible shell)
#
# 🧰 Prerequisites:
#   - Flutter SDK
#   - Bash
#   - sed, grep, cut, dos2unix (standard on Unix-like OS)
#
###############################################################################

set -e

# Normalize line endings (ignore errors on systems without dos2unix)
dos2unix pubspec.yaml 2>/dev/null || true

echo "🔄 Incrementing build version..."

# Extract current version
version_line=$(grep '^version:' pubspec.yaml)
version_base=$(echo "$version_line" | cut -d' ' -f2 | cut -d'+' -f1)
version_build=$(echo "$version_line" | cut -d'+' -f2)

version_major_minor=$(echo "$version_base" | cut -d'.' -f1,2)
version_patch=$(echo "$version_base" | cut -d'.' -f3)

# Increment patch and build
new_patch=$((version_patch + 1))
new_build=$((version_build + 1))

new_version="${version_major_minor}.${new_patch}+${new_build}"
version_no_plus=$(echo "$new_version" | tr -d '+')

echo "📦 Updating pubspec.yaml to version: $new_version"

# Cross-platform sed support
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/^version:.*/version: $new_version/" pubspec.yaml
else
    sed -i "s/^version:.*/version: $new_version/" pubspec.yaml
fi

echo "🧹 flutter clean & get"
flutter clean
flutter packages get

echo "🚀 flutter build web --release"
flutter build web --release

# Replace base href
echo "🔗 Updating <base href> in index.html"
baseHref="/nsl/nsl_new/"
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s|<base href=\"/\">|<base href=\"$baseHref\">|g" build/web/index.html
else
    sed -i "s|<base href=\"/\">|<base href=\"$baseHref\">|g" build/web/index.html
fi

# Update main.dart.js references with version
echo "🔧 Patching main.dart.js references"
for file in build/web/flutter.js build/web/flutter_bootstrap.js build/web/index.html; do
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "s/\"main.dart.js\"/\"main.dart.js?v=$version_no_plus\"/g" "$file"
    else
        sed -i "s/\"main.dart.js\"/\"main.dart.js?v=$version_no_plus\"/g" "$file"
    fi
done

# Patch main.dart.js for asset versioning
echo "🛠 Patching asset loader in main.dart.js"
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/self\.window\.fetch(a),/self.window.fetch(a + '?v=$version_no_plus'),/g" build/web/main.dart.js
else
    sed -i "s/self\.window\.fetch(a),/self.window.fetch(a + '?v=$version_no_plus'),/g" build/web/main.dart.js
fi

# Add version to manifest.json
echo "📦 Adding version to manifest.json URL"
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/\"manifest.json\"/\"manifest.json?v=$version_no_plus\"/" build/web/index.html
else
    sed -i "s/\"manifest.json\"/\"manifest.json?v=$version_no_plus\"/" build/web/index.html
fi

echo "✅ Flutter web build completed successfully!"
