import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/services/model_api_service.dart';
class ModelSelectionDropdown extends StatefulWidget {
  final List<ModelInfo> models;
  final ModelInfo selectedModel;
  final Function(ModelInfo) onModelSelected;
  final bool dropdownAbove;
  const ModelSelectionDropdown({
    super.key,
    required this.models,
    required this.selectedModel,
    required this.onModelSelected,
    this.dropdownAbove = false,
  });
  @override
  State<ModelSelectionDropdown> createState() => _ModelSelectionDropdownState();
}
class _ModelSelectionDropdownState extends State<ModelSelectionDropdown>
    with RouteAware, WidgetsBindingObserver {
  bool isDropdownOpen = false;
  bool isHovered = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  RouteObserver<PageRoute>? _routeObserver;
  bool isMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 600;
  }
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }
  @override
  void didChangeMetrics() {
    // Close dropdown when window is resized (minimize/maximize)
    if (isDropdownOpen) {
      _closeDropdown();
    }
  }
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      _routeObserver = RouteObserver<PageRoute>();
      _routeObserver?.subscribe(this, modalRoute);
    }
  }
  @override
  void dispose() {
    _removeOverlay();
    
    _routeObserver?.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  @override
  void deactivate() {
    _closeDropdown();
    super.deactivate();
  }
  @override
  void didPushNext() => _closeDropdown();
  @override
  void didPop() => _closeDropdown();
  @override
  void didPopNext() => _closeDropdown();
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _closeDropdown();
    }
  }
  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }
  void _closeDropdown() {
    if (isDropdownOpen) {
      _removeOverlay();
      if (mounted) {
        setState(() {
          isDropdownOpen = false;
        });
      }
    }
  }
  void _toggleDropdown() {
    isDropdownOpen ? _closeDropdown() : _showDropdown();
  }
  void _showDropdown() {
    if (!mounted) return;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isDropdownOpen = true;
    });
  }
  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);
    var size = renderBox.size;
    bool mobile = isMobile(context);
    // Calculate responsive heights
    final double screenHeight = MediaQuery.of(context).size.height;
    final double modelItemHeight =
        mobile ? 56.0 : 64.0; // Responsive item height
    final double moreModelsHeight =
        mobile ? 44.0 : 48.0; // Responsive more models height
    const int maxVisibleModels = 3; // Show only 3 models before scrolling
    final double padding = mobile ? 4.0 : 8.0;
    // Calculate total dropdown height
    final double maxDropdownHeight =
        (maxVisibleModels * modelItemHeight) + moreModelsHeight + padding;
    final double scrollableHeight = widget.models.length > maxVisibleModels
        ? maxVisibleModels * modelItemHeight
        : widget.models.length * modelItemHeight;
    // Ensure dropdown doesn't exceed screen bounds
    final double availableSpace = widget.dropdownAbove
        ? offset.dy - 20
        : screenHeight - offset.dy - size.height - 20;
    final double finalDropdownHeight =
        maxDropdownHeight > availableSpace ? availableSpace : maxDropdownHeight;
    return OverlayEntry(
        builder: (context) => GestureDetector(
              onTap: _closeDropdown,
              behavior: HitTestBehavior.translucent,
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.transparent,
                child: Stack(
                  children: [
                    Positioned(
                      left: mobile ? offset.dx - 190 : offset.dx - 150,
                      top: widget.dropdownAbove
                          ? offset.dy - finalDropdownHeight + 10
                          : offset.dy + size.height + 6,
                      width: mobile ? 280 : 320,
                      child: GestureDetector(
                        onTap: () {},
                        child: Material(
                          color: Colors.transparent,
                          child: Container(
                            height: finalDropdownHeight,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: Colors.grey.shade300, width: 1),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Material(
                                color: Colors.white,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Scrollable models section
                                    Expanded(
                                      child: widget.models.length >
                                              maxVisibleModels
                                          ? Scrollbar(
                                              thumbVisibility: true,
                                              child: ListView.builder(
                                                padding: EdgeInsets.zero,
                                                itemCount: widget.models.length,
                                                itemBuilder: (context, index) {
                                                  return _buildModelItem(
                                                      widget.models[index],
                                                      mobile,
                                                      modelItemHeight);
                                                },
                                              ),
                                            )
                                          : Column(
                                              children: widget.models
                                                  .map((model) =>
                                                      _buildModelItem(
                                                          model,
                                                          mobile,
                                                          modelItemHeight))
                                                  .toList(),
                                            ),
                                    ),
                                    // Divider
                                    Container(
                                      height: 1,
                                      color: Colors.grey.shade200,
                                    ),
                                    // More models section (fixed at bottom)
                                    InkWell(
                                      onTap: _closeDropdown,
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(12),
                                        bottomRight: Radius.circular(12),
                                      ),
                                      child: Container(
                                        width: double.infinity,
                                        height: moreModelsHeight,
                                        padding: EdgeInsets.symmetric(
                                            horizontal: mobile ? 12 : 16,
                                            vertical: mobile ? 8 : 12),
                                        child: Row(
                                          children: mobile
                                              ? [
                                                  SvgPicture.asset(
                                                    "assets/images/chat/arrow_left_mobile.svg",
                                                    height: 12,
                                                    width: 12,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            Colors
                                                                .grey.shade600,
                                                            BlendMode.srcIn),
                                                  ),
                                                  Spacer(),
                                                  Text(
                                                    'More models',
                                                    style: FontManager
                                                        .getCustomStyle(
                                                      fontSize:
                                                          mobile ? 12 : 14,
                                                      fontFamily: FontManager
                                                          .fontFamilyInter,
                                                      color:
                                                          Colors.grey.shade600,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ]
                                              : [
                                                  Text(
                                                    'More models',
                                                    style: FontManager
                                                        .getCustomStyle(
                                                      fontSize: 14,
                                                      fontFamily: FontManager
                                                          .fontFamilyInter,
                                                      color:
                                                          Colors.grey.shade600,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  Spacer(),
                                                  Icon(
                                                    Icons.arrow_forward_ios,
                                                    size: 14,
                                                    color: Colors.grey.shade600,
                                                  ),
                                                ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }
  Widget _buildModelItem(ModelInfo model, bool mobile, double itemHeight) {
    final bool isSelected = model.id == widget.selectedModel.id;
    return InkWell(
      onTap: () {
        widget.onModelSelected(model);
        _closeDropdown();
      },
      child: Container(
        width: double.infinity,
        height: itemHeight,
        padding: EdgeInsets.symmetric(
            horizontal: mobile ? AppSpacing.sm : 16,
            vertical: mobile ? AppSpacing.xs : 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade50 : Colors.transparent,
        ),
        child: Row(
          children: mobile
              ? [
                  if (isSelected)
                    Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(9),
                      ),
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  SizedBox(width: AppSpacing.xs),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            if (model.isPro)
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 1),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade100,
                                  borderRadius: BorderRadius.circular(3),
                                ),
                                child: Text(
                                  'PRO',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 8,
                                    fontFamily: FontManager.fontFamilyInter,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade800,
                                  ),
                                ),
                              ),
                            SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                model.name,
                                style: FontManager.getCustomStyle(
                                  fontSize: 12,
                                  fontFamily: FontManager.fontFamilyInter,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? Colors.blue.shade700
                                      : Colors.black,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 2),
                        // Text(
                        //   model.description,
                        //   textAlign: TextAlign.right,
                        //   style: FontManager.getCustomStyle(
                        //     fontSize: 10,
                        //     fontFamily: FontManager.fontFamilyInter,
                        //     color: Colors.grey.shade600,
                        //   ),
                        //   maxLines: 1,
                        //   overflow: TextOverflow.ellipsis,
                        // ),
                      ],
                    ),
                  ),
                ]
              : [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                model.name,
                                 style: FontManager.getCustomStyle(
                            fontSize: 12,
                            fontFamily: FontManager.fontFamilyInter,
                            color: isSelected
                                      ? Colors.blue.shade700: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                                // style: FontManager.getCustomStyle(
                                //   fontSize: 14,
                                //   fontFamily: FontManager.fontFamilyInter,
                                //   fontWeight: FontWeight.w600,
                                //   color: isSelected
                                //       ? Colors.blue.shade700
                                //       : Colors.black,
                                // ),
                                // overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (model.isPro) ...[
                              SizedBox(width: 8),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade100,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'PRO',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 10,
                                    fontFamily: FontManager.fontFamilyInter,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade800,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        // SizedBox(height: 2),
                        // Text(
                        //   model.description,
                        //   style: FontManager.getCustomStyle(
                        //     fontSize: 12,
                        //     fontFamily: FontManager.fontFamilyInter,
                        //     color: Colors.grey.shade600,
                        //   ),
                        //   maxLines: 1,
                        //   overflow: TextOverflow.ellipsis,
                        // ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                ],
        ),
      ),
    );
  }
  @override
  Widget build(BuildContext context) {
    final bool mobile = isMobile(context);
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xs),
              border: Border.all(
                color: mobile
                    ? Color(0xFFE4E7EA) // Mobile border color
                    : (isHovered || isDropdownOpen
                        ? Colors.grey.shade400
                        : Colors.transparent),
                width: 1,
              ),
            ),
            padding: mobile
                ? EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs, vertical: AppSpacing.xs)
                : EdgeInsets.symmetric(horizontal: 14, vertical: AppSpacing.xs),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.selectedModel.name,
                  style: FontManager.getCustomStyle(
                      fontSize: mobile ? FontManager.s12 : FontManager.s14,
                      color: mobile ? Colors.black : Colors.grey.shade700,
                      fontWeight:
                          mobile ? FontManager.medium : FontManager.regular,
                      fontFamily: FontManager.fontFamilyInter),
                ),
                SizedBox(width: AppSpacing.xxs),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: mobile ? Colors.black : Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
