import 'package:flutter/material.dart';
import 'package:nsl/services/new_file_upload_service.dart';
import 'package:nsl/utils/logger.dart';

/// A service that handles file upload and OCR processing using the new API (http://10.26.1.11:8002/api/v1/process)
/// without showing the side panel
class NewFileUploadOcrService {
  final NewFileUploadService _newFileUploadService = NewFileUploadService();

  /// Process a file by picking, uploading, and performing OCR using the new API
  /// Returns a Future that completes with a Map containing:
  /// - success: Whether the operation was successful
  /// - fileName: The name of the uploaded file
  /// - extractedText: The text extracted from the image (if OCR was successful)
  /// - jobId: The job ID from the new API
  /// - status: Processing status
  /// - processingTime: Time taken to process
  /// - confidenceScores: OCR confidence metrics
  /// - toolsUsed: OCR tools utilized
  /// - metadata: Additional processing information
  /// - errorMessage: An error message (if an error occurred)
  Future<Map<String, dynamic>> processFileForChat({
    String strategy = 'auto',
    bool asyncProcessing = false,
  }) async {
    try {
      // Pick a file
      final file = await _newFileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        return {
          'success': false,
          'errorMessage': 'File selection canceled',
          'isCanceled': true,
        };
      }

      // Upload the file using the new API
      final uploadResult = await _newFileUploadService.uploadFileNewApi(
        file,
        strategy: strategy,
        asyncProcessing: asyncProcessing,
      );

      if (!uploadResult['success']) {
        // Handle upload error
        return {
          'success': false,
          'errorMessage': 'Failed to upload file using new API: ${uploadResult['message']}',
        };
      }

      // Get all the information from the new API response
      final fileName = file.name;
      final extractedText = uploadResult['extracted_text'];
      final jobId = uploadResult['job_id'];
      final status = uploadResult['status'];
      final processingTime = uploadResult['processing_time'];
      final confidenceScores = uploadResult['confidence_scores'];
      final toolsUsed = uploadResult['tools_used'];
      final metadata = uploadResult['metadata'];

      Logger.info('File uploaded successfully using new API. Job ID: $jobId');
      Logger.info('Extracted Text: $extractedText');
      Logger.info('Processing Time: $processingTime');
      Logger.info('Tools Used: $toolsUsed');
      Logger.info('Confidence Scores: $confidenceScores');

      // Return success with all the information
      return {
        'success': true,
        'fileName': fileName,
        'extractedText': extractedText,
        'jobId': jobId,
        'status': status,
        'processingTime': processingTime,
        'confidenceScores': confidenceScores,
        'toolsUsed': toolsUsed,
        'metadata': metadata,
      };
    } catch (e) {
      // Handle general error
      Logger.error('Error in new file processing service: $e');
      return {
        'success': false,
        'errorMessage': 'Error processing file using new API: $e',
      };
    }
  }

  /// Check the status of an async job
  Future<Map<String, dynamic>> checkJobStatus(String jobId) async {
    try {
      final result = await _newFileUploadService.checkJobStatus(jobId);
      return result;
    } catch (e) {
      Logger.error('Error checking job status: $e');
      return {
        'success': false,
        'errorMessage': 'Error checking job status: $e',
      };
    }
  }

  /// Show a temporary overlay with a message (same as the old service for consistency)
  void showOverlay(BuildContext context, String message,
      {bool isError = false}) {
    // Create an overlay entry
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 50,
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isError ? Colors.red.shade700 : Colors.green.shade700,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 3 seconds (slightly longer for new API messages)
    Future.delayed(Duration(seconds: 3), () {
      overlayEntry.remove();
    });
  }

  /// Show a detailed overlay with job information
  void showDetailedOverlay(BuildContext context, Map<String, dynamic> result) {
    if (result['success']) {
      final message = '''File: ${result['fileName']}
Job ID: ${result['jobId']}
Status: ${result['status']}
Processing Time: ${result['processingTime']}s
Tools Used: ${result['toolsUsed']?.join(', ') ?? 'N/A'}''';
      
      showOverlay(context, message);
    } else {
      showOverlay(context, result['errorMessage'] ?? 'Unknown error', isError: true);
    }
  }
}
