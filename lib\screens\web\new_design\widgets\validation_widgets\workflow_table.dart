import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/process_flow_tree_widget.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

/// A reusable workflow table widget that displays workflow data in a process flow format.
///
/// This widget shows workflow information extracted from validation results
/// and provides interaction capabilities for workflow visualization.
class WorkflowTable extends StatelessWidget {
  /// The manual creation provider containing workflow data
  final ManualCreationProvider provider;

  /// Callback when a workflow node is selected
  final VoidCallback? onNodeSelected;

  const WorkflowTable({
    super.key,
    required this.provider,
    this.onNodeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final workflowData = provider.getLinearWorkflowData();

    if (workflowData == null) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No workflow data available',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    print('🌳 Rendering linear workflow tree');

    return _buildProcessFlowDiagram(context, workflowData);
  }

  Widget _buildProcessFlowDiagram(
      BuildContext context, Map<String, dynamic> workflowData) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workflow content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: ConstrainedBox(
                constraints: const BoxConstraints(minWidth: double.infinity),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Main workflow title
                    _buildWorkflowTitle(context, workflowData),
                    SizedBox(height: 20),

                    // Process flow diagram
                    _buildProcessFlowTree(context, workflowData),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTitle(
      BuildContext context, Map<String, dynamic> workflowData) {
    final title = workflowData['title'] ?? '';

    // Only show title if it's not empty
    if (title.isEmpty) {
      return Text(
        'No workflow title available from API response',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodyMedium(context),
          fontWeight: FontManager.medium,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      );
    }

    return Text(
      title,
      style: FontManager.getCustomStyle(
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontWeight: FontManager.bold,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
      ),
    );
  }

  // Build process flow tree based on the provided process_flow data
  Widget _buildProcessFlowTree(
      BuildContext context, Map<String, dynamic> workflowData) {
    // Get the extracted workflow data from the provider
    final extractedWorkflowData = provider.extractedWorkflowData;

    if (extractedWorkflowData == null || extractedWorkflowData.isEmpty) {
      return Text(
        'No process flow data available',
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Get the process flow from the first workflow
    final workFlowDetails = extractedWorkflowData.first['workFlowDetails'];
    final processFlow = workFlowDetails?.processFlow;

    if (processFlow == null || processFlow.isEmpty) {
      return Text(
        'No process flow nodes found',
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // return _buildProcessFlowTreeStructure(context, processFlow);
    return ProcessFlowWidget(
      processList: processFlow,
    );
  }

  // Build the process flow tree structure based on the provided process_flow data
  Widget _buildProcessFlowTreeStructure(
      BuildContext context, List<dynamic> processFlow) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Container(
          padding: EdgeInsets.all(20),
          child: _buildProcessColumnsWidget(context, processFlow),
        ),
      ),
    );
  }

  // Build widget from process columns
  Widget _buildProcessColumnsWidget(
      BuildContext context, List<dynamic> processFlow) {
    final columns = provider.buildProcessColumns(processFlow);
    List<Color> colors = ManualCreationProvider.pastelColors;

    // Create a mapping to track node connections and styling
    Map<String, Map<String, dynamic>> nodeConnections = {};

    // Analyze the process flow to determine connections
    provider.analyzeNodeConnections(processFlow, nodeConnections, colors);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columns.asMap().entries.map((entry) {
        int columnIndex = entry.key;
        List<dynamic> column = entry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: column.asMap().entries.expand((nodeEntry) {
            int nodeIndex = nodeEntry.key;
            dynamic node = nodeEntry.value;
            List<Widget> widgets = [];

            // Handle special nodes
            if (provider.isEmptyNode(node)) {
              widgets.add(SizedBox(height: node.height?.toDouble() ?? 30.0));
            } else if (provider.isTerminalNode(node)) {
              widgets.add(_buildTerminalIcon());
            } else if (provider.isPausedNode(node)) {
              // Handle paused nodes - show them with "Paused" route type
              String nodeName = node.loName ?? 'Unknown';
              String routeType = 'Paused';

              // Determine styling based on node connections analysis
              bool isLinkStart = false;
              bool isLinkEnd = false;
              Color? boxColor;

              // Check if this node has connection styling using column-specific key
              String nodeKey = '${nodeName}_$columnIndex';
              String? alternateTag;
              String? parallelTag;
              if (nodeConnections.containsKey(nodeKey)) {
                final connectionInfo = nodeConnections[nodeKey]!;
                isLinkStart = connectionInfo['isLinkStart'] ?? false;
                isLinkEnd = connectionInfo['isLinkEnd'] ?? false;
                boxColor = connectionInfo['boxColor'];
                alternateTag = connectionInfo['alternateTag'];
                parallelTag = connectionInfo['parallelTag'];
              }

              Widget nodeWidget = _buildNodeWidget(
                context,
                nodeName,
                routeType,
                isLinkStart: isLinkStart,
                isLinkEnd: isLinkEnd,
                boxColor: boxColor,
                alternateTag: alternateTag,
                parallelTag: parallelTag,
              );

              widgets.add(nodeWidget);
            } else {
              // Handle ProcessFlow objects only
              String nodeName = node.loName ?? 'Unknown';
              String routeType = node.routeType ?? '';

              // Determine styling based on node connections analysis
              bool isLinkStart = false;
              bool isLinkEnd = false;
              Color? boxColor;

              // Check if this node has connection styling using column-specific key
              String nodeKey = '${nodeName}_$columnIndex';
              String? alternateTag;
              String? parallelTag;
              if (nodeConnections.containsKey(nodeKey)) {
                final connectionInfo = nodeConnections[nodeKey]!;
                isLinkStart = connectionInfo['isLinkStart'] ?? false;
                isLinkEnd = connectionInfo['isLinkEnd'] ?? false;
                boxColor = connectionInfo['boxColor'];
                alternateTag = connectionInfo['alternateTag'];
                parallelTag = connectionInfo['parallelTag'];
              }

              Widget nodeWidget = _buildNodeWidget(
                context,
                nodeName,
                routeType,
                isLinkStart: isLinkStart,
                isLinkEnd: isLinkEnd,
                boxColor: boxColor,
                alternateTag: alternateTag,
                parallelTag: parallelTag,
              );

              // Add connections for alternate routing
              if (routeType == 'Alternate') {
                widgets.add(Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    nodeWidget,
                    _buildAlternateConnection(),
                  ],
                ));
              } else {
                widgets.add(nodeWidget);
              }
            }

            // Add spacing between widgets (except for the last one)
            if (nodeIndex < column.length - 1) {
              widgets.add(SizedBox(height: 50));
            }

            return widgets;
          }).toList(),
        );
      }).toList(),
    );
  }

  // Build a simple node widget
  Widget _buildNodeWidget(
      BuildContext context, String nodeName, String routeType,
      {bool isLinkStart = false,
      bool isLinkEnd = false,
      Color? boxColor,
      String? alternateTag,
      String? parallelTag}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          if (provider.workflowLoValidationResult != null) {
            provider.showWorkflowLoDetailsPanel(
                provider.workflowLoValidationResult!);
          }
          onNodeSelected?.call();
        },
        child: Container(
          child: Row(
            children: [
              CustomPaint(
                size: Size(40, 1),
                painter: HorizontalDottedLinePainter(
                  color: Color(0xff797676),
                  dashLength: 3,
                  dashGap: 3,
                ),
              ),
              boxColor != null
                  ? Row(
                      children: [
                        // Add alternate tag if present
                        if (alternateTag != null)
                          Text(
                            alternateTag,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Color(0xff5D5D5D),
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        // Add parallel tag if present
                        if (parallelTag != null)
                          Text(
                            parallelTag,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Color(0xff5D5D5D),
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        Container(
                          margin: EdgeInsets.only(
                              left: AppSpacing.xxs, right: AppSpacing.xs),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                                color: Color(0xff797676), width: 0.5),
                            color: Colors.white,
                          ),
                        ),

                        isLinkStart
                            ? Padding(
                                padding: const EdgeInsets.only(
                                    right: AppSpacing.xxs),
                                child: _buildLinkIcon(boxColor: boxColor),
                              )
                            : SizedBox(),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(AppSpacing.xxs),
                              color: boxColor),
                          padding: EdgeInsets.all(AppSpacing.xxs),
                          child: Text(
                            nodeName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black87,
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        ),
                        isLinkEnd
                            ? Padding(
                                padding:
                                    const EdgeInsets.only(left: AppSpacing.xxs),
                                child: _buildLinkIcon(boxColor: boxColor),
                              )
                            : SizedBox(),
                        SizedBox(
                          width: AppSpacing.xs,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        // Add alternate tag if present
                        if (alternateTag != null)
                          Text(
                            alternateTag,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Color(0xff5D5D5D),
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        // Add parallel tag if present
                        if (parallelTag != null)
                          Text(
                            parallelTag,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Color(0xff5D5D5D),
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        Container(
                          margin: EdgeInsets.only(
                              left: AppSpacing.xxs, right: AppSpacing.xs),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                                color: Color(0xff797676), width: 0.5),
                            color: Colors.white,
                          ),
                        ),

                        Container(
                          padding: EdgeInsets.all(AppSpacing.xxs),
                          child: Text(
                            nodeName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black87,
                              fontWeight: FontManager.medium,
                            ),
                          ),
                        ),

                        SizedBox(
                          width: AppSpacing.xs,
                        ),
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }

  // Build terminal icon for workflow end
  Widget _buildTerminalIcon() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.xxs),
      child: Row(
        children: [
          CustomPaint(
            size: Size(30, 1),
            painter: HorizontalDottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
          SvgPicture.asset(
            'assets/images/workflow/terminal_icon.svg',
            width: 20,
            height: 20,
          ),
        ],
      ),
    );
  }

  // Build link icon for connections
  Widget _buildLinkIcon({Color? boxColor}) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSpacing.xxs), color: boxColor),
      padding: EdgeInsets.all(AppSpacing.xxs),
      child: SvgPicture.asset(
        'assets/images/workflow/link_icon.svg',
        width: 20,
        height: 20,
        colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
      ),
    );
  }

  Widget _buildAlternateConnection() {
    return Padding(
      padding: const EdgeInsets.only(top: AppSpacing.sm, right: AppSpacing.xxs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomPaint(
            size: Size(20, 1),
            painter: HorizontalDottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
          const SizedBox(
            width: AppSpacing.xxs,
          ),
          CustomPaint(
            size: Size(1, 80),
            painter: DottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for dotted vertical lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    this.dashLength = 5.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for dotted horizontal lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    this.dashLength = 3.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
