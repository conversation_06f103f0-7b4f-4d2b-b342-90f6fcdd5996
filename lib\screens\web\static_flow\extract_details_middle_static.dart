import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_table.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

class ExtractDetailsMiddleStatic extends StatefulWidget {
  const ExtractDetailsMiddleStatic({super.key});

  @override
  State<ExtractDetailsMiddleStatic> createState() =>
      _ExtractDetailsMiddleStaticState();
}

class _ExtractDetailsMiddleStaticState
    extends State<ExtractDetailsMiddleStatic> {
  OverlayEntry? _overlayEntry;
  final Map<String, LayerLink> _layerLinks = {};
  String? _currentHoveredObject;
  late AccordionController _accordionController;

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    _removeOverlay();
    super.dispose();
  }

  LayerLink _getLayerLink(String objectTitle) {
    if (!_layerLinks.containsKey(objectTitle)) {
      _layerLinks[objectTitle] = LayerLink();
    }
    return _layerLinks[objectTitle]!;
  }

  void _showOverlay(String objectTitle) {
    _removeOverlay();
    _currentHoveredObject = objectTitle;
    _overlayEntry = _createOverlayEntry(objectTitle);
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _currentHoveredObject = null;
  }

  OverlayEntry _createOverlayEntry(String objectTitle) {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: 280,
        child: CompositedTransformFollower(
          link: _getLayerLink(objectTitle),
          showWhenUnlinked: false,
          offset: const Offset(-250, 25),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    'This Object is already exists in your library. You need to rename the objects to proceed.',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.grey[700]!,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () => _removeOverlay(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                            side: BorderSide(color: Colors.grey[500]!),
                          ),
                        ),
                        child: Text(
                          'Continue',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.grey[600]!,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => _removeOverlay(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Resolve',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontFamily: FontManager.fontFamilyInter,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            provider.isAIMode ? 'Extracted Details' : 'Objects',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.w400,
              height: 1,
            ),
          ),

          // AI/Manual Toggle - positioned to the right
          _buildAIManualToggle(context, provider),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              provider.toggleAIMode();
              Provider.of<ManualCreationProvider>(context, listen: false)
                  .handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          provider.isAIMode ? 'Form' : 'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildExtractedDetailsTab(context);
    } else {
      return _buildObjectsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(6.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Expansion panels for objects in extracted details
          _buildObjectExpansionPanel(context, 'Object: Customer'),

          _buildObjectExpansionPanel(context, 'Object: Product'),

          _buildObjectExpansionPanel(context, 'Object: Order'),
        ],
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EntityTable(
            provider:
                Provider.of<ManualCreationProvider>(context, listen: false),
            onEntitySelected: (entity) {
              Provider.of<ManualCreationProvider>(context, listen: false);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(
      BuildContext context, String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Color(0xFF0058FF),
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                if (subtitle != title) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(objectTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            // borderRadius: BorderRadius.circular(4),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ExpansionTile(
              tilePadding:
                  const EdgeInsets.symmetric(horizontal: 4, vertical: 0),

              // childrenPadding: EdgeInsets.zero,
              onExpansionChanged: (expanded) {
                provider.setObjectExpansion(objectTitle, expanded);
              },
              trailing: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color:
                      isExpanded ? const Color(0xFF0058FF) : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: isExpanded ? Colors.white : Colors.grey[600],
                  size: 16,
                ),
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Text(
                      objectTitle,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        height: 1.2,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Alert icon with hover popup
                  CompositedTransformTarget(
                    link: _getLayerLink(objectTitle),
                    child: MouseRegion(
                      onEnter: (_) => _showOverlay(objectTitle),
                      onExit: (_) => _removeOverlay(),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        // decoration: BoxDecoration(
                        //   color: Colors.red.withOpacity(0.1),
                        //   borderRadius: BorderRadius.circular(4),
                        // ),
                        child: Icon(
                          Icons.notifications,
                          color: Colors.red,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              children: [
                // Accordion content - replace the placeholder with accordion items
                Column(
                  children: [
                    _buildBooksAccordionItem(AccordionItem(
                      id: '${objectTitle}_details',
                      title: 'Object Details',
                      subtitle:
                          'Detailed information about the ${objectTitle.replaceAll('Object: ', '')} object including attributes, relationships, and business rules.',
                      children: [
                        'Attributes',
                        'Relationships',
                        'Business Rules'
                      ],
                    )),
                    _buildBooksAccordionItem(AccordionItem(
                      id: '${objectTitle}_properties',
                      title: 'Properties & Methods',
                      subtitle: null,
                      children: [
                        'Primary Key',
                        'Foreign Keys',
                        'Validation Rules',
                        'Business Methods'
                      ],
                    )),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Books accordion style - recreated from global_library_accordion_flow
  Widget _buildBooksAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);

    // Get status and count based on item title
    final statusInfo = _getStatusInfo(item.title);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  // Title
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.title,
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status badge positioned on the right
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusInfo['backgroundColor'],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            statusInfo['status'],
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyInter,
                              color: statusInfo['textColor'],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Count
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      statusInfo['count'],
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyInter,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Color(0xFFF9FAFB),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children
                        .map(
                          (child) => Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Row(
                              children: [
                                Container(
                                  width: 4,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[400],
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text(
                                  child,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily: FontManager.fontFamilyInter,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper method to get status information based on item title
  Map<String, dynamic> _getStatusInfo(String title) {
    switch (title) {
      case 'Object Details':
        return {
          'status': 'Partial Completion',
          'count': '1 Entity Detected',
          'backgroundColor': Color(0xFFFEF3C7),
          'textColor': Color(0xFF92400E),
        };
      case 'Properties & Methods':
        return {
          'status': 'Completed',
          'count': '25 Attributes',
          'backgroundColor': Color(0xFFD1FAE5),
          'textColor': Color(0xFF065F46),
        };
      default:
        return {
          'status': 'Missing',
          'count': '0 Configure',
          'backgroundColor': Color(0xFFFEE2E2),
          'textColor': Color(0xFF991B1B),
        };
    }
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}
