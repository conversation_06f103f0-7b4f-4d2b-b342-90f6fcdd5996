import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'package:url_launcher/url_launcher.dart';

/// A configurable hyperlink widget that renders a clickable link with various styling options.
class HyperlinkWidget extends StatefulWidget {
  // Content properties
  final String text;
  final String url;
  final IconData? icon;
  final bool showIcon;
  final bool iconLeading;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color? hoverColor;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isItalic;
  final bool isUnderlined;
  final String? fontFamily;
  final bool hasShadow;
  final double elevation;

  // Layout properties
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;

  // Behavior properties
  final bool openInNewTab;
  final bool useExternalBrowser;
  final String? tooltip;
  final bool isDisabled;

  // Callbacks
  final Function(String)? onTap;
  final Function(String)? onLongPress;

  const HyperlinkWidget({
    super.key,
    required this.text,
    required this.url,
    this.icon,
    this.showIcon = false,
    this.iconLeading = true,
    this.textColor = const Color(0xFF0058FF),
    this.backgroundColor = Colors.transparent,
    this.hoverColor,
    this.fontSize = 14.0,
    this.fontWeight = FontManager.medium,
    this.isItalic = false,
    this.isUnderlined = true,
    this.fontFamily = FontManager.fontFamilyInter,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.all(0.0),
    this.margin = const EdgeInsets.all(0),
    this.borderRadius = 4.0,
    this.hasBorder = false,
    this.borderColor = const Color(0xFF0058FF),
    this.borderWidth = 1.0,
    this.openInNewTab = false,
    this.useExternalBrowser = false,
    this.tooltip,
    this.isDisabled = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  State<HyperlinkWidget> createState() => _HyperlinkWidgetState();
}

class _HyperlinkWidgetState extends State<HyperlinkWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Create the text style
    final textStyle = TextStyle(
      color:
          _isHovered && widget.hoverColor != null
              ? widget.hoverColor
              : const Color(0xFF0058FF),
      //fontSize: widget.fontSize,
      fontSize: _getResponsiveFontSize(context),
      fontWeight: FontManager.medium,
      fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
      decoration:
          widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
      fontFamily: widget.fontFamily,
      decorationColor: const Color(0xFF0058FF),
    );

    // Create the icon if needed
    Widget? iconWidget;
    if (widget.showIcon && widget.icon != null) {
      iconWidget = Icon(
        widget.icon,
        size: widget.fontSize * 1.2,
        color:
            _isHovered && widget.hoverColor != null
                ? widget.hoverColor
                : widget.textColor,
      );
    }

    // Create the text widget
    final textWidget = Text(
      widget.text,
      style: textStyle,
      overflow: TextOverflow.visible,
      softWrap: true,
    );

    // Combine icon and text if needed
    Widget contentWidget;
    if (widget.showIcon && iconWidget != null) {
      contentWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children:
            widget.iconLeading
                ? [
                  iconWidget,
                  const SizedBox(width: 4),
                  Flexible(child: textWidget),
                ]
                : [
                  Flexible(child: textWidget),
                  const SizedBox(width: 4),
                  iconWidget,
                ],
      );
    } else {
      contentWidget = textWidget;
    }

    // Create the container with styling
    final styledWidget = Container(
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.hasShadow
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 opacity
                    blurRadius: widget.elevation,
                    offset: Offset(0, widget.elevation / 2),
                  ),
                ]
                : null,
      ),
      child: contentWidget,
    );

    // Create the interactive widget
    Widget interactiveWidget = MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap:
            widget.isDisabled
                ? null
                : () async {
                  if (widget.onTap != null) {
                    widget.onTap!(widget.url);
                  } else {
                    await _launchUrl();
                  }
                },
        onLongPress:
            widget.isDisabled || widget.onLongPress == null
                ? null
                : () => widget.onLongPress!(widget.url),
        child: styledWidget,
      ),
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      interactiveWidget = Tooltip(
        message: widget.tooltip!,
        child: interactiveWidget,
      );
    }

    return interactiveWidget;
  }

  Future<void> _launchUrl() async {
    final Uri uri = Uri.parse(widget.url);

    // Handle different URL schemes
    if (widget.url.startsWith('mailto:')) {
      // Email link
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } else if (widget.url.startsWith('tel:')) {
      // Phone link
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } else if (widget.url.startsWith('file:')) {
      // Local file link
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } else {
      // Web link
      try {
        await launchUrl(
          uri,
          mode:
              widget.useExternalBrowser
                  ? LaunchMode.externalApplication
                  : LaunchMode.platformDefault,
          webViewConfiguration: WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
            headers: <String, String>{'Referer': 'app'},
          ),
        );
      } catch (e) {
        debugPrint('Could not launch $uri: $e');
        // Show error message
        if (mounted) {
          // Use a post-frame callback to ensure we're not in the middle of a build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Could not open link: ${widget.url}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          });
        }
      }
    }
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 16.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 12.0; // Small (768-1024px)
  } else {
    return 12.0; // Default for very small screens
  }
}
