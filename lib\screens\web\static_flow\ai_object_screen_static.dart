import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/ai_object_right_panel.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/screens/web/static_flow/ai_validation_popup.dart';
import 'package:nsl/screens/web/static_flow/create_object_screen_static.dart';
import 'package:nsl/screens/web/static_flow/ai_object_right_panel.dart';

void main() {
  runApp(const MaterialApp(home: AiObjectScreenStatic()));
}

class AiObjectScreenStatic extends StatelessWidget {
  const AiObjectScreenStatic({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Centered HoverNavItems at top
          Center(
            child: HoverNavItems(),
          ),
          // 3 Column Layout
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // Column 1
                Expanded(
                  flex: 1,
                  child: Stack(
                    children: const [
                      CreateObjectScreenStatic(),
                      // NotificationHoverPanel(),
                    ],
                  ),
                ),

                // Column 2
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xffF7F9FB),
                      border: Border(
                        left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                        right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      ),
                    ),
                    child: const Center(
                      child: ExtractDetailsMiddleStatic(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Column 3
                Expanded(
                  flex: 1,
                  child: const AiObjectRightPanel(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
