import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/option_with_value.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/selectable_type_writer_text_widget.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/typed_checkbox_widget.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/response_text_parser.dart';
// import 'package:nsl/utils/response_text_parser.dart';

class ParsedResponseRenderer extends StatefulWidget {
  final String responseText;
  final Duration typingSpeed;
  final void Function(String option, bool isChecked, bool isMultiSelect)?
      onOptionChanged;
  final bool isTypingRequired;
  final VoidCallback? onNewLine;
  final List<OptionWithValue> options;
  final String displayText;
  final String trailingQuestion;
  final String leadingQuestion;
  final VoidCallback? onTypingComplete;
  final bool isLast;

  const ParsedResponseRenderer({
    super.key,
    required this.responseText,
    this.typingSpeed = const Duration(milliseconds: 40),
    this.onOptionChanged,
    this.isTypingRequired = true,
    this.onNewLine,
    required this.options,
    required this.displayText,
    required this.trailingQuestion,
    required this.leadingQuestion,
    this.onTypingComplete,
    this.isLast = false,
  });

  @override
  State<ParsedResponseRenderer> createState() => _ParsedResponseRendererState();
}

// class _ParsedResponseRendererState extends State<ParsedResponseRenderer> {
//   late final Map<String, dynamic> parsedData;

//   bool showCheckboxes = false;
//   bool showInitialQuestion = false;
//   bool showFinalQuestion = false;

//   @override
//   void initState() {
//     super.initState();
//     // parsedData =
//     //     ResponseTextParser.parseNewResponseStructure(widget.responseText);
//     // options = ResponseTextParser.getSelectionOptions(parsedData);
//     // displayText = ResponseTextParser.getUserDisplayText(parsedData);
//     // // trailingQuestion =
//     // //     ResponseTextParser.extractLastQuestion(widget.responseText);
//     // leadingQuestion = ResponseTextParser.extractQuestionInsideSelectionOptions(
//     //     widget.responseText);
//     // trailingQuestion = ResponseTextParser.extractQuestionAfterSelectionOptions(
//     //     widget.responseText);
//   }

//   // String _extractFinalQuestion(String fullText) {
//   //   final lines = fullText.split('\n');
//   //   for (int i = lines.length - 1; i >= 0; i--) {
//   //     final line = lines[i].trim();
//   //     if (line.endsWith('?')) {
//   //       return line;
//   //     }
//   //   }
//   //   return '';
//   // }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         /// Main typewritten content
//         widget.isTypingRequired
//             ? SelectableTypewriterText(
//                 textSpans: _buildTypewriterSpans(
//                   widget.displayText,
//                 ),
//                 speed: widget.typingSpeed,
//                 onComplete: () {
//                   setState(() {
//                     if (widget.leadingQuestion.isEmpty) {
//                       showCheckboxes = true;
//                     } else {
//                       showInitialQuestion = true;
//                     }
//                   });
//                 },
//                 onNewline: () => widget.onNewLine?.call(),
//               )
//             : SelectableText.rich(TextSpan(
//                 children: _buildTypewriterSpans(
//                   widget.displayText,
//                 ),
//                 style: FontManager.getCustomStyle(
//                     fontFamily: FontManager.fontFamilyTiemposText,
//                     fontSize: FontManager.s16,
//                     color: Colors.black),
//               )),
//         widget.isTypingRequired
//             ?

//             /// Leading question (if any)
//             showInitialQuestion && widget.leadingQuestion.isNotEmpty
//                 ? SelectableTypewriterText(
//                     textSpans: _buildTypewriterSpans(widget.leadingQuestion),
//                     speed: widget.typingSpeed,
//                     onNewline: () => widget.onNewLine?.call(),
//                     onComplete: () {
//                       setState(() {
//                         showCheckboxes = true;
//                       });
//                     },
//                   )
//                 : Container()
//             : widget.leadingQuestion.isNotEmpty
//                 ? SelectableText.rich(TextSpan(
//                     children: _buildTypewriterSpans(
//                       widget.leadingQuestion,
//                     ),
//                     style: FontManager.getCustomStyle(
//                         fontFamily: FontManager.fontFamilyTiemposText,
//                         fontSize: FontManager.s16,
//                         color: Colors.black),
//                   ))
//                 : Container(),
//         widget.isTypingRequired
//             ? showCheckboxes && widget.options.isNotEmpty
//                 ? TypingCheckboxList(
//                     isEnabled: !widget.isTypingRequired,
//                     options: widget.options,
//                     typingSpeed: widget.typingSpeed,
//                     onChanged: widget.onOptionChanged,
//                     isTypingRequired: widget.isTypingRequired,
//                     onTypingComplete: () {
//                       setState(() {
//                         if (widget.trailingQuestion.isNotEmpty) {
//                           showFinalQuestion = true;
//                         } else {
//                           widget.onTypingComplete?.call();
//                         }
//                       });
//                     },
//                   )
//                 : Container()
//             : TypingCheckboxList(
//                 isEnabled: !widget.isTypingRequired,
//                 options: widget.options,
//                 typingSpeed: widget.typingSpeed,
//                 onChanged: widget.onOptionChanged,
//                 isTypingRequired: widget.isTypingRequired,
//                 onTypingComplete: () {
//                   setState(() {
//                     // Now you can show trailing question here if needed
//                     showFinalQuestion = true;
//                   });
//                 },
//               ),

//         widget.isTypingRequired
//             ?

//             /// Trailing question (if any)
//             showFinalQuestion && widget.trailingQuestion.isNotEmpty
//                 ?
//                 // const SizedBox(height: 16),
//                 // Text("\n"),
//                 SelectableTypewriterText(
//                     textSpans: _buildTypewriterSpans(widget.trailingQuestion),
//                     speed: widget.typingSpeed,
//                     onNewline: () => widget.onNewLine?.call(),
//                     onComplete: () {
//                       widget.onTypingComplete?.call();
//                     },
//                   )
//                 : Container()
//             : widget.trailingQuestion.isNotEmpty
//                 ? SelectableText.rich(TextSpan(
//                     children: _buildTypewriterSpans(
//                       widget.trailingQuestion,
//                     ),
//                     style: FontManager.getCustomStyle(
//                         fontFamily: FontManager.fontFamilyTiemposText,
//                         fontSize: FontManager.s16,
//                         color: Colors.black),
//                   ))
//                 : Container()
//       ],
//     );
//   }

//   /// Helper to convert plain text into styled TextSpan list
//   List<InlineSpan> _buildTypewriterSpans(String text) {
//     // Remove lines starting with **HEADING**-like titles
//     final cleanedLines = text
//         .split('\n')
//         .map((line) => line.trim())
//         .where((line) => !RegExp(r'^\*\*.*\*\*$').hasMatch(line))
//         .toList();

//     final List<InlineSpan> spans = [];
//     if (text.isNotEmpty) {
//       for (var line in cleanedLines) {
//         if (!line.startsWith("\n") && !line.endsWith("\n")) {
//           line = "\n$line\n";
//         } else if (!line.startsWith("\n")) {
//           // line = "\n$line";
//         } else if (!line.endsWith("\n")) {
//           line = "$line\n";
//         }
//         spans.add(TextSpan(
//           text: line,
//           style: FontManager.getCustomStyle(
//               fontFamily: FontManager.fontFamilyTiemposText,
//               fontSize: FontManager.s16,
//               color: Colors.black),
//         ));
//       }
//     }

//     return spans;
//   }
// }

class _ParsedResponseRendererState extends State<ParsedResponseRenderer> {
  late final List<ParsedSection> _renderableSections;
  int _currentSectionIndex = 0;

  // 👇 single variable to control spacing everywhere
  final EdgeInsets sectionSpacing = const EdgeInsets.only(bottom: 0);

  @override
  void initState() {
    super.initState();
    final parsed = AiResponseParser.parseSections(widget.responseText);
    _renderableSections = parsed.entries
        .map((entry) => ParsedSection(name: entry.key, content: entry.value))
        .where((section) =>
            section.isRenderable &&
            (!section.name.toLowerCase().contains('global_summary_update') ||
                !section.name.toLowerCase().contains('global summary update')))
        .toList();
  }

  void _onCurrentSectionComplete() {
    if (_currentSectionIndex + 1 < _renderableSections.length) {
      setState(() {
        _currentSectionIndex++;
      });
    } else {
      widget.onTypingComplete?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_renderableSections.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(_renderableSections.length, (index) {
        final section = _renderableSections[index];
        final contentText = section.content is String
            ? section.content as String
            : const JsonEncoder.withIndent('  ').convert(section.content);
        final sectionName = index != 0 ? section.name : null;
        final isOptionsSection = section.name.contains("selection") ||
            section.name.contains("options");

        if (index < _currentSectionIndex || !widget.isTypingRequired) {
          return _buildCompletedSection(
              isOptionsSection, sectionName, contentText, section);
        }

        if (index == _currentSectionIndex) {
          return _buildCurrentTypingSection(
              isOptionsSection, sectionName, contentText, section);
        }

        return const SizedBox.shrink();
      }),
    );
  }

  Widget _buildCompletedSection(
    bool isOptionsSection,
    String? sectionName,
    String contentText,
    ParsedSection section,
  ) {
    if (isOptionsSection) {
      final lines = _parseOptionsWithDecorators(section);
      final spans = _buildSectionTitleSpans(sectionName);

      return Padding(
        padding: sectionSpacing,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (spans.isNotEmpty)
              SelectableText.rich(
                TextSpan(children: spans),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: FontManager.s16,
                  color: Colors.black,
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(
                  top: AppSpacing.xxs, bottom: AppSpacing.sm),
              child: TypingCheckboxList(
                lines: lines,
                typingSpeed: widget.typingSpeed,
                onChanged: widget.onOptionChanged,
                isEnabled: widget.isLast,
                isTypingRequired: widget.isTypingRequired,
                onTypingComplete: _onCurrentSectionComplete,
              ),
            ),
          ],
        ),
      );
    } else {
      return Padding(
        padding: sectionSpacing,
        child: SelectableText.rich(
          TextSpan(
            children:
                _buildTypewriterSpans(contentText, sectionName: sectionName),
          ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: FontManager.s16,
            color: Colors.black,
          ),
        ),
      );
    }
  }

  Widget _buildCurrentTypingSection(
    bool isOptionsSection,
    String? sectionName,
    String contentText,
    ParsedSection section,
  ) {
    if (isOptionsSection) {
      final lines = _parseOptionsWithDecorators(section);
      final spans = _buildSectionTitleSpans(sectionName);

      return Padding(
        padding: sectionSpacing,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.isTypingRequired && spans.isNotEmpty)
              SelectableTypewriterText(
                key: ValueKey(_currentSectionIndex + 10000),
                textSpans: spans,
                speed: widget.typingSpeed,
                onNewline: widget.onNewLine,
              )
            else if (spans.isNotEmpty)
              SelectableText.rich(
                TextSpan(children: spans),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: FontManager.s16,
                  color: Colors.black,
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(
                  top: AppSpacing.xxs, bottom: AppSpacing.sm),
              child: TypingCheckboxList(
                lines: lines,
                typingSpeed: widget.typingSpeed,
                onChanged: widget.onOptionChanged,
                isEnabled: widget.isLast,
                isTypingRequired: widget.isTypingRequired,
                onTypingComplete: _onCurrentSectionComplete,
              ),
            ),
          ],
        ),
      );
    } else {
      return Padding(
        padding: sectionSpacing,
        child: widget.isTypingRequired
            ? SelectableTypewriterText(
                key: ValueKey(_currentSectionIndex),
                textSpans: _buildTypewriterSpans(contentText,
                    sectionName: sectionName),
                speed: widget.typingSpeed,
                onNewline: widget.onNewLine,
                onComplete: _onCurrentSectionComplete,
              )
            : SelectableText.rich(
                TextSpan(
                  children: _buildTypewriterSpans(contentText,
                      sectionName: sectionName),
                ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: FontManager.s16,
                  color: Colors.black,
                ),
              ),
      );
    }
  }

  List<InlineSpan> _buildSectionTitleSpans(String? sectionName) {
    if (sectionName == null || sectionName.isEmpty) return [];
    return [
      TextSpan(
        text:
            sectionName.replaceAll(":", "").replaceAll("_", " ").replaceAll("\n", "").toTitleCase(),
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: FontManager.s18,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          height: 1.2,
        ),
      )
    ];
  }

  List<InlineSpan> _buildTypewriterSpans(String content,
      {String? sectionName}) {
    final spans = <InlineSpan>[];

    if (sectionName != null && sectionName.isNotEmpty) {
      spans.add(TextSpan(
        text:
            '${sectionName.replaceAll(":", "").replaceAll("_", " ").replaceAll("\n", "").toTitleCase()}\n',
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: FontManager.s18,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          height: 2,
        ),
      ));
    }

    final lines = content
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .toList();

    spans.addAll(lines.map((line) => TextSpan(
          text: '${line.replaceAll("**", "")}\n',
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: FontManager.s16,
            color: Colors.black,
          ),
        )));

    return spans;
  }

  List<RichOptionLine> _parseOptionsWithDecorators(ParsedSection section) {
    return (section.content as String)
        .split('\n')
        .map((line) {
          final trimmed = line.trim();
          if (trimmed.startsWith('□')) {
            return RichOptionLine(
              text: trimmed.replaceAll('□', '').replaceAll("**", "").trim(),
              isCheckbox: true,
              isRadio: false,
            );
          } else if (trimmed.startsWith('○')) {
            return RichOptionLine(
              text: trimmed.replaceAll('○', '').replaceAll("**", "").trim(),
              isCheckbox: false,
              isRadio: true,
            );
          } else {
            return RichOptionLine(
              text: trimmed.replaceAll("**", ""),
              isCheckbox: false,
              isRadio: false,
            );
          }
        })
        .where((line) => line.text.isNotEmpty)
        .toList();
  }
}

class ParsedSection {
  final String name;
  final dynamic content;

  ParsedSection({required this.name, required this.content});

  bool get isCheckboxSection =>
      name.toLowerCase().replaceAll('_', '').contains('selectionoptions');

  bool get isIgnored =>
      name.toLowerCase().trim().contains('thinking') || name.trim().isEmpty;

  bool get isRenderable => !isIgnored;
}
