import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/constants.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';
import '../../../l10n/app_localizations.dart';
import '../../../providers/auth_provider.dart';
import '../../../services/image_service.dart';
import '../../../ui_components/inputs/app_text_field.dart';
import '../../../ui_components/theme/app_theme.dart';
import '../../../theme/spacing.dart';
import '../../../utils/logger.dart';
import '../../../utils/navigation_service.dart';
import '../../../utils/validators.dart';
import '../../../widgets/auth/auth_button.dart';
import '../../../widgets/auth/auth_link.dart';
import '../../../widgets/auth/password_field.dart';
import '../../../widgets/auth/validated_text_field.dart';
import '../../../widgets/common/loading_overlay.dart';

class WebRegisterScreen extends StatefulWidget {
  const WebRegisterScreen({super.key});

  @override
  State<WebRegisterScreen> createState() => _WebRegisterScreenState();
}

class _WebRegisterScreenState extends State<WebRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _mobileController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _orgNameController = TextEditingController(text: 'org_it');

  String _selectedRole = AppConstants.getRoles().first;

  File? _profileImage;
  String? _base64Image;

  bool _isManualLoading = false; // Manual loading state for testing

  final _imageService = ImageService();

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _mobileController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _orgNameController.dispose();
    super.dispose();
  }

  Future<void> _selectProfileImage() async {
    try {
      final image = await _imageService.showImagePickerDialog(context);
      if (image != null) {
        // Convert image to base64 for API
        final bytes = await image.readAsBytes();
        final base64Image = base64Encode(bytes);

        setState(() {
          _profileImage = image;
          _base64Image = base64Image;
        });

        Logger.info('Profile image selected and converted to base64');
      }
    } catch (e) {
      Logger.error('Error selecting profile image: $e');
    }
  }

  Future<void> _register() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Set manual loading state to true
        setState(() {
          _isManualLoading = true;
        });

        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Debug log for initial loading state
        Logger.info('Initial loading state: ${authProvider.isLoading}');

        final firstName = _firstNameController.text.trim();
        final lastName = _lastNameController.text.trim();
        final email = _emailController.text.trim();
        final username = _usernameController.text.trim();
        final mobile = _mobileController.text.trim();
        final password = _passwordController.text;
        final organization = _orgNameController.text.trim();

        Logger.info('Attempting registration for email: $email');

        final success = await authProvider.register(
          name: '$firstName $lastName',
          email: email,
          mobile: mobile,
          password: password,
          username: username,
          role: _selectedRole,
          profilePicture: _base64Image,
          organization: organization,
        );

        // Debug log for loading state after registration
        Logger.info(
            'Loading state after registration: ${authProvider.isLoading}');

        // Set manual loading state to false
        // if (mounted) {
        setState(() {
          _isManualLoading = false;
        });
        // }

        if (success) {
          Logger.info('Registration successful, navigating to login screen');

          // Show success dialog
          _showSuccessDialog();
        } else {
          Logger.info('Registration failed');

          // Show error dialog
          _showErrorDialog(
              authProvider.error ?? 'Registration failed. Please try again.');
        }
      } catch (e) {
        // Make sure to reset manual loading state in case of error
        // if (mounted) {
        setState(() {
          _isManualLoading = false;
        });
        // }

        Logger.error('Error during registration process: $e');
        // Show error dialog for exceptions
        _showErrorDialog(
            'An error occurred during registration. Please try again.');
      } finally {
        // Make sure loading state is reset
        if (_isManualLoading) {
          setState(() {
            _isManualLoading = false;
          });
        }
      }
    } else {
      Logger.info('Form validation failed');
    }
  }

  // Show success dialog
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => CustomAlertDialog(
        title: 'common.success',
        content: 'auth.registrationSuccess',
        onClose: () => Navigator.of(context).pop(),
        primaryButtonText: 'auth.login',
        onPrimaryPressed: () {
          Navigator.of(context).pop();
          NavigationService.navigateToLogin();
        },
        // secondaryButtonText: 'Cancel',
        // onSecondaryPressed: () => Navigator.of(context).pop(),
      ),
    );

    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AlertDialog(
    //       title: Row(
    //         children: [
    //           Icon(Icons.check_circle,
    //               color: Theme.of(context).colorScheme.primary),
    //           const SizedBox(width: 8),
    //           Text(context.tr('common.success')),
    //         ],
    //       ),
    //       content: Text(
    //         context.tr('auth.registrationSuccess'),
    //       ),
    //       actions: [
    //         TextButton(
    //           onPressed: () {
    //             Navigator.of(context).pop();
    //             NavigationService.navigateToLogin();
    //           },
    //           child: Text(context.tr('auth.login')),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  // Show error dialog
  void _showErrorDialog(String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => CustomAlertDialog(
        title: 'common.error',
        content: errorMessage,
        onClose: () => Navigator.of(context).pop(),
        primaryButtonText: 'common.close',
        onPrimaryPressed: () {
          // Handle primary action
          Navigator.of(context).pop();
        },
        // secondaryButtonText: 'Cancel',
        // onSecondaryPressed: () => Navigator.of(context).pop(),
      ),
    );

    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AlertDialog(
    //       title: Row(
    //         children: [
    //           Icon(Icons.error_outline, color: AppTheme.errorColor),
    //           const SizedBox(width: 8),
    //           Text(context.tr('common.error')),
    //         ],
    //       ),
    //       content: Text(errorMessage),
    //       actions: [
    //         TextButton(
    //           onPressed: () {
    //             Navigator.of(context).pop();
    //           },
    //           child: Text(context.tr('common.close')),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // Debug log to check loading state
        Logger.info('AuthProvider loading state: ${authProvider.isLoading}');
        return NSLKnowledgeLoaderWrapper(
          isLoading: authProvider.isLoading || _isManualLoading,
          child: Scaffold(
            body: Stack(
              children: [
                Row(
                  children: [
                    // Left panel with background and branding
                    Expanded(
                      flex: 5,
                      child: Container(
                        decoration: BoxDecoration(
                          // color: Theme.of(context).colorScheme.primary,
                          color: Colors.white,
                          // Use a gradient as fallback if image is not available
                          // gradient: LinearGradient(
                          //   begin: Alignment.topLeft,
                          //   end: Alignment.bottomRight,
                          //   colors: [
                          //     Theme.of(context).colorScheme.primary,
                          //     Theme.of(context).colorScheme.primary.withAlpha(178),
                          //   ],
                          // ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Logo or App Name
                              // Text(
                              //   'NSL',
                              //   style: Theme.of(context)
                              //       .textTheme
                              //       .displayLarge
                              //       ?.copyWith(
                              //         color: Theme.of(context).colorScheme.onPrimary,
                              //         fontWeight: FontWeight.bold,
                              //         fontSize: 72,
                              //       ),
                              // ),
                              // Image(
                              //   image: AssetImage('assets/images/logo.png'),
                              //   height: 200,
                              // ),

                              SvgPicture.asset(
                                'assets/images/login_logo_new.svg',
                              ),
                              // width: 200,height: 200,),
                              const SizedBox(height: AppSpacing.xl),
                              SvgPicture.asset(
                                'assets/images/nsl_login_illustrations.svg',
                              ),
                              // Image(
                              //   image: AssetImage('assets/images/logo.png'),
                              //   height: 200,
                              // ),
                              const SizedBox(height: AppSpacing.xl),
                              Text(
                                'Welcome to the,',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.headlineSmall(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                ),
                                // style: Theme.of(context)
                                //     .textTheme
                                //     .displayLarge
                                //     ?.copyWith(
                                //       color: AppColors.black,
                                //       fontWeight: FontWeight.bold,
                                //       fontSize: 22,
                                //     ),
                              ),
                              const SizedBox(height: AppSpacing.xxs),
                              Text(
                                textAlign: TextAlign.center,
                                'World’s First \nSolution Generative Model',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.headlineSmall(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.textBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                                // style: Theme.of(context)
                                //     .textTheme
                                //     .displayLarge
                                //     ?.copyWith(
                                //       color: AppColors.textBlue,
                                //       fontWeight: FontWeight.bold,
                                //       fontSize: 22,
                                //     ),
                              ),

                              const SizedBox(height: AppSpacing.md),
                              Text(
                                'Patented Across Us, Europe, Africa, Asia, Australia \nSolutions Is Natural Language - Without Writing Or Question A Single Line Of Code \nAsk Me About Nsl Invention & Constructs.',
                                textAlign: TextAlign.center,
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                ),
                                // style: Theme.of(context)
                                //     .textTheme
                                //     .titleMedium
                                //     ?.copyWith(
                                //       color: AppColors.black,
                                //       fontWeight: FontWeight.normal,
                                //       fontSize: 16,
                                //     ),
                              ),
                              // Container(
                              //   width: 400,
                              //   padding: const EdgeInsets.all(AppSpacing.lg),
                              //   decoration: BoxDecoration(
                              //     color: Theme.of(context)
                              //         .colorScheme
                              //         .onPrimary
                              //         .withAlpha(25),
                              //     borderRadius:
                              //         BorderRadius.circular(AppTheme.borderRadiusL),
                              //   ),
                              //   child: Column(
                              //     children: [
                              //       Text(
                              //         'Welcome to NSL Platform',
                              //         style: Theme.of(context)
                              //             .textTheme
                              //             .titleLarge
                              //             ?.copyWith(
                              //               color: Theme.of(context)
                              //                   .colorScheme
                              //                   .onPrimary,
                              //               fontWeight: FontWeight.bold,
                              //             ),
                              //       ),
                              //       const SizedBox(height: AppSpacing.md),
                              //       Text(
                              //         'Sign in to access your account and start creating solutions, managing transactions, and more.',
                              //         style: Theme.of(context)
                              //             .textTheme
                              //             .bodyMedium
                              //             ?.copyWith(
                              //               color: Theme.of(context)
                              //                   .colorScheme
                              //                   .onPrimary
                              //                   .withAlpha(230),
                              //             ),
                              //         textAlign: TextAlign.center,
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      ),
                      // child: Container(
                      //   decoration: BoxDecoration(
                      //     color: Theme.of(context).colorScheme.primary,
                      //     // Use a gradient as fallback
                      //     gradient: LinearGradient(
                      //       begin: Alignment.topLeft,
                      //       end: Alignment.bottomRight,
                      //       colors: [
                      //         Theme.of(context).colorScheme.primary,
                      //         Theme.of(context).colorScheme.primary.withAlpha(178),
                      //       ],
                      //     ),
                      //   ),
                      //   child: Center(
                      //     child: Column(
                      //       mainAxisAlignment: MainAxisAlignment.center,
                      //       children: [
                      //         // Logo or App Name
                      //         Text(
                      //           'NSL',
                      //           style: Theme.of(context)
                      //               .textTheme
                      //               .displayLarge
                      //               ?.copyWith(
                      //                 color: Theme.of(context).colorScheme.onPrimary,
                      //                 fontWeight: FontWeight.bold,
                      //                 fontSize: 72,
                      //               ),
                      //         ),
                      //         const SizedBox(height: AppSpacing.md),
                      //         Text(
                      //           'Next-Generation Solutions Platform',
                      //           style: Theme.of(context)
                      //               .textTheme
                      //               .headlineSmall
                      //               ?.copyWith(
                      //                 color: Theme.of(context)
                      //                     .colorScheme
                      //                     .onPrimary
                      //                     .withAlpha(230),
                      //               ),
                      //         ),
                      //         const SizedBox(height: AppSpacing.xl),
                      //         Container(
                      //           width: 400,
                      //           padding: const EdgeInsets.all(AppSpacing.lg),
                      //           decoration: BoxDecoration(
                      //             color: Theme.of(context)
                      //                 .colorScheme
                      //                 .onPrimary
                      //                 .withAlpha(25),
                      //             borderRadius:
                      //                 BorderRadius.circular(AppTheme.borderRadiusL),
                      //           ),
                      //           child: Column(
                      //             children: [
                      //               Text(
                      //                 'Join NSL Platform',
                      //                 style: Theme.of(context)
                      //                     .textTheme
                      //                     .titleLarge
                      //                     ?.copyWith(
                      //                       color: Theme.of(context)
                      //                           .colorScheme
                      //                           .onPrimary,
                      //                       fontWeight: FontWeight.bold,
                      //                     ),
                      //               ),
                      //               const SizedBox(height: AppSpacing.md),
                      //               Text(
                      //                 'Create an account to access powerful tools for solution creation, transaction management, and more.',
                      //                 style: Theme.of(context)
                      //                     .textTheme
                      //                     .bodyMedium
                      //                     ?.copyWith(
                      //                       color: Theme.of(context)
                      //                           .colorScheme
                      //                           .onPrimary
                      //                           .withAlpha(230),
                      //                     ),
                      //                 textAlign: TextAlign.center,
                      //               ),
                      //             ],
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                    ),

                    // Right panel with registration form
                    Expanded(
                      flex: 5,
                      child: Container(
                        // color: Theme.of(context).colorScheme.surface,
                        color: AppColors.greyBg,
                        child: Center(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.all(AppTheme.spacingL),
                            child: ConstrainedBox(
                              constraints: const BoxConstraints(maxWidth: 500),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // Title Text
                                  Text(
                                    context.tr('auth.signUp'),
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.headlineMedium(context),
                                      fontWeight: FontManager.bold,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: AppColors.textBlue2,
                                    ),
                                    // style: Theme.of(context)
                                    //     .textTheme
                                    //     .displayMedium
                                    //     ?.copyWith(
                                    //       fontWeight: FontWeight.bold,
                                    //       color: AppColors.textBlue2,
                                    //     ),
                                    textAlign: TextAlign.left,
                                  ),
                                  const SizedBox(height: AppTheme.spacingXs),

                                  // Subtitle Text
                                  // Text(
                                  //   'Fill in your details to register',
                                  //   style: Theme.of(context)
                                  //       .textTheme
                                  //       .bodyLarge
                                  //       ?.copyWith(
                                  //         color: Theme.of(context)
                                  //             .colorScheme
                                  //             .onSurface
                                  //             .withAlpha(178),
                                  //       ),
                                  //   textAlign: TextAlign.center,
                                  // ),
                                  const SizedBox(height: AppTheme.spacingL),

                                  // Profile Picture
                                  // Center(
                                  //   child: Column(
                                  //     children: [
                                  //       GestureDetector(
                                  //         onTap: !authProvider.isLoading
                                  //             ? _selectProfileImage
                                  //             : null,
                                  //         child: Stack(
                                  //           children: [
                                  //             Container(
                                  //               width: 120,
                                  //               height: 120,
                                  //               decoration: BoxDecoration(
                                  //                 color: Theme.of(context)
                                  //                     .colorScheme
                                  //                     .primary
                                  //                     .withAlpha(25),
                                  //                 shape: BoxShape.circle,
                                  //                 border: Border.all(
                                  //                   color: Theme.of(context)
                                  //                       .colorScheme
                                  //                       .primary
                                  //                       .withAlpha(50),
                                  //                   width: 2,
                                  //                 ),
                                  //               ),
                                  //               child: ClipOval(
                                  //                 child: _profileImage != null
                                  //                     ? Image.file(
                                  //                         _profileImage!,
                                  //                         fit: BoxFit.cover,
                                  //                         width: 120,
                                  //                         height: 120,
                                  //                       )
                                  //                     : Icon(
                                  //                         Icons.person,
                                  //                         size: 60,
                                  //                         color: Theme.of(context)
                                  //                             .colorScheme
                                  //                             .primary,
                                  //                       ),
                                  //               ),
                                  //             ),
                                  //             Positioned(
                                  //               bottom: 0,
                                  //               right: 0,
                                  //               child: Container(
                                  //                 decoration: BoxDecoration(
                                  //                   color: Theme.of(context)
                                  //                       .colorScheme
                                  //                       .primary,
                                  //                   shape: BoxShape.circle,
                                  //                 ),
                                  //                 padding: const EdgeInsets.all(8),
                                  //                 child: Icon(
                                  //                   Icons.camera_alt,
                                  //                   color: Theme.of(context)
                                  //                       .colorScheme
                                  //                       .onPrimary,
                                  //                   size: 20,
                                  //                 ),
                                  //               ),
                                  //             ),
                                  //           ],
                                  //         ),
                                  //       ),
                                  //       if (_profileImage != null)
                                  //         Padding(
                                  //           padding: const EdgeInsets.only(top: 8.0),
                                  //           child: Row(
                                  //             mainAxisAlignment:
                                  //                 MainAxisAlignment.center,
                                  //             children: [
                                  //               TextButton.icon(
                                  //                 icon: const Icon(Icons.refresh),
                                  //                 label: const Text('Change'),
                                  //                 onPressed: !authProvider.isLoading
                                  //                     ? _selectProfileImage
                                  //                     : null,
                                  //               ),
                                  //               TextButton.icon(
                                  //                 icon: const Icon(Icons.delete),
                                  //                 label: const Text('Remove'),
                                  //                 onPressed: !authProvider.isLoading
                                  //                     ? () {
                                  //                         setState(() {
                                  //                           _profileImage = null;
                                  //                           _base64Image = null;
                                  //                         });
                                  //                       }
                                  //                     : null,
                                  //               ),
                                  //             ],
                                  //           ),
                                  //         ),
                                  //     ],
                                  //   ),
                                  // ),
                                  const SizedBox(height: AppTheme.spacingM),

                                  // Error Message (if any)
                                  // if (authProvider.error != null) ...[
                                  //   Container(
                                  //     padding:
                                  //         const EdgeInsets.all(AppTheme.spacingM),
                                  //     decoration: BoxDecoration(
                                  //       color: AppTheme.errorColor.withAlpha(25),
                                  //       borderRadius: BorderRadius.circular(
                                  //           AppTheme.borderRadiusM),
                                  //     ),
                                  //     child: Text(
                                  //       authProvider.error!,
                                  //       style: Theme.of(context)
                                  //           .textTheme
                                  //           .bodyMedium
                                  //           ?.copyWith(
                                  //             color: AppTheme.errorColor,
                                  //           ),
                                  //       textAlign: TextAlign.center,
                                  //     ),
                                  //   ),
                                  //   const SizedBox(height: AppTheme.spacingM),
                                  // ],

                                  // Registration Form
                                  _buildRegistrationForm(context, authProvider),

                                  const SizedBox(height: AppTheme.spacingXl),

                                  // Footer
                                  // Text(
                                  //   '© ${DateTime.now().year} NSL Platform. All rights reserved.',
                                  //   style: Theme.of(context).textTheme.bodySmall,
                                  //   textAlign: TextAlign.center,
                                  // ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // Full-screen loading overlay
                // if (authProvider.isLoading || _isManualLoading)
                //   LoadingOverlay(
                //     text: context.tr('common.loading'),
                //     color: AppColors.textBlue2,
                //   ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRegistrationForm(
      BuildContext context, AuthProvider authProvider) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // First Name and Last Name Fields
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ValidatedTextField(
                  controller: _firstNameController,
                  label: context.tr('auth.firstName'),
                  placeholder: 'Enter your first name',
                  type: AppTextFieldType.text,
                  textInputAction: TextInputAction.next,
                  // prefix: const Icon(Icons.person_outline),
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: (value) =>
                      Validators.validateRequired(value, 'First name'),
                  isRequired: true,
                  noBorder: true,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              // Last Name Field
              Expanded(
                child: ValidatedTextField(
                  controller: _lastNameController,
                  label: context.tr('auth.lastName'),
                  placeholder: 'Enter your last name',
                  type: AppTextFieldType.text,
                  textInputAction: TextInputAction.next,
                  // prefix: const Icon(Icons.account_circle_outlined),
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: (value) =>
                      Validators.validateRequired(value, 'Last name'),
                  isRequired: true,
                  noBorder: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Email and Username Fields
          Row(
            children: [
              // Email Field
              Expanded(
                child: ValidatedTextField(
                  controller: _emailController,
                  label: context.tr('auth.email'),
                  placeholder: 'Enter your email',
                  type: AppTextFieldType.email,
                  textInputAction: TextInputAction.next,
                  // prefix: const Icon(Icons.email_outlined),
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: Validators.validateEmail,
                  isRequired: true,
                  noBorder: true,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              // Username Field
              Expanded(
                child: ValidatedTextField(
                  controller: _usernameController,
                  label: context.tr('auth.username'),
                  placeholder: 'Choose a username',
                  type: AppTextFieldType.text,
                  textInputAction: TextInputAction.next,
                  // prefix: const Icon(Icons.account_circle_outlined),
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: Validators.validateUsername,
                  isRequired: true,
                  noBorder: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),

          Row(
            children: [
              // Password Field
              Expanded(
                child: PasswordField(
                  controller: _passwordController,
                  label: context.tr('auth.password'),
                  placeholder: 'Enter your password',
                  textInputAction: TextInputAction.next,
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: Validators.validatePassword,
                  isRequired: true,
                  noBorder: true,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),

              // Confirm Password Field
              Expanded(
                child: PasswordField(
                  controller: _confirmPasswordController,
                  label: context.tr('auth.confirmPassword'),
                  placeholder: 'Confirm your password',
                  textInputAction: TextInputAction.done,
                  enabled: !(authProvider.isLoading || _isManualLoading),
                  validator: (value) => Validators.validatePasswordMatch(
                    value,
                    _passwordController.text,
                  ),
                  isRequired: true,
                  noBorder: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          // Mobile Field
          Row(
            children: [
              // Expanded(
              //   child: ValidatedTextField(
              //     controller: _mobileController,
              //     label: 'Mobile Number',
              //     placeholder: 'Enter your mobile number',
              //     type: AppTextFieldType.number,
              //     textInputAction: TextInputAction.next,
              //     // prefix: const Icon(Icons.phone_outlined),
              //     enabled: !authProvider.isLoading,
              //     validator: Validators.validateMobile,
              //     inputFormatters: [
              //       FilteringTextInputFormatter.digitsOnly,
              //       LengthLimitingTextInputFormatter(10),
              //     ],
              //     isRequired: true,
              //     noBorder: true,
              //   ),
              // ),

              // const SizedBox(width: AppTheme.spacingM),

              // Organization Field
              Expanded(
                child: ValidatedTextField(
                  controller: _orgNameController,
                  label: context.tr('auth.organization'),
                  placeholder: 'Enter your organization name',
                  type: AppTextFieldType.text,
                  textInputAction: TextInputAction.next,
                  // prefix: const Icon(Icons.business_outlined),
                  enabled: false,
                  //  !authProvider.isLoading,
                  validator: Validators.validateOrganization,
                  isRequired: true,
                  noBorder: true,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: // Role Dropdown
                    Column(
                  children: [
                    Text(''),
                    DropdownButtonFormField<String>(
                      value: _selectedRole,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(color: AppColors.black),
                      decoration: InputDecoration(
                        fillColor: AppColors.white,
                        labelText: context.tr('auth.role'),
                        // prefixIcon: const Icon(Icons.assignment_ind_outlined),
                        border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                            borderSide: BorderSide.none),
                        enabledBorder: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                          // borderSide: BorderSide.none
                          borderSide: BorderSide(
                            color: Theme.of(context)
                                .colorScheme
                                .outline
                                .withAlpha(50),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                            borderSide: BorderSide.none

                            // borderSide: BorderSide(
                            //   color: Theme.of(context).colorScheme.primary,
                            // ),
                            ),
                      ),
                      dropdownColor: AppColors.white,
                      items: AppConstants.getRoles()
                          .cast<String>()
                          .map<DropdownMenuItem<String>>((String role) {
                        return DropdownMenuItem<String>(
                          value: role,
                          child: Text(
                            role,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                              fontFamily:
                                  'Inter', // optional: use your preferred font
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: authProvider.isLoading
                          ? null
                          : (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedRole = newValue;
                                });
                              }
                            },
                    ),
                  ],
                ),
              )
            ],
          ),
          // const SizedBox(height: AppTheme.spacingL),

          const SizedBox(height: AppTheme.spacingL),

          // Register Button
          Center(
            child: SizedBox(
              //height: 42,
              width: 100,
              // padding: const EdgeInsets.symmetric(horizontal: 150),
              child: AuthButton(
                text: context.tr('auth.signUp'),
                onPressed: (authProvider.isLoading || _isManualLoading)
                    ? null
                    : _register,
                isLoading: authProvider.isLoading || _isManualLoading,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Login Link
          AuthLink(
            text: "${context.tr('auth.alreadyRegistered')} ",
            linkText: context.tr('auth.login'),
            onPressed: (authProvider.isLoading || _isManualLoading)
                ? null
                : () {
                    NavigationService.navigateToLogin();
                    Logger.info('Login link tapped');
                  },
            isDisabled: authProvider.isLoading || _isManualLoading,
          ),
        ],
      ),
    );
  }
}
