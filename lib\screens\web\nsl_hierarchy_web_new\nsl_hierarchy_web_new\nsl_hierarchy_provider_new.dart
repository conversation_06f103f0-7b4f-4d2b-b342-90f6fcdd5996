import 'package:flutter/material.dart';
import 'package:nsl/providers/base_provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/services/nsl_hierarchy_api_service_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/services/nsl_hierarchy_data_transformer_new.dart';

import 'package:nsl/utils/logger.dart';

class GoLoPanel {
  final String id;
  final String metricType;

  GoLoPanel({
    required this.id,
    required this.metricType,
  });
}

class NslHierarchyProviderNew extends BaseProvider {
  // Core hierarchy data
  List<NSLHierarchyData1> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  
  // Selection state
  NSLHierarchyData1? _selectedNode;
  String? _selectedNodeId;
  Set<String> _infoTappedNodes = {};
  String? _selectedInfoNodeId;
  List<String> _selectedPathIds = [];

  bool _hasInfoTapped = false;
  
  // Panel state
  bool _showSidePanel = false;
  List<GoLoPanel> _activeGoLoPanels = [];
  int _clearSelectionTrigger = 0;
  
  // API data
  NslTreeSidePanel? _nodeDetails;
  bool _isLoadingNodeDetails = false;
  MetricsInfo? _nodeTransactions;
  bool _isLoadingTransactions = false;
  
  // Root node data (always available for top bar)
  NslTreeSidePanel? _rootNodeDetails;
  MetricsInfo? _rootNodeTransactions;
  
  // Search state
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  
  // UI state
  Map<String, dynamic>? _organizationalStructure;
  Map<String, dynamic>? _systemInfo;
  
  // Side panel dimensions
  double _sidePanelWidth = 0.0;
  double _minSidePanelWidth = 0.0;
  double _maxSidePanelWidth = 0.0;
  
  // Chart state
  bool _isInfoExpansion = false;
  final Map<String, double> _subtreeWidths = {};
  Map<String, double> _levelCenterY = {};
  Map<String, double> _dynamicSeparatorPositions = {};
  Map<String, double> _actualSeparatorPositions = {};
  List<String> _availableLevels = [];
  final Map<String, GlobalKey> _separatorKeys = {};
  bool _positionsInitialized = false;
  
  // Scroll controllers
  final ScrollController _horizontalScrollController = ScrollController();
  
  // Getters
  List<NSLHierarchyData1> get nslNodes => _nslNodes;
  NSLNode? get rootNode => _rootNode;
  NSLNode? get filteredRootNode => _filteredRootNode;
  NSLHierarchyData1? get selectedNode => _selectedNode;
  String? get selectedNodeId => _selectedNodeId;
  Set<String> get infoTappedNodes => _infoTappedNodes;
  String? get selectedInfoNodeId => _selectedInfoNodeId;
  List<String> get selectedPathIds => _selectedPathIds;
  bool get hasInfoTapped => _hasInfoTapped;
  bool get showSidePanel => _showSidePanel;
  List<GoLoPanel> get activeGoLoPanels => _activeGoLoPanels;
  int get clearSelectionTrigger => _clearSelectionTrigger;
  NslTreeSidePanel? get nodeDetails => _nodeDetails;
  bool get isLoadingNodeDetails => _isLoadingNodeDetails;
  MetricsInfo? get nodeTransactions => _nodeTransactions;
  bool get isLoadingTransactions => _isLoadingTransactions;
  String get searchQuery => _searchQuery;
  TextEditingController get searchController => _searchController;
  Map<String, dynamic>? get organizationalStructure => _organizationalStructure;
  Map<String, dynamic>? get systemInfo => _systemInfo;
  double get sidePanelWidth => _sidePanelWidth;
  double get minSidePanelWidth => _minSidePanelWidth;
  double get maxSidePanelWidth => _maxSidePanelWidth;
  bool get isInfoExpansion => _isInfoExpansion;
  Map<String, double> get subtreeWidths => _subtreeWidths;
  Map<String, double> get levelCenterY => _levelCenterY;
  Map<String, double> get dynamicSeparatorPositions => _dynamicSeparatorPositions;
  Map<String, double> get actualSeparatorPositions => _actualSeparatorPositions;
  List<String> get availableLevels => _availableLevels;
  Map<String, GlobalKey> get separatorKeys => _separatorKeys;
  bool get positionsInitialized => _positionsInitialized;
  ScrollController get horizontalScrollController => _horizontalScrollController;
  
  // Root node data getters (for top bar when side panel is closed)
  NslTreeSidePanel? get rootNodeDetails => _rootNodeDetails;
  MetricsInfo? get rootNodeTransactions => _rootNodeTransactions;

  NslHierarchyProviderNew() {
    _searchController.addListener(_onSearchChanged);
    loadNSLData();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _horizontalScrollController.dispose();
    super.dispose();
  }

  // Search functionality
  void _onSearchChanged() {
    _searchQuery = _searchController.text.trim();
    _buildFilteredTree();
    notifyListeners();
  }

  void clearSearch() {
    _searchController.clear();
  }

  // Core data loading
  Future<void> loadNSLData() async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        // Fetch data from API
        final apiResponse = await NslHierarchyApiServiceNew.fetchModules();
        
        if (apiResponse == null) {
          throw Exception('Failed to fetch data from API');
        }

        // Transform API data to hierarchy structure
        _rootNode = NslHierarchyDataTransformerNew.transformApiDataToHierarchy(apiResponse);
        
        if (_rootNode == null) {
          throw Exception('Failed to transform API data to hierarchy');
        }

        if (_rootNode != null) {
          _nslNodes = _rootNode!.originalData.getAllNodes();
        }

        // Create organizational structure
        _organizationalStructure = {
          'M4': {
            'level_name': 'Executive Level',
            'node': {
              'id': _rootNode?.id ?? 'owner',
              'title': _rootNode?.title ?? 'CEO Operations',
              'type': 'MODULE',
            }
          },
          'M3': {'level_name': 'Department Level'},
          'M2': {'level_name': 'Team Level'},
          'M1': {'level_name': 'Individual Level'},
        };

        // Create system info
        _systemInfo = {
          'default_time_range': {
            'from': DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
            'to': DateTime.now().toIso8601String(),
          },
          'last_updated': DateTime.now().toIso8601String(),
        };

        // Build filtered tree
        _buildFilteredTree();

        // Fetch root node details and transactions
        if (_rootNode != null) {
          await Future.wait([
            _fetchRootNodeDetails(_rootNode!.id),
            _fetchRootNodeTransactions(_rootNode!.id),
          ]);
        }
      },
      context: 'NslHierarchyProviderNew.loadNSLData',
    );
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      final foundNodes = _nslNodes
          .where((node) =>
              node.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              node.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundNodes.isNotEmpty) {
        _filteredRootNode = _findNodeInTree(_rootNode, foundNodes.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  NSLNode? _findNodeInTree(NSLNode? node, String nodeId) {
    if (node == null) return null;
    
    if (node.id == nodeId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findNodeInTree(child, nodeId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  // Node selection and interaction
  Future<void> onNodeTitleTap(NSLNode node) async {
    _selectedNode = node.originalData;
    _selectedNodeId = node.id;
    _showSidePanel = true;
    _activeGoLoPanels.clear();
    _clearSelectionTrigger++;
    _isLoadingNodeDetails = true;
    _isLoadingTransactions = true;
    _nodeDetails = null;
    _nodeTransactions = null;
    notifyListeners();

    try {
      final nodeDetailsTask = NslHierarchyApiServiceNew.fetchNodeDetails(node.id);
      
      final now = DateTime.now();
      final fromDate = now.subtract(Duration(days: 30));
      final fromUnix = fromDate.millisecondsSinceEpoch ~/ 1000;
      final toUnix = now.millisecondsSinceEpoch ~/ 1000;
      
      final transactionsTask = NslHierarchyApiServiceNew.fetchNodeTransactions(node.id, fromUnix, toUnix);
      
      final results = await Future.wait([nodeDetailsTask, transactionsTask]);
      final nodeDetails = results[0] as NslTreeSidePanel?;
      final transactions = results[1] as MetricsInfo?;
      
      _nodeDetails = nodeDetails;
      _nodeTransactions = transactions;
      _isLoadingNodeDetails = false;
      _isLoadingTransactions = false;
      notifyListeners();
    } catch (e) {
      Logger.error('Error fetching node data: $e');
      _isLoadingNodeDetails = false;
      _isLoadingTransactions = false;
      notifyListeners();
    }
  }

  void onNodeInfoTap(NSLNode node) {
    _isInfoExpansion = true;
    if (_selectedInfoNodeId == node.id) {
      _selectedInfoNodeId = null;
    } else {
      _selectedInfoNodeId = node.id;
    }
    
    // Toggle node expansion logic would go here
    // This would need to be implemented based on the existing logic
    notifyListeners();
  }

  void hideSidePanel() {
    _showSidePanel = false;
    _selectedNode = null;
    _selectedNodeId = null;
    _activeGoLoPanels.clear();
    _clearSelectionTrigger++;
    
    // Restore root node data for the top bar
    _nodeDetails = _rootNodeDetails;
    _nodeTransactions = _rootNodeTransactions;
    
    notifyListeners();
  }

  // GO/LO Panel management
  void onArrowTap(String metricType) {
    _activeGoLoPanels.clear();
    
    String normalizedMetricType = metricType;
    if (metricType.toLowerCase().contains('total go')) {
      normalizedMetricType = 'GOs';
    } else if (metricType.toLowerCase().contains('total lo')) {
      normalizedMetricType = 'LOs';
    } else if (metricType.toLowerCase().contains('go')) {
      normalizedMetricType = 'GOs';
    } else if (metricType.toLowerCase().contains('lo')) {
      normalizedMetricType = 'LOs';
    }
    
    _activeGoLoPanels.add(GoLoPanel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      metricType: normalizedMetricType,
    ));
    notifyListeners();
  }

  void closeGoLoPanel(String panelId) {
    _activeGoLoPanels.removeWhere((panel) => panel.id == panelId);
    _clearSelectionTrigger++;
    notifyListeners();
  }

  void clearArrowSelection() {
    // Called when GO/LO panels are closed to clear selection
    notifyListeners();
  }

  // API methods
  Future<void> _fetchRootNodeDetails(String nodeId) async {
    try {
      final nodeDetails = await NslHierarchyApiServiceNew.fetchNodeDetails(nodeId);
      _rootNodeDetails = nodeDetails;
      _nodeDetails = nodeDetails; // Also set current node details for initial load
      notifyListeners();
    } catch (e) {
      Logger.error('Error fetching root node details: $e');
    }
  }

  Future<void> _fetchRootNodeTransactions(String nodeId) async {
    try {
      final now = DateTime.now();
      final fromDate = now.subtract(Duration(days: 30));
      final fromUnix = fromDate.millisecondsSinceEpoch ~/ 1000;
      final toUnix = now.millisecondsSinceEpoch ~/ 1000;
      
      final transactions = await NslHierarchyApiServiceNew.fetchNodeTransactions(nodeId, fromUnix, toUnix);
      _rootNodeTransactions = transactions;
      _nodeTransactions = transactions; // Also set current node transactions for initial load
      notifyListeners();
    } catch (e) {
      Logger.error('Error fetching root node transactions: $e');
    }
  }

  Future<void> onDateRangeChanged(DateTime fromDate, DateTime toDate) async {
    if (_selectedNode == null) return;

    _isLoadingTransactions = true;
    _nodeTransactions = null;
    notifyListeners();

    try {
      final fromUnix = fromDate.millisecondsSinceEpoch ~/ 1000;
      final toUnix = toDate.millisecondsSinceEpoch ~/ 1000;
      
      final transactions = await NslHierarchyApiServiceNew.fetchNodeTransactions(
        _selectedNode!.id, 
        fromUnix, 
        toUnix
      );
      
      _nodeTransactions = transactions;
      _isLoadingTransactions = false;
      notifyListeners();
    } catch (e) {
      Logger.error('Error fetching transactions for date range: $e');
      _isLoadingTransactions = false;
      notifyListeners();
    }
  }

  // UI state management
  void updateSidePanelDimensions(double screenWidth) {
    final desiredSidePanelWidth = screenWidth * 0.48;
    _sidePanelWidth = desiredSidePanelWidth;
    _minSidePanelWidth = desiredSidePanelWidth * 0.8;
    _maxSidePanelWidth = desiredSidePanelWidth * 1.2;
    // Don't call notifyListeners here as this is called during build
  }

  // Chart-specific state management
  void setSelectedPathIds(List<String> pathIds) {
    _selectedPathIds = pathIds;
    notifyListeners();
  }

  void setInfoTappedNode(String nodeId, bool tapped) {
    if (tapped) {
      _infoTappedNodes.add(nodeId);
    } else {
      _infoTappedNodes.remove(nodeId);
    }
    notifyListeners();
  }

  void clearSubtreeWidths() {
    _subtreeWidths.clear();
  }

  void setSubtreeWidth(String nodeId, double width) {
    _subtreeWidths[nodeId] = width;
  }

  double? getSubtreeWidth(String nodeId) {
    return _subtreeWidths[nodeId];
  }

  void setLevelCenterY(Map<String, double> levelCenterY) {
    _levelCenterY = levelCenterY;
  }

  void setDynamicSeparatorPositions(Map<String, double> positions) {
    _dynamicSeparatorPositions = positions;
  }

  void setActualSeparatorPositions(Map<String, double> positions) {
    _actualSeparatorPositions = positions;
    if (positions.isNotEmpty && !_positionsInitialized) {
      _positionsInitialized = true;
      notifyListeners();
    }
  }

  void setAvailableLevels(List<String> levels) {
    _availableLevels = levels;
  }

  void setSeparatorKeys(Map<String, GlobalKey> keys) {
    _separatorKeys.clear();
    _separatorKeys.addAll(keys);
  }

  void setInfoExpansion(bool isInfoExpansion) {
    _isInfoExpansion = isInfoExpansion;
  }

  // Node expansion and tree manipulation
  NSLNode? updateRootNode(NSLNode newRootNode) {
    _rootNode = newRootNode;
    _filteredRootNode = newRootNode;
    notifyListeners();
    return _rootNode;
  }

  void updateSelectedPathIds(List<String> pathIds) {
    _selectedPathIds = pathIds;
    notifyListeners();
  }

  // Helper methods for chart functionality
  List<String> getPathToNode(String targetNodeId) {
    return _getVisualHierarchyPath(targetNodeId);
  }

  // Build path based on the actual visual hierarchy (what user sees)
  List<String> _getVisualHierarchyPath(String targetNodeId) {
    List<List<String>> allPossiblePaths = _findAllVisualPaths(targetNodeId);
    
    if (allPossiblePaths.isEmpty) {
      return [];
    }
    
    return _chooseBestPath(allPossiblePaths, targetNodeId);
  }

  // Find all possible visual paths to a target node
  List<List<String>> _findAllVisualPaths(String targetNodeId) {
    List<List<String>> allPaths = [];
    
    void findPathsRecursive(NSLNode currentNode, List<String> currentPath) {
      List<String> newPath = List.from(currentPath)..add(currentNode.id);
      
      if (currentNode.id == targetNodeId) {
        allPaths.add(newPath);
        return;
      }
      
      if (currentNode.isExpanded) {
        for (final child in currentNode.children) {
          findPathsRecursive(child, newPath);
        }
      }
    }
    
    if (_rootNode != null) {
      findPathsRecursive(_rootNode!, []);
    }
    return allPaths;
  }

  // Choose the best path from multiple possible paths
  List<String> _chooseBestPath(List<List<String>> allPaths, String targetNodeId) {
    if (allPaths.isEmpty) return [];
    if (allPaths.length == 1) return allPaths.first;
    
    List<String> bestPath = allPaths.first;
    int bestScore = _calculatePathScore(bestPath);
    
    for (final path in allPaths) {
      int score = _calculatePathScore(path);
      if (score > bestScore) {
        bestScore = score;
        bestPath = path;
      }
    }
    
    return bestPath;
  }

  // Calculate a score for a path (higher score = better path)
  int _calculatePathScore(List<String> path) {
    int score = 0;
    
    score -= path.length * 10;
    
    for (final nodeId in path) {
      NSLNode? node = findNodeById(nodeId);
      if (node != null && node.isExpanded) {
        score += 50;
      }
    }
    
    if (path.length >= 2) {
      String parentId = path[path.length - 2];
      NSLNode? parentNode = findNodeById(parentId);
      if (parentNode != null && parentNode.isExpanded) {
        score += 100;
      }
    }
    
    return score;
  }

  NSLNode? findNodeById(String nodeId) {
    return _findNodeInTree(_rootNode, nodeId);
  }

  String? getSelectedNodeLevel() {
    if (_selectedNodeId == null) return null;
    final selectedNode = findNodeById(_selectedNodeId!);
    return selectedNode?.level;
  }

  // Node expansion methods
  NSLNode _toggleNodeExpansionRecursive(NSLNode currentNode, String targetNodeId) {
    if (currentNode.id == targetNodeId) {
      return currentNode.copyWith(isExpanded: !currentNode.isExpanded);
    }
    final List<NSLNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild = _toggleNodeExpansionRecursive(child, targetNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }
    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  NSLNode _toggleNodeWithSiblingCollapse(NSLNode rootNode, String targetNodeId) {
    final targetNodeInfo = _findNodeWithParent(rootNode, targetNodeId);
    if (targetNodeInfo == null) return rootNode;
    final targetNode = targetNodeInfo['node'] as NSLNode;
    final parentNode = targetNodeInfo['parent'] as NSLNode?;

    if (targetNode.id == rootNode.id) {
      if (targetNode.isExpanded) {
        return _collapseAllChildren(rootNode.copyWith(isExpanded: false));
      } else {
        return _expandToDefaultLevel(rootNode);
      }
    }
    
    NSLNode updatedRoot = _toggleNodeExpansionRecursive(rootNode, targetNodeId);
    if (!targetNode.isExpanded && parentNode != null) {
      updatedRoot = _collapseSiblingsChildren(updatedRoot, parentNode.id, targetNodeId);
    }
    return updatedRoot;
  }

  Map<String, NSLNode?>? _findNodeWithParent(NSLNode currentNode, String targetNodeId, [NSLNode? parent]) {
    if (currentNode.id == targetNodeId) {
      return {'node': currentNode, 'parent': parent};
    }
    for (final child in currentNode.children) {
      final result = _findNodeWithParent(child, targetNodeId, currentNode);
      if (result != null) return result;
    }
    return null;
  }

  NSLNode _collapseSiblingsChildren(NSLNode currentNode, String parentNodeId, String expandedNodeId) {
    if (currentNode.id == parentNodeId) {
      final List<NSLNode> updatedChildren = [];
      for (final child in currentNode.children) {
        if (child.id == expandedNodeId) {
          updatedChildren.add(child);
        } else {
          final collapsedSibling = _collapseAllChildren(child.copyWith(isExpanded: false));
          updatedChildren.add(collapsedSibling);
        }
      }
      return currentNode.copyWith(children: updatedChildren);
    }
    
    final List<NSLNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild = _collapseSiblingsChildren(child, parentNodeId, expandedNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }
    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  NSLNode _collapseAllChildren(NSLNode node) {
    if (node.children.isEmpty) {
      return node;
    }
    final List<NSLNode> collapsedChildren = node.children.map((child) {
      return _collapseAllChildren(child.copyWith(isExpanded: false));
    }).toList();
    return node.copyWith(children: collapsedChildren);
  }

  NSLNode _expandToDefaultLevel(NSLNode node) {
    if (node.id == _rootNode?.id) {
      final collapsedChildren = node.children.map((child) {
        return _collapseAllChildren(child.copyWith(isExpanded: false));
      }).toList();
      
      return node.copyWith(
        isExpanded: true,
        children: collapsedChildren,
      );
    } else {
      final collapsedChildren = node.children.map((child) {
        return _collapseAllChildren(child.copyWith(isExpanded: false));
      }).toList();
      
      return node.copyWith(
        isExpanded: false,
        children: collapsedChildren,
      );
    }
  }

  // Enhanced onNodeInfoTap with tree manipulation
  void onNodeInfoTapWithExpansion(NSLNode node) {
    _isInfoExpansion = false;
     _selectedInfoNodeId = node.id;
    if (_selectedInfoNodeId == node.id) {
      _selectedInfoNodeId = null;
    } else {
      _selectedInfoNodeId = node.id;
    }
    
    if (_rootNode != null) {
      final newRoot = _toggleNodeWithSiblingCollapse(_rootNode!, node.id);
      _subtreeWidths.clear();
      _rootNode = newRoot;
      _filteredRootNode = newRoot;
    }
    
    notifyListeners();
  }
}
