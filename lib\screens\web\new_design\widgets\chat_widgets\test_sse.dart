import 'dart:convert';
// import 'package:fetch_client/fetch_client.dart';
import 'package:eventflux/client.dart';
import 'package:eventflux/enum.dart';
import 'package:eventflux/models/reconnect.dart';
import 'package:eventflux/models/response.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart';

// void main() async {
//   // Instantiate the FetchClient (CORS not needed for native Flutter apps, but needed for Flutter web)
//   final client = FetchClient(mode: RequestMode.cors);

//   // Define the API endpoint
//   final uri = Uri.parse('http://**********:8200/api/v3/brd/start/stream');

//   // Create the request body
//   final Map<String, dynamic> requestBody = {
//     "tenant": "t001",
//     "project": "proj1",
//     "user_id": "U3",
//     "initial_input":
//         "I need a comprehensive CRM system for my digital marketing agency. We are a team of 25 people including 5 account managers, 8 creative designers, 4 developers, 3 project managers, 2 data analysts, 2 marketing specialists, and 1 operations manager. We are based in New York and serve enterprise clients across North America. We need lead management, client onboarding, project tracking, campaign management, and performance analytics.",
//     "preferred_model": "claude"
//   };

//   // Create the HTTP Request
//   final request = Request('POST', uri)
//     ..headers.addAll({
//       'accept': 'application/json',
//       'Content-Type': 'application/json',
//     })
//     ..body = jsonEncode(requestBody);

//   try {
//     // Send the request
//     final response = await client.send(request);

//     // Print headers and URL
//     print('Redirected: ${response.redirected}');
//     print('Response URL: ${response.url}');

//     // Decode and print the response body
//     final body = await utf8.decodeStream(response.stream);
//     print('Response Body: $body');
//   } catch (e) {
//     print('Error: $e');
//   } finally {
//     client.close();
//   }
// }

void main() {
  // EventFlux.instance.connect(
  //   EventFluxConnectionType.get,
  //   'http://localhost:3000/api/v3/brd/start/stream?tenant=t001&project=proj1&user_id=U3&initial_input=hi&preferred_model=claude',
  //   onSuccessCallback: (EventFluxResponse? response) {
  //     response?.stream?.listen((data) {
  //       print('SSE data: ${data.data} ${data.event}');
  //     });
  //   },
  // );
  EventFlux.instance.connect(
    EventFluxConnectionType.post,
    // 'http://**********:8200/api/v3/brd/start/stream',
    'http://localhost:3000/api/v3/brd/start/stream',
    // 'http://**********:8100/api/enhanced-brd/start',
    header: {
      'Content-Type': 'application/json',
      'Accept': 'application/octet-stream'
    },
    body: {
      "tenant": "t001",
      "project": "proj1",
      "user_id": "U3",
      "initial_input":
          "I need a comprehensive CRM system for my digital marketing agency. We are a team of 25 people including 5 account managers, 8 creative designers, 4 developers, 3 project managers, 2 data analysts, 2 marketing specialists, and 1 operations manager. We are based in New York and serve enterprise clients across North America. We need lead management, client onboarding, project tracking, campaign management, and performance analytics.",
      "preferred_model": "claude"
      // "project_id": "project_id_001",
      // "initial_input": "create soluton ecommerce application",
      // "user_id": "U3",
      // "tenant_id": "t001",
      // "industry": "general",
      // "company_size": "medium"
    },
    onSuccessCallback: (EventFluxResponse? response) {
      response?.stream?.listen((data) {
        // Your data is now in the spotlight!
        print('SSE data: ${data.data} ${data.event} ');
      });
    },
  );
  // EventFlux.instance.connect(
  //   EventFluxConnectionType.post,
  //   'http://localhost:3000/sse',
  //   // body: {
  //   //   "tenant": "t001",
  //   //   "project": "proj1",
  //   //   "user_id": "U3",
  //   //   "initial_input":
  //   //       "I need a comprehensive CRM system for my digital marketing agency. We are a team of 25 people including 5 account managers, 8 creative designers, 4 developers, 3 project managers, 2 data analysts, 2 marketing specialists, and 1 operations manager. We are based in New York and serve enterprise clients across North America. We need lead management, client onboarding, project tracking, campaign management, and performance analytics.",
  //   //   "preferred_model": "claude"
  //   // },
  //   onSuccessCallback: (EventFluxResponse? response) {
  //     response?.stream?.listen((data) {
  //       // Your data is now in the spotlight!
  //       print("${data.id} data ${data.data} event ${data.event} ");
  //     });
  //   },
  // );
  // runApp(MaterialApp(home: TestSSE(),));
}

class TestSSE extends StatelessWidget {
  const TestSSE({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        // body: StreamBuilder(stream: getStream, builder: (context, snapshot) {
        //   if(snapshot.connectionState==ConnectionState.waiting){
        //     return CircularProgressIndicator();
        //   }else if(snapshot.hasError){
        //     print(snapshot.error);
        //   }else{
        //     if(snapshot.hasData){
        //       print(snapshot.data);
        //     }
        //   }
        // },),
        );
  }
}
