class NslTreeHierarchy {
    int? status;
    String? message;
    Result? result;

    NslTreeHierarchy({
        this.status,
        this.message,
        this.result,
    });

    factory NslTreeHierarchy.fromJson(Map<String, dynamic> json) => NslTreeHierarchy(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
    };

    NslTreeHierarchy copyWith({
        int? status,
        String? message,
        Result? result,
    }) => 
        NslTreeHierarchy(
            status: status ?? this.status,
            message: message ?? this.message,
            result: result ?? this.result,
        );
}

class Result {
    List<Node>? nodes;

    Result({
        this.nodes,
    });

    factory Result.fromJson(Map<String, dynamic> json) => Result(
        nodes: json["nodes"] == null ? [] : List<Node>.from(json["nodes"]!.map((x) => Node.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "nodes": nodes == null ? [] : List<dynamic>.from(nodes!.map((x) => x.toJson())),
    };

    Result copyWith({
        List<Node>? nodes,
    }) => 
        Result(
            nodes: nodes ?? this.nodes,
        );
}

class Node {
    String? id;
    String? displayId;
    String? name;
    List<String>? parentIds;
    List<String>? children;
    NodeType? nodeType;
    Level? level;
    int? npValue;

    Node({
        this.id,
        this.displayId,
        this.name,
        this.parentIds,
        this.children,
        this.nodeType,
        this.level,
        this.npValue,
    });

    factory Node.fromJson(Map<String, dynamic> json) => Node(
        id: json["id"],
        displayId: json["displayId"],
        name: json["name"],
        parentIds: json["parentIds"] == null ? [] : List<String>.from(json["parentIds"]!.map((x) => x)),
        children: json["children"] == null ? [] : List<String>.from(json["children"]!.map((x) => x)),
        nodeType: json["nodeType"] == null ? null : NodeType.values.firstWhere((e) => e.name == json["nodeType"]),
        level: json["level"] == null ? null : Level.values.firstWhere((e) => e.name == json["level"]),
        npValue: json["npValue"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "displayId": displayId,
        "name": name,
        "parentIds": parentIds == null ? [] : List<dynamic>.from(parentIds!.map((x) => x)),
        "children": children == null ? [] : List<dynamic>.from(children!.map((x) => x)),
        "nodeType": nodeType?.name,
        "level": level?.name,
        "npValue": npValue,
    };

    Node copyWith({
        String? id,
        String? displayId,
        String? name,
        List<String>? parentIds,
        List<String>? children,
        NodeType? nodeType,
        Level? level,
        int? npValue,
    }) => 
        Node(
            id: id ?? this.id,
            displayId: displayId ?? this.displayId,
            name: name ?? this.name,
            parentIds: parentIds ?? this.parentIds,
            children: children ?? this.children,
            nodeType: nodeType ?? this.nodeType,
            level: level ?? this.level,
            npValue: npValue ?? this.npValue,
        );
}

enum Level {
    M1,
    M2,
    M3,
    M4
}

enum NodeType {
    MODULE,
    USER
}
