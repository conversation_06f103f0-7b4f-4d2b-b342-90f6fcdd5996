import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/services/model_api_service.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/services/file_upload_service.dart';
import 'package:nsl/services/file_upload_ocr_service.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/model_selection_dropdown.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:provider/provider.dart';

class ChatInputField extends StatefulWidget {
  final TextEditingController chatController;
  final VoidCallback sendMessage;
  final FocusNode? focusNode;
  final double? width;
  final double? height;
  final dynamic parentState;

  const ChatInputField({
    super.key,
    required this.chatController,
    required this.sendMessage,
    this.focusNode,
    this.width,
    this.height,
    this.parentState,
  });

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  late MultimediaService _multimediaService;
  bool _isRecording = false;
  bool _isLoading = false;

  // Model selection
  late List<ModelInfo> availableModels;
  late ModelInfo selectedModel;

  @override
  void initState() {
    super.initState();
    // Listen to text changes to update provider state
    widget.chatController.addListener(_onTextChanged);

    // Initialize multimedia service
    _multimediaService = MultimediaService();
    _initializeMultimediaService();

    // Initialize model selection
    _initializeModels();
  }

  @override
  void dispose() {
    widget.chatController.removeListener(_onTextChanged);
    _multimediaService.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    provider.hasTextInChatField = widget.chatController.text.trim().isNotEmpty;
  }

  Future<void> _initializeMultimediaService() async {
    try {
      await _multimediaService.initialize();

      // Set up callbacks
      _multimediaService.onStateChanged = () {
        if (mounted) {
          setState(() {
            _isRecording = _multimediaService.isRecording;
          });
        }
      };

      _multimediaService.onTextRecognized = (text) {
        if (mounted) {
          widget.chatController.text = text;
          final provider = Provider.of<WebHomeProvider>(context, listen: false);
          provider.hasTextInChatField = text.isNotEmpty;
        }
      };
    } catch (e) {
      Logger.error('Error initializing multimedia service: $e');
    }
  }

  void _initializeModels() {
    availableModels = [
      ModelInfo(
        id: 'GLM 1.0',
        name: 'GLM 1.0',
        description: 'Loading models...',
        apiKey: 'loading',
        isSelected: true,
      ),
    ];

    selectedModel = availableModels.firstWhere(
      (model) => model.isSelected,
      orElse: () => availableModels.first,
    );
  }

  Future<void> _toggleRecording() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isRecording) {
        // Stop recording
        await _multimediaService.stopSpeechRecognition();
        Logger.info('Mobile recording stopped');
      } else {
        // Start recording
        await _multimediaService.startSpeechRecognition(context);
        Logger.info('Mobile recording started');
      }
    } catch (e) {
      Logger.error('Error toggling recording: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Recording error: $e'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleFileUpload() async {
    final fileUploadService = FileUploadService();
    final fileUploadOcrService = FileUploadOcrService();

    try {
      // Pick the file
      final file = await fileUploadService.pickFile();

      if (file == null) {
        return;
      }

      // Process the file for OCR
      final uploadResult = await fileUploadService.uploadFile(file);

      if (!mounted) return;

      if (!uploadResult['success']) {
        // Handle upload error
        if (mounted) {
          fileUploadOcrService.showOverlay(
            context,
            'Failed to upload file: ${uploadResult['message']}',
            isError: true,
          );
        }
        return;
      }

      // Success - get extracted text
      final extractedText = uploadResult['extracted_text'] ?? '';

      // Update chat controller with extracted text
      if (extractedText.isNotEmpty) {
        widget.chatController.text = extractedText;
        final provider = Provider.of<WebHomeProvider>(context, listen: false);
        provider.hasTextInChatField = true;
      }

      // Show success message
      if (mounted) {
        fileUploadOcrService.showOverlay(
          context,
          'File uploaded: ${file.name}',
        );
      }

      Logger.info('Mobile file upload completed: ${file.name}');
    } catch (e) {
      Logger.error('Mobile file upload error: $e');

      if (mounted) {
        final fileUploadOcrService = FileUploadOcrService();
        fileUploadOcrService.showOverlay(
          context,
          'Error uploading file: $e',
          isError: true,
        );
      }
    }
  }

  void _handleScreenshot() {
    // Placeholder for screenshot functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Screenshot feature coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Build a full-width recording UI for web audio recorder
  Widget _buildFullWidthRecordingUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: _multimediaService.createWebAudioRecorderWidget(
        chatController: widget.chatController,
        onCancel: () {
          setState(() {
            _isRecording = false;
          });
        },
        onLoadingChanged: (loading) {
          if (widget.parentState != null) {
            widget.parentState.setState(() {
              widget.parentState.isAudioLoading = loading;
            });
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<WebHomeProvider>(context);

    // If recording is active and should use web audio recorder, show full-width recording UI
    if (_isRecording && _multimediaService.shouldUseWebAudioRecorder()) {
      return Container(
        width: widget.width,
        height: widget.height,
        margin: EdgeInsets.only(
          bottom: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.md),
          border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
        ),
        child: _buildFullWidthRecordingUI(context),
      );
    }

    return Column(
      children: [
        // Main chat input container (fixed size)
        Container(
          margin: EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.lg),
            border: Border.all(color: Color(0xffd0d0d0)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          constraints: BoxConstraints(minHeight: 100),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: widget.chatController,
                        focusNode: widget.focusNode,
                        onSubmitted: (_) => widget.sendMessage(),
                        decoration: InputDecoration(
                          hintText: 'Ask NSL',
                          hintStyle: TextStyle(
                            color: Colors.grey.shade300,
                          ),
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          contentPadding:
                              EdgeInsets.symmetric(vertical: AppSpacing.xs),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: AppSpacing.xs,
                    right: AppSpacing.xs,
                    bottom: AppSpacing.xs),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Add/Close button
                    Container(
                      height: 32,
                      width: 32,
                      decoration: BoxDecoration(
                        color: provider.isMobileChatExpanded
                            ? Color(0xff0058FF)
                            : Color(0xFFE4EDFF),
                        shape: BoxShape.circle,
                      ),
                      padding: EdgeInsets.zero,
                      child: IconButton(
                        icon: Icon(
                          provider.isMobileChatExpanded
                              ? Icons.close
                              : Icons.add,
                          color: provider.isMobileChatExpanded
                              ? Colors.white
                              : Colors.black87,
                        ),
                        onPressed: () {
                          provider.toggleMobileChatExpansion();
                        },
                        iconSize: 16,
                        padding: EdgeInsets.zero,
                      ),
                    ),

                    // Send/Mic button
                    Container(
                      decoration: BoxDecoration(
                        color: _isRecording
                            ? Color(0xff0058FF)
                            : Color(0xFFE4EDFF),
                        shape: BoxShape.circle,
                      ),
                      height: 32,
                      width: 32,
                      padding: EdgeInsets.zero,
                      child: IconButton(
                        icon: _isLoading
                            ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    _isRecording
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                              )
                            : provider.hasTextInChatField
                                ? Icon(Icons.send, color: Colors.black)
                                : _isRecording
                                    ? Icon(Icons.stop, color: Colors.white)
                                    : Icon(Icons.mic, color: Colors.black),
                        onPressed: _isLoading
                            ? null
                            : provider.hasTextInChatField
                                ? widget.sendMessage
                                : _toggleRecording,
                        iconSize: 16,
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Option cards below the chat input (when expanded)
        if (provider.isMobileChatExpanded)
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            margin: EdgeInsets.only(
               
                bottom: AppSpacing.xs),
            child: Row(
              children: [
                // Left side: Files and Screenshot cards with intrinsic width
                _buildOptionCard(
                  svgAssetPath: 'assets/images/chat/files_chat_mobile.svg',
                  label: 'Files',
                  onTap: _handleFileUpload,
                ),
                SizedBox(width: AppSpacing.xs),
                _buildOptionCard(
                   svgAssetPath: 'assets/images/chat/screenshot_mobile.svg',
                  label: 'Screenshot',
                  onTap: _handleScreenshot,
                ),
                Spacer(), // Push GLM 1.0 to the right
                // Right side: GLM 1.0 card with custom dropdown positioning
               ModelSelectionDropdown(
                    models: availableModels,
                    selectedModel: selectedModel,
                    dropdownAbove: true,
                    onModelSelected: (ModelInfo model) {
                      setState(() {
                        selectedModel = model;
                        // Update the selected state in the models list
                        availableModels = availableModels
                            .map((m) => ModelInfo(
                                  id: m.id,
                                  name: m.name,
                                  description: m.description,
                                  apiKey: m.apiKey,
                                  isPro: m.isPro,
                                  isSelected: m.id == model.id,
                                ))
                            .toList();
                      });
                      Logger.info('Selected model: ${model.name}');
                    },
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildOptionCard({
  required String svgAssetPath,
  required String label,
  required VoidCallback onTap,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        border: Border.all(color: Color(0xFFE4E7EA)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center, // Align center vertically
        children: [
          SvgPicture.asset(
            svgAssetPath,
            height: 14,
            width: 14,
            colorFilter: ColorFilter.mode(Colors.black, BlendMode.srcIn),
          ),
          SizedBox(width: AppSpacing.xs),
          Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              color: Colors.black,
              fontWeight: FontManager.medium,
              // fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ],
      ),
    ),
  );
}

}
