import 'package:flutter/material.dart';
import 'package:nsl/models/option_with_value.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/selectable_type_writer_text_widget.dart';
import 'package:nsl/utils/font_manager.dart';

// class TypingCheckboxList extends StatefulWidget {
//   final List<RichOptionLine> lines;
//   final Duration typingSpeed;
//   final Function(String, bool)? onChanged;
//   final VoidCallback? onTypingComplete;
//   final bool isTypingRequired;
//   final bool isEnabled;

//   const TypingCheckboxList({
//     super.key,
//     required this.lines,
//     required this.typingSpeed,
//     this.onChanged,
//     this.onTypingComplete,
//     this.isTypingRequired = true,
//     this.isEnabled = true,
//   });

//   @override
//   State<TypingCheckboxList> createState() => _TypingCheckboxListState();
// }

// class _TypingCheckboxListState extends State<TypingCheckboxList> {
//   final Map<String, bool> _typedDone = {};
//   final Map<String, bool> _checkboxState = {};

//   @override
//   void initState() {
//     super.initState();
//     for (final line in widget.lines) {
//       _typedDone[line.text] = false; // initially not typed
//       if (line.isCheckbox) {
//         _checkboxState[line.text] = false; // initially unchecked
//       }
//     }
//   }

//   void _handleToggle(String option, bool value) {
//     widget.onChanged?.call(option, value);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: widget.lines.map((line) {
//         final isCheckboxLine = line.isCheckbox;
//         return Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             if (isCheckboxLine && widget.isEnabled)
//               Padding(
//                 padding: const EdgeInsets.only(right: 8.0),
//                 child: Checkbox(
//                   value: _checkboxState[line.text],
//                   onChanged: (value) {
//                     if (value != null) {
//                       setState(() => _checkboxState[line.text] = value);
//                       _handleToggle(
//                           line.text, _checkboxState[line.text] ?? false);
//                     }
//                   },
//                   visualDensity: VisualDensity.compact,
//                   materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                 ),
//               )
//             else if (isCheckboxLine)
//               const Text("□ "),
//             Expanded(
//               child: widget.isTypingRequired
//                   ? SelectableTypewriterText(
//                       textSpans: [
//                         TextSpan(
//                           text: line.text,
//                           style: FontManager.getCustomStyle(
//                             fontFamily: FontManager.fontFamilyTiemposText,
//                             fontSize: FontManager.s16,
//                             color: Colors.black,
//                           ),
//                         ),
//                       ],
//                       speed: widget.typingSpeed,
//                       onComplete: () {
//                         setState(() => _typedDone[line.text] = true);

//                         if (_typedDone.values.every((t) => t)) {
//                           widget.onTypingComplete?.call();
//                           for (final key in _typedDone.keys) {
//                             _typedDone[key] = false;
//                           }
//                         }
//                       },
//                     )
//                   : Text(
//                       line.text,
//                       style: FontManager.getCustomStyle(
//                         fontFamily: FontManager.fontFamilyTiemposText,
//                         fontSize: FontManager.s16,
//                         color: Colors.black,
//                       ),
//                     ),
//             ),
//           ],
//         );
//       }).toList(),
//     );
//   }
// }

class TypingCheckboxList extends StatefulWidget {
  final List<RichOptionLine> lines;
  final Duration typingSpeed;
  final Function(String, bool, bool)? onChanged;
  final VoidCallback? onTypingComplete;
  final bool isTypingRequired;
  final bool isEnabled;

  const TypingCheckboxList({
    super.key,
    required this.lines,
    required this.typingSpeed,
    this.onChanged,
    this.onTypingComplete,
    this.isTypingRequired = true,
    this.isEnabled = true,
  });

  @override
  State<TypingCheckboxList> createState() => _TypingCheckboxListState();
}

class _TypingCheckboxListState extends State<TypingCheckboxList> {
  final Map<String, bool> _typedDone = {};
  final Map<String, bool> _checkboxState = {};
  String? _selectedRadio;

  @override
  void initState() {
    super.initState();
    for (final line in widget.lines) {
      _typedDone[line.text] = false;
      if (line.isCheckbox) {
        _checkboxState[line.text] = false;
      }
    }
  }

  void _handleToggle(String option, bool value, bool multiSelect) {
    widget.onChanged?.call(option, value, multiSelect);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.lines.map((line) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (line.isCheckbox && widget.isEnabled)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0, top: 2.0),
                  child: Checkbox(
                    value: _checkboxState[line.text],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _checkboxState[line.text] = value);
                        _handleToggle(line.text,
                            _checkboxState[line.text] ?? false, true);
                      }
                    },
                    visualDensity: VisualDensity.compact,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                )
              else if (line.isCheckbox)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0, top: 4.0),
                  child: Text("□ "),
                )
              else if (line.isRadio && widget.isEnabled)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      if (_selectedRadio == line.text) {
                        _selectedRadio = null;
                      } else {
                        _selectedRadio = line.text;
                      }
                    });
                    _handleToggle(
                        line.text, _selectedRadio == line.text, false);
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Icon(
                      _selectedRadio == line.text
                          ? Icons.radio_button_checked
                          : Icons.radio_button_unchecked,
                      size: 20,
                      color: Colors.black,
                    ),
                  ),
                )
              // Radio<String>(
              //   value: line.text,
              //   groupValue: _selectedRadio,
              //   onChanged: (value) {
              //     setState(() {
              //       if (_selectedRadio == value) {
              //         _selectedRadio = null; // deselect
              //       } else {
              //         _selectedRadio = value;
              //       }
              //     });
              //     _handleToggle(line.text, _selectedRadio == line.text);
              //   },
              //   visualDensity: VisualDensity.compact,
              //   materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              // )
              else if (line.isRadio)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0, top: 4.0),
                  child: Text("○ "),
                ),
              Expanded(
                child: widget.isTypingRequired
                    ? SelectableTypewriterText(
                        textSpans: [
                          TextSpan(
                            text: line.text,
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: FontManager.s16,
                              color: Colors.black,
                            ),
                          ),
                        ],
                        speed: widget.typingSpeed,
                        onComplete: () {
                          setState(() => _typedDone[line.text] = true);

                          if (_typedDone.values.every((t) => t)) {
                            widget.onTypingComplete?.call();
                            for (final key in _typedDone.keys) {
                              _typedDone[key] = false;
                            }
                          }
                        },
                      )
                    : Text(
                        line.text,
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: FontManager.s16,
                          color: Colors.black,
                        ),
                      ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
