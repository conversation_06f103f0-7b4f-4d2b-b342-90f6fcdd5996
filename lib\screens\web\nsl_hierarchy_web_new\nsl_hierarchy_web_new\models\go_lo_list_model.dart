class GoLoListModel {
    int? status;
    String? message;
    Result? result;

    GoLoListModel({
        this.status,
        this.message,
        this.result,
    });

    factory GoLoListModel.fromJson(Map<String, dynamic> json) => GoLoListModel(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
    };

    GoLoListModel copyWith({
        int? status,
        String? message,
        Result? result,
    }) => 
        GoLoListModel(
            status: status ?? this.status,
            message: message ?? this.message,
            result: result ?? this.result,
        );
}

class Result {
    List<Datum>? data;
    int? pageNumber;
    int? pageSize;
    int? totalHits;
    int? totalPages;

    Result({
        this.data,
        this.pageNumber,
        this.pageSize,
        this.totalHits,
        this.totalPages,
    });

    factory Result.fromJson(Map<String, dynamic> json) => Result(
        data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        totalHits: json["totalHits"],
        totalPages: json["totalPages"],
    );

    Map<String, dynamic> toJson() => {
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "totalHits": totalHits,
        "totalPages": totalPages,
    };

    Result copyWith({
        List<Datum>? data,
        int? pageNumber,
        int? pageSize,
        int? totalHits,
        int? totalPages,
    }) => 
        Result(
            data: data ?? this.data,
            pageNumber: pageNumber ?? this.pageNumber,
            pageSize: pageSize ?? this.pageSize,
            totalHits: totalHits ?? this.totalHits,
            totalPages: totalPages ?? this.totalPages,
        );
}

class Datum {
    String? id;
    String? name;
    List<Lo>? los;

    Datum({
        this.id,
        this.name,
        this.los,
    });

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        name: json["name"],
        los: json["los"] == null ? [] : List<Lo>.from(json["los"]!.map((x) => Lo.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "los": los == null ? [] : List<dynamic>.from(los!.map((x) => x.toJson())),
    };

    Datum copyWith({
        String? id,
        String? name,
        List<Lo>? los,
    }) => 
        Datum(
            id: id ?? this.id,
            name: name ?? this.name,
            los: los ?? this.los,
        );
}

class Lo {
    String? id;
    String? name;
    List<Entity>? entities;

    Lo({
        this.id,
        this.name,
        this.entities,
    });

    factory Lo.fromJson(Map<String, dynamic> json) => Lo(
        id: json["id"],
        name: json["name"],
        entities: json["entities"] == null ? [] : List<Entity>.from(json["entities"]!.map((x) => Entity.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "entities": entities == null ? [] : List<dynamic>.from(entities!.map((x) => x.toJson())),
    };

    Lo copyWith({
        String? id,
        String? name,
        List<Entity>? entities,
    }) => 
        Lo(
            id: id ?? this.id,
            name: name ?? this.name,
            entities: entities ?? this.entities,
        );
}

class Entity {
    String? id;
    String? name;
    List<Attribute>? attributes;

    Entity({
        this.id,
        this.name,
        this.attributes,
    });

    factory Entity.fromJson(Map<String, dynamic> json) => Entity(
        id: json["id"],
        name: json["name"],
        attributes: json["attributes"] == null ? [] : List<Attribute>.from(json["attributes"]!.map((x) => Attribute.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "attributes": attributes == null ? [] : List<dynamic>.from(attributes!.map((x) => x.toJson())),
    };

    Entity copyWith({
        String? id,
        String? name,
        List<Attribute>? attributes,
    }) => 
        Entity(
            id: id ?? this.id,
            name: name ?? this.name,
            attributes: attributes ?? this.attributes,
        );
}

class Attribute {
    String? id;
    String? name;

    Attribute({
        this.id,
        this.name,
    });

    factory Attribute.fromJson(Map<String, dynamic> json) => Attribute(
        id: json["id"],
        name: json["name"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
    };

    Attribute copyWith({
        String? id,
        String? name,
    }) => 
        Attribute(
            id: id ?? this.id,
            name: name ?? this.name,
        );
}
