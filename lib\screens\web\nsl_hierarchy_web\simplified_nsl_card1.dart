import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';


class SimplifiedNSLCard1 extends StatelessWidget {
  final NSLNode node;
  final bool isSelected;
  final bool isHighlightedInPath;
  final VoidCallback onTitleTap; // For title container and green dot - opens side panel
  final VoidCallback onInfoTap;  // For info container - changes border and expands children
  final bool isInfoTapped; // To track if info container was tapped for blue border
  
  const SimplifiedNSLCard1({
    super.key,
    required this.node,
    required this.isSelected,
    required this.isHighlightedInPath,
    required this.onTitleTap,
    required this.onInfoTap,
    this.isInfoTapped = false,
  });

  @override
  Widget build(BuildContext context) {
    bool isRootNode = node.level == 'M4';

    return Tooltip(
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: const BorderRadius.all(Radius.circular(3)),
      ),
      margin: const EdgeInsets.only(left: 55),
      message: node.title,
      child: MouseRegion(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //  if (isRootNode)
            // Root node: Stack for overlapping effect
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.topCenter,
              children: [
                // Title Container with margin for overlap
                Container(
                  margin: EdgeInsets.only(top: 12),
                  child: InkWell(
                    onTap: onTitleTap,
                    child: _buildTitleContainer(),
                  ),
                ),
                // Blue circle with green dot positioned to overlap
                Positioned(
                  top: 0,
                  child: InkWell(
                    onTap: onTitleTap,
                    child: _buildNodeIndicator(),
                  ),
                ),
              ],
            ),
            //  else
            // Non-root nodes: Simple layout with green dot above
            // Column(
            //   children: [
            //     InkWell(
            //       onTap: onTitleTap,
            //       child: Center(
            //         child: _buildNodeIndicator(),
            //       ),
            //     ),
            //     InkWell(
            //       onTap: onTitleTap,
            //       child: _buildTitleContainer(),
            //     ),
            //   ],
            // ),

            // Info Container for all nodes
            InkWell(
              onTap: onInfoTap,
              child: _buildInfoContainer(),
            ),
          ],
        ),
      ),
    );
    //return ;
  }

  Widget _buildTitleContainer() {
    return IntrinsicWidth(
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ?  Color(0xFF0058FF) : node.levelColor, // Blue background when selected
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            color:   isInfoTapped
                ? Color(0xFF0058FF):// Blue border when selected
                 Color(0xFFD1D1D1),    // Default gray border
            width:1,
            // isSelected ? 1 : 1,  // Thicker border when selected
          ),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.size10, vertical:AppSpacing.size6),
          child: Text(
            
            node.title.length > 10 ? '${node.title.substring(0, 7)}...' : node.title,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.regular, // Bold when selected
              fontFamily: FontManager.fontFamilyTiemposText,
              color:  isSelected ?  Colors.white: Colors.black, // Blue text when selected
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoContainer() {
    return IntrinsicWidth(
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 123,
        //  maxWidth: 250,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            color: 
          
                //  isHighlightedInPath
                //     ? Colors.orange.shade300:
                    Color(0xFFD1D1D1),),
          
        ),
        child: Container(
           padding: const EdgeInsets.symmetric(horizontal: AppSpacing.size10, vertical:AppSpacing.size6),
      
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //  mainAxisSize: MainAxisSize.min,
            children: [
              // ID (show displayId)
              Text(
                'ID: ${node.originalData.employeeId ?? node.id}',
               style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),),
                      
            const SizedBox(width: AppSpacing.size20),
              // NP (total_bets)
              Text(
                'NP: ${node.totalBets}',
               style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
 Widget _buildNodeIndicator() {
  // Check if this is the root node (you can adjust this condition based on your data structure)
  bool isRootNode = node.level == 'M4';
  
 // if (isRootNode) {
    // Root node: Green dot with blue circle around it
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white, // White background to "cut" into the title
        border: Border.all(
          color: Color(0xFF0058FF), // Blue circle
          width: 0.5,
        ),
      ),
      child: Center(
        child: Container(
          width: 10,
          height: 10,
          decoration: const BoxDecoration(
            color: Colors.black, // Green dot
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
 // }
  //  else {
  //   // Other nodes: Just green dot
  //   return Container(
  //     width: 10,
  //     height: 10,
  //     decoration: const BoxDecoration(
  //       color: Color(0xFF4CAF50), // Green dot
  //       shape: BoxShape.circle,
  //     ),
  //   );
  // }
}

}
