import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class NewExistingProjectModel extends StatefulWidget {
  final VoidCallback? onNewProjectSelected;
  final VoidCallback? onExistingProjectSelected;

  const NewExistingProjectModel({
    super.key,
    this.onNewProjectSelected,
    this.onExistingProjectSelected,
  });

  @override
  State<NewExistingProjectModel> createState() =>
      _NewExistingProjectModelState();
}

class _NewExistingProjectModelState extends State<NewExistingProjectModel> {
  // Modal state
  bool _showModal = false;
  bool _isNewProject = true; // true for new project, false for existing project

  // New project form controllers
  final TextEditingController _projectNameController = TextEditingController();
  final TextEditingController _documentNameController = TextEditingController();
  String? _selectedIndustry;
  bool _isDropdownFocused = false;

  // Existing project form controllers
  String? _selectedProject;
  bool _isProjectDropdownFocused = false;

  final List<String> _industries = [
    'E-Commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology',
    'Manufacturing',
    'Retail',
    'Real Estate',
  ];

  final List<String> _existingProjects = [
    'E Commerce Solution',
    'Fashion & Apparel Platform',
    'Financial Advisory System',
    'Home Rentals Platform',
    'Online Grocery Store',
    'Courier & Logistics App',
    'Automotive Marketplace',
    'Fitness & Wellness App',
  ];

  @override
  void dispose() {
    _projectNameController.dispose();
    _documentNameController.dispose();
    super.dispose();
  }

  void _showProjectModal(bool isNewProject) {
    setState(() {
      _isNewProject = isNewProject;
    });

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                width: 482,
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: _isNewProject
                    ? _buildNewProjectModal(setDialogState)
                    : _buildExistingProjectModal(setDialogState),
              ),
            );
          },
        );
      },
    );
  }

  void _closeModal() {
    Navigator.of(context).pop();
    // Reset form data
    _projectNameController.clear();
    _documentNameController.clear();
    _selectedIndustry = null;
    _selectedProject = null;
  }

  void _handleStart() {
    if (_isNewProject) {
      // Handle new project creation
      if (_projectNameController.text.isNotEmpty && _selectedIndustry != null) {
        _closeModal();
        widget.onNewProjectSelected?.call();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'New project "${_projectNameController.text}" created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show validation error
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please fill in all required fields'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      // Handle existing project selection
      if (_selectedProject != null) {
        _closeModal();
        widget.onExistingProjectSelected?.call();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Project "$_selectedProject" selected successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show validation error
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select a project'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      // decoration: BoxDecoration(
      //   // color: Colors.white,
      //   borderRadius: BorderRadius.circular(8),
      //   // boxShadow: [
      //   //   BoxShadow(
      //   //     color: Colors.black.withOpacity(0.1),
      //   //     blurRadius: 10,
      //   //     offset: const Offset(0, 4),
      //   //   ),
      //   // ],
      // ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title text
          Text(
            'Looks like this is a new project. Let\'s start by creating a new project? Or want to save in existing Project?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 24),

          // Buttons row
          Row(
            children: [
              // New button
              _ProjectButton(
                text: 'New',
                isPrimary: true,
                onPressed: () => _showProjectModal(true),
              ),
              const SizedBox(width: 16),

              // Existing button
              _ProjectButton(
                text: 'Existing',
                isPrimary: false,
                onPressed: () => _showProjectModal(false),
              ),
              // const SizedBox(width: 16),
              // TextButton(
              //   onPressed: () {
              //     Navigator.push(
              //       context,
              //       MaterialPageRoute(
              //           builder: (context) => GlobalLibraryAccordionFlow()),
              //     );
              //   },
              //   child: Text('Global Library Acc'),
              // )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNewProjectModal(StateSetter setDialogState) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Container(
            alignment: Alignment.center,
            child: Text(
              'Save In A Project',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.headlineLarge(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: const Color(0xff0058FF),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Name field
          Text(
            'Name',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 40,
            child: TextField(
              controller: _projectNameController,
              decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide:
                      const BorderSide(color: Color(0xff0058FF), width: 1),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                hintText: 'Enter project name',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Industry field
          Text(
            'Industry',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Focus(
            onFocusChange: (hasFocus) {
              setState(() {
                _isDropdownFocused = hasFocus;
              });
            },
            child: Container(
              width: double.infinity,
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDropdownFocused
                      ? const Color(0xff0058FF)
                      : Colors.grey.shade300,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedIndustry,
                  hint: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Text(
                      'Select industry',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ),
                  icon: Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  isExpanded: true,
                  items: _industries.map((String industry) {
                    return DropdownMenuItem<String>(
                      value: industry,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          industry,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setDialogState(() {
                      _selectedIndustry = newValue;
                    });
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          // Start button
          Center(
            child: SizedBox(
              width: 200,
              height: 44,
              child: ElevatedButton(
                onPressed: _handleStart,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff0058FF),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Text(
                  'Start',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleLarge(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExistingProjectModal(StateSetter setDialogState) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Container(
          alignment: Alignment.center,
          child: Text(
            'Select Project',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.headlineLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xff0058FF),
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Projects field
        Text(
          'Projects',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _isProjectDropdownFocused = hasFocus;
            });
          },
          child: Container(
            width: double.infinity,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(
                color: _isProjectDropdownFocused
                    ? const Color(0xff0058FF)
                    : Colors.grey.shade300,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedProject,
                hint: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    'Select project',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ),
                icon: Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600,
                  ),
                ),
                isExpanded: true,
                items: _existingProjects.map((String project) {
                  return DropdownMenuItem<String>(
                    value: project,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(
                        project,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setDialogState(() {
                    _selectedProject = newValue;
                  });
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 32),

        // Start button
        Center(
          child: SizedBox(
            width: 200,
            height: 44,
            child: ElevatedButton(
              onPressed: _handleStart,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Start',
                style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleLarge(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    height: 1),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _ProjectButton extends StatefulWidget {
  final String text;
  final bool isPrimary;
  final VoidCallback onPressed;

  const _ProjectButton({
    required this.text,
    required this.isPrimary,
    required this.onPressed,
  });

  @override
  State<_ProjectButton> createState() => _ProjectButtonState();
}

class _ProjectButtonState extends State<_ProjectButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onPressed,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: widget.isPrimary
                ? const Color(0xff0058FF)
                : (isHovered ? Colors.grey.shade100 : Colors.white),
            border: widget.isPrimary
                ? null
                : Border.all(
                    color:
                        isHovered ? Colors.grey.shade400 : Colors.grey.shade300,
                    width: 1,
                  ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            widget.text,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: widget.isPrimary
                  ? Colors.white
                  : (isHovered ? Colors.black : Colors.grey.shade700),
            ),
          ),
        ),
      ),
    );
  }
}
