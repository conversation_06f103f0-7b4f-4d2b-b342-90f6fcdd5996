import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:provider/provider.dart';

import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/providers/library_counts_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class Project {
  final String projectName;
  final String createdOn;
  final String lastModified;
  final String lastModifiedBy;
  final String status;
  final DateTime lastUpdated;

  Project({
    required this.projectName,
    required this.createdOn,
    required this.lastModified,
    required this.lastModifiedBy,
    required this.status,
    required this.lastUpdated,
  });

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      projectName: json['projectName'] as String,
      createdOn: json['createdOn'] as String,
      lastModified: json['lastModified'] as String,
      lastModifiedBy: json['lastModifiedBy'] as String,
      status: json['status'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

class MyProjectsScreenMobile extends StatefulWidget {
  const MyProjectsScreenMobile({
    super.key,
    this.showNavigationBar = true,
    this.searchQuery,
  });

  final bool showNavigationBar;
  final String? searchQuery;

  @override
  State<MyProjectsScreenMobile> createState() => MyProjectsScreenMobileState();
}

class MyProjectsScreenMobileState extends State<MyProjectsScreenMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _projectsPerViewNormal = 1.25; // Show 1 full card + 25% of next
  static const double _projectsPerViewCompact = 1.5;
  static const double _projectAspectRatio = 0.6; // width / height
  static const double _verticalSpacing = 12.0;
  static const double _projectSpacing = 16.0;
  static const double _horizontalPadding = 16.0;
  static const int _recentProjectsLimit = 10;
  static const double _fixedCardHeight = 180.0; // Fixed height for cards

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<Project> projects = [];
  List<Project> recentProjects = [];
  List<Project> allProjects = [];
  List<Project> filteredRecentProjects = [];
  List<Project> filteredAllProjects = [];
  bool isLoading = true;

  // Controllers
  late CarouselController _recentProjectsController;
  late CarouselController _allProjectsController;
  late AnimationController _loadingAnimationController;

  // Only needed when showNavigationBar is true
  FocusNode? _searchFocusNode;
  TextEditingController? _searchController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  // Create project modal state
  bool _showCreateProjectModal = false;
  final TextEditingController _projectNameController = TextEditingController();
  String? _selectedIndustry;
  bool _isDropdownFocused = false;

  final List<String> _industries = [
    'E-Commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology',
  ];

  /// Get projects per view based on keyboard visibility
  double _getProjectsPerView() {
    return _isKeyboardVisible ? _projectsPerViewCompact : _projectsPerViewNormal;
  }

  // JSON string containing project data
  static const String projectsJsonString = '''
{
  "projects": [
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%",
      "lastUpdated": "2024-12-15T10:30:00Z"
    },
    {
      "projectName": "Fashion & Apparel Platform",
      "createdOn": "08/05/2025",
      "lastModified": "09/06/2025",
      "lastModifiedBy": "John Smith",
      "status": "Discovery 80% / Development 20%",
      "lastUpdated": "2024-12-18T14:22:00Z"
    },
    {
      "projectName": "Financial Advisory System",
      "createdOn": "07/05/2025",
      "lastModified": "08/06/2025",
      "lastModifiedBy": "Sarah Johnson",
      "status": "Discovery 100% / Development 45%",
      "lastUpdated": "2024-12-10T09:15:00Z"
    },
    {
      "projectName": "Home Rentals Platform",
      "createdOn": "06/05/2025",
      "lastModified": "07/06/2025",
      "lastModifiedBy": "Mike Wilson",
      "status": "Discovery 30% / Development 0%",
      "lastUpdated": "2024-12-20T16:45:00Z"
    },
    {
      "projectName": "Online Grocery Store",
      "createdOn": "05/05/2025",
      "lastModified": "06/06/2025",
      "lastModifiedBy": "Emily Davis",
      "status": "Discovery 90% / Development 60%",
      "lastUpdated": "2024-12-08T11:30:00Z"
    },
    {
      "projectName": "Courier & Logistics App",
      "createdOn": "04/05/2025",
      "lastModified": "05/06/2025",
      "lastModifiedBy": "David Brown",
      "status": "Discovery 70% / Development 30%",
      "lastUpdated": "2024-12-19T13:20:00Z"
    },
    {
      "projectName": "Automotive Marketplace",
      "createdOn": "03/05/2025",
      "lastModified": "04/06/2025",
      "lastModifiedBy": "Lisa Anderson",
      "status": "Discovery 60% / Development 15%",
      "lastUpdated": "2024-12-12T08:45:00Z"
    },
    {
      "projectName": "Fitness & Wellness App",
      "createdOn": "02/05/2025",
      "lastModified": "03/06/2025",
      "lastModifiedBy": "Robert Taylor",
      "status": "Discovery 85% / Development 40%",
      "lastUpdated": "2024-12-21T15:10:00Z"
    },
    {
      "projectName": "Real Estate Portal",
      "createdOn": "01/05/2025",
      "lastModified": "02/06/2025",
      "lastModifiedBy": "Jennifer White",
      "status": "Discovery 95% / Development 70%",
      "lastUpdated": "2024-12-07T12:00:00Z"
    },
    {
      "projectName": "Restaurant & Cafe",
      "createdOn": "31/04/2025",
      "lastModified": "01/06/2025",
      "lastModifiedBy": "Alex Chen",
      "status": "Discovery 40% / Development 10%",
      "lastUpdated": "2024-12-16T17:30:00Z"
    },
    {
      "projectName": "Healthcare Platform",
      "createdOn": "30/04/2025",
      "lastModified": "31/05/2025",
      "lastModifiedBy": "Maria Garcia",
      "status": "Discovery 75% / Development 35%",
      "lastUpdated": "2024-12-22T09:25:00Z"
    },
    {
      "projectName": "Education Portal",
      "createdOn": "29/04/2025",
      "lastModified": "30/05/2025",
      "lastModifiedBy": "James Wilson",
      "status": "Discovery 65% / Development 25%",
      "lastUpdated": "2024-12-05T14:40:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadProjects();
  }

  @override
  void didUpdateWidget(MyProjectsScreenMobile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _searchQuery = widget.searchQuery ?? '';
      _filterProjects();
    }
  }

  void _initializeControllers() {
    _recentProjectsController = CarouselController();
    _allProjectsController = CarouselController();
    _searchQuery = widget.searchQuery ?? '';
    if (widget.showNavigationBar) {
      _searchFocusNode = FocusNode();
      _searchController = TextEditingController();
      _searchFocusNode!.addListener(_onSearchFocusChange);
      _searchController!.addListener(_onSearchChanged);
    }
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {});
  }

  void _onSearchChanged() {
    final query = _searchController?.text.toLowerCase().trim() ?? '';
    setState(() {
      _searchQuery = query;
      _filterProjects();
    });
  }

  void _filterProjects() {
    if (_searchQuery.isEmpty) {
      filteredRecentProjects = recentProjects;
      filteredAllProjects = allProjects;
    } else {
      filteredRecentProjects = recentProjects.where((project) {
        return project.projectName.toLowerCase().contains(_searchQuery) ||
            project.lastModifiedBy.toLowerCase().contains(_searchQuery) ||
            project.status.toLowerCase().contains(_searchQuery);
      }).toList();

      filteredAllProjects = allProjects.where((project) {
        return project.projectName.toLowerCase().contains(_searchQuery) ||
            project.lastModifiedBy.toLowerCase().contains(_searchQuery) ||
            project.status.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _loadProjects() {
    try {
      final data = json.decode(projectsJsonString);
      final loadedProjects = (data['projects'] as List<dynamic>)
          .map((projectJson) =>
              Project.fromJson(projectJson as Map<String, dynamic>))
          .toList();

      // Keep original order for allProjects (as received from API/JSON)
      final originalOrderProjects = List<Project>.from(loadedProjects);

      // Sort projects by lastUpdated date (most recent first) for recentProjects
      loadedProjects.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        projects = originalOrderProjects; // Original order
        recentProjects =
            loadedProjects.take(_recentProjectsLimit).toList(); // Recent sorted
        allProjects = originalOrderProjects; // Original order (as from API/JSON)
        filteredRecentProjects = recentProjects; // Initialize filtered lists
        filteredAllProjects = allProjects;
        isLoading = false;
      });

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        projects = <Project>[];
        recentProjects = <Project>[];
        allProjects = <Project>[];
        isLoading = false;
      });
      debugPrint('Error loading projects: $e');
    }
  }

  @override
  void dispose() {
    _recentProjectsController.dispose();
    _allProjectsController.dispose();
    if (widget.showNavigationBar) {
      _searchController?.removeListener(_onSearchChanged);
      _searchController?.dispose();
      _searchFocusNode?.removeListener(_onSearchFocusChange);
      _searchFocusNode?.dispose();
    }
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildProjectsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildProjectsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: null,
      appBar: null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProjectsContent(),
        ],
      ),
      floatingActionButton: null,
    );
  }

  Widget _buildProjectsContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeading("Recent Projects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentProjectsCarousel(),
                ),
                const SizedBox(height: 24),
                _buildSectionHeading("All Projects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllProjectsCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeading(String title) {
    return Container(
      padding: EdgeInsets.only(right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: _sectionHeadingStyle),
          if (title == "Recent Projects")
            _MobileCreateButton(
              onPressed: () {
                _showCreateProjectBottomSheet();
              },
            ),
        ],
      ),
    );
  }

  Widget _buildRecentProjectsCarousel() {
    final projectsToShow =
        _searchQuery.isEmpty ? recentProjects : filteredRecentProjects;
    return _buildCarousel(
      projects: projectsToShow,
      controller: _recentProjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No recent projects found'
          : 'No recent projects match your search',
    );
  }

  Widget _buildAllProjectsCarousel() {
    final projectsToShow =
        _searchQuery.isEmpty ? allProjects : filteredAllProjects;
    return _buildCarousel(
      projects: projectsToShow,
      controller: _allProjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No projects found'
          : 'No projects match your search',
    );
  }

  Widget _buildCarousel({
    required List<Project> projects,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (projects.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: projects.asMap().entries.map((entry) {
        return _buildProjectItem(entry.value, entry.key);
      }).toList(),
    );
  }

  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final projectsPerView = _getProjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / projectsPerView;
  }

  Widget _buildProjectItem(Project project, int projectIndex) {
    return GestureDetector(
      onTap: () => _navigateToProjectDetails(projectIndex, project.projectName),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildProjectContent(project),
          ),
        ),
      ),
    );
  }

  Widget _buildProjectContent(Project project) {
    final projectDimensions = _calculateProjectDimensions(context);

    return Container(
      width: projectDimensions['width']!,
      height: _fixedCardHeight, // Fixed height to prevent resizing when keyboard appears
      margin: EdgeInsets.only(right: _projectSpacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Project name and edit button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    project.projectName,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  onPressed: () => _editProject(project),
                  icon: const Icon(
                    Icons.edit_outlined,
                    size: 16,
                    color: Colors.grey,
                  ),
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Project details
            _buildDetailRow('Created', project.createdOn),
            const SizedBox(height: 6),
            _buildDetailRow('Modified', project.lastModified),
            const SizedBox(height: 6),
            _buildDetailRow('By', project.lastModifiedBy),
            const SizedBox(height: 12),
            // Status
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Color(0xff0058FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                project.status,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Color(0xff0058FF),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 70,
          child: Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Map<String, double> _calculateProjectDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final projectsPerView = _getProjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / projectsPerView;

    double projectWidth = itemExtent - _projectSpacing;

    // Size constraints for different modes
    if (_isKeyboardVisible) {
      projectWidth = projectWidth.clamp(250.0, 320.0);
    } else {
      projectWidth = projectWidth.clamp(280.0, 350.0);
    }

    return {
      'width': projectWidth,
      'height': _fixedCardHeight,
      'spacing': _projectSpacing,
    };
  }

  double _calculateCarouselHeight() {
    return _fixedCardHeight + _verticalSpacing; // Fixed height matching the card height
  }

  /// Navigates to project details screen
  void _navigateToProjectDetails(int projectIndex, String projectName) {
    // Handle project navigation
    debugPrint('Tapped on project: $projectName');
  }

  /// Edit project action
  void _editProject(Project project) {
    // Handle edit action
    debugPrint('Edit pressed for project: ${project.projectName}');
  }

  void _showCreateProjectBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Container(
                      alignment: Alignment.center,
                      child: Text(
                        'Create A Project',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.headlineMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Color(0xff0058FF),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Name field
                    Text(
                      'Name',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 44,
                      child: TextField(
                        controller: _projectNameController,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: Color(0xff0058FF), width: 1),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 12),
                          hintText: 'Enter project name',
                          hintStyle: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: FontManager.s14,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Industry field
                    Text(
                      'Industry',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      height: 44,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isDropdownFocused
                              ? Color(0xff0058FF)
                              : Colors.grey.shade300,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedIndustry,
                          hint: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Text(
                              'Select industry',
                              style: FontManager.getCustomStyle(
                                fontSize: ResponsiveFontSizes.bodyMedium(context),
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ),
                          icon: Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          isExpanded: true,
                          items: _industries.map((String industry) {
                            return DropdownMenuItem<String>(
                              value: industry,
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                child: Text(
                                  industry,
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              _selectedIndustry = newValue;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Start button
                    SizedBox(
                      width: double.infinity,
                      height: 44,
                      child: ElevatedButton(
                        onPressed: () {
                          // Handle project creation
                          if (_projectNameController.text.isNotEmpty &&
                              _selectedIndustry != null) {
                            // Create new project object
                            final newProject = Project(
                              projectName: _projectNameController.text,
                              createdOn: DateTime.now()
                                  .toString()
                                  .substring(0, 10)
                                  .replaceAll('-', '/'),
                              lastModified: DateTime.now()
                                  .toString()
                                  .substring(0, 10)
                                  .replaceAll('-', '/'),
                              lastModifiedBy: 'Current User',
                              status: 'Discovery 0% / Development 0%',
                              lastUpdated: DateTime.now(),
                            );

                            // Store project name for success message
                            final projectName = _projectNameController.text;

                            // Add new project to the beginning of the list
                            setState(() {
                              projects.insert(0, newProject);
                              recentProjects.insert(0, newProject);
                              allProjects.insert(0, newProject);
                              filteredRecentProjects.insert(0, newProject);
                              filteredAllProjects.insert(0, newProject);

                              // Reset form
                              _projectNameController.clear();
                              _selectedIndustry = null;
                            });

                            // Close bottom sheet
                            Navigator.of(context).pop();

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Project "$projectName" created successfully!'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xff0058FF),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Start',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ),
                    ),
                    // Add bottom padding for safe area
                    SizedBox(height: MediaQuery.of(context).padding.bottom),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Mobile-optimized Create Button
class _MobileCreateButton extends StatelessWidget {
  final VoidCallback onPressed;

  const _MobileCreateButton({required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Color(0xff0058FF),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0xff0058FF).withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 4),
                Text(
                  'Create',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
