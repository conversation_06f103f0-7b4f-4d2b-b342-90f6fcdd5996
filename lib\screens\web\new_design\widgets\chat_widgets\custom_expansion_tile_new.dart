// Custom ExpansionTile that separates title tap from expansion toggle
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/constants.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';

// Shared utility class for bottom sheet functionality
class EntityMenuItems {
  static const List<Map<String, String>> _menuItems = [
    {
      'icon': 'assets/images/entity/nested.svg',
      'title': 'Nested',
      'value': 'nested'
    },
    {
      'icon': 'assets/images/entity/shared.svg',
      'title': 'Shared',
      'value': 'shared'
    },
    {
      'icon': 'assets/images/entity/junction.svg',
      'title': 'Junction',
      'value': 'junction'
    },
    {
      'icon': 'assets/images/entity/agent.svg',
      'title': 'Agents',
      'value': 'agents'
    },
    {
      'icon': 'assets/images/entity/workflow.svg',
      'title': 'Workflows',
      'value': 'workflows'
    },
  ];

  static void show(BuildContext context, {Function(String)? onItemSelected}) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Menu items
              ..._menuItems.map((item) => _buildBottomSheetItem(
                    context: context,
                    icon: item['icon']!,
                    title: item['title']!,
                    onTap: () {
                      Navigator.pop(context);
                      onItemSelected?.call(item['value']!);
                    },
                  )),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildBottomSheetItem({
    required BuildContext context,
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            SvgPicture.asset(
              icon,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade700,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s16,
                color: Colors.black,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Utility class for responsive behavior
class ScreenResponsiveHelper {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }
}

class CustomExpansionTileNew extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final bool showArrow;
  final bool showThreeDots;
  final String? panelId; // Unique identifier for accordion behavior
  final AccordionController?
      accordionController; // Controller for accordion behavior

  const CustomExpansionTileNew({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.white,
    this.showArrow = true,
    this.showThreeDots = true,
    this.panelId,
    this.accordionController,
  });

  @override
  State<CustomExpansionTileNew> createState() => CustomExpansionTileNewState();
}

class CustomExpansionTileNewState extends State<CustomExpansionTileNew>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurn;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));
    _iconTurn = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeIn)));

    // Set initial expansion state
    if (widget.accordionController != null && widget.panelId != null) {
      _isExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
    } else {
      _isExpanded = widget.initiallyExpanded;
    }

    if (_isExpanded) {
      _controller.value = 1.0;
    }

    // Listen to accordion controller changes
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.addListener(_onAccordionStateChanged);
    }
  }

  @override
  void didUpdateWidget(CustomExpansionTileNew oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initiallyExpanded != oldWidget.initiallyExpanded) {
      if (widget.initiallyExpanded) {
        _isExpanded = true;
        _controller.forward();
      } else {
        _isExpanded = false;
        _controller.reverse();
      }
    }
  }

  void _onAccordionStateChanged() {
    if (widget.accordionController != null && widget.panelId != null) {
      final shouldBeExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
      if (shouldBeExpanded != _isExpanded) {
        setState(() {
          _isExpanded = shouldBeExpanded;
          if (_isExpanded) {
            _controller.forward();
          } else {
            _controller.reverse();
          }
          widget.onExpansionChanged(_isExpanded);
        });
      }
    }
  }

  @override
  void dispose() {
    // Remove accordion controller listener
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.removeListener(_onAccordionStateChanged);
    }
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    // Only allow expansion if arrow is shown
    if (!widget.showArrow) return;

    // If accordion controller is provided and panelId is set, use accordion behavior
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.togglePanel(widget.panelId!);
      return;
    }

    // Default behavior (non-accordion)
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: _isExpanded ? Color(0xffE4EDFF) : widget.backgroundColor,
      child: Column(
        children: [
          // Title row with conditional tap handlers
          InkWell(
            onTap: () {
              final isMobile = ScreenResponsiveHelper.isMobile(context);

              if (isMobile) {
                // Mobile behavior: First tap expands, second tap (on expanded content) calls onTitleTap
                if (!_isExpanded && widget.showArrow) {
                  // First tap: expand the content
                  _toggleExpansion();
                } else if (_isExpanded) {
                  // Second tap on expanded content: call onTitleTap (which should show bottom sheet)
                  widget.onTitleTap();
                } else {
                  // If no arrow, just call onTitleTap
                  widget.onTitleTap();
                }
              } else {
                // Desktop behavior: Always call onTitleTap and toggle expansion if arrow is shown
                widget.onTitleTap();
                if (widget.showArrow) {
                  _toggleExpansion();
                }
              }
            },
            child: Container(
              color: Colors
                  .transparent, // Make it transparent to let parent color show through
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: widget.title),
                  // Conditionally show arrow icon
                  if (widget.showArrow)
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 7.0),
                      child: HoverArrowIcon(
                        onTap: () {
                          // Arrow always just toggles expansion, regardless of platform
                          _toggleExpansion();
                        },
                        iconTurn: _iconTurn,
                      ),
                    ),
                  // HoverArrowIcon(
                  //   onTap: _toggleExpansion,
                  //   iconTurn: _iconTurn,
                  // ),
                  // Static toggle menu after down arrow
                  if (widget.showArrow) StaticToggleMenu(),
                  // Always show three dots icon
                  widget.showThreeDots
                      ? HoverThreeDotsIcon(
                          onTap: widget.onThreeDotsPressed ?? () {},
                        )
                      : SizedBox(),
                ],
              ),
            ),
          ),
          // Expandable content
          AnimatedBuilder(
            animation: _controller.view,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _heightFactor.value,
                  alignment:
                      Alignment.topLeft, // Align content to the start (left)
                  // Align content to the start (left)
                  child: child,
                ),
              );
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.children,
            ),
          ),
        ],
      ),
    );
  }
}

class HoverArrowIcon extends StatefulWidget {
  final VoidCallback onTap;
  final Animation<double> iconTurn;

  const HoverArrowIcon({
    super.key,
    required this.onTap,
    required this.iconTurn,
  });

  @override
  State<HoverArrowIcon> createState() => _HoverArrowIconState();
}

class _HoverArrowIconState extends State<HoverArrowIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: RotationTransition(
            turns: widget.iconTurn,
            child: Icon(
              Icons.keyboard_arrow_down,
              size: 20,
              color: isHovered ? Color(0xff0058FF) : Colors.grey.shade800,
            ),
          ),
        ),
      ),
    );
  }
}

class HoverThreeDotsIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverThreeDotsIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverThreeDotsIcon> createState() => _HoverThreeDotsIconState();
}

class _HoverThreeDotsIconState extends State<HoverThreeDotsIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final isMobile = ScreenResponsiveHelper.isMobile(context);

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: () {
          if (isMobile) {
            EntityMenuItems.show(context);
          } else {
            widget.onTap();
          }
        },
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            size: 20,
            color: isHovered ? const Color(0xff0058FF) : Colors.grey.shade800,
          ),
        ),
      ),
    );
  }
}

// Provider for managing toggle menu state
class ToggleMenuProvider extends ChangeNotifier {
  bool _isHovered = false;
  bool _isMenuOpen = false;

  bool get isHovered => _isHovered;
  bool get isMenuOpen => _isMenuOpen;

  void setHovered(bool hovered) {
    if (_isHovered != hovered) {
      _isHovered = hovered;
      notifyListeners();
    }
  }

  void setMenuOpen(bool open) {
    if (_isMenuOpen != open) {
      _isMenuOpen = open;
      notifyListeners();
    }
  }

  void handleMenuSelection(String value) {
    // Handle menu selection logic here
    // This can be extended to handle different menu actions
    setMenuOpen(false);
    notifyListeners();
  }
}

class StaticToggleMenu extends StatelessWidget {
  const StaticToggleMenu({super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = ScreenResponsiveHelper.isMobile(context);

    if (isMobile) {
      // Mobile: Show simple icon that opens bottom sheet
      return InkWell(
        onTap: () => EntityMenuItems.show(context),
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          height: 28,
          width: 28,
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(6.0),
          ),
          child: const Center(
            child: Icon(
              Icons.more_vert,
              size: 20,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    // Desktop: Show original popup menu
    return ChangeNotifierProvider(
      create: (_) => ToggleMenuProvider(),
      child: Consumer<ToggleMenuProvider>(
        builder: (context, provider, child) {
          return MouseRegion(
            onEnter: (_) => provider.setHovered(true),
            onExit: (_) => provider.setHovered(false),
            child: InkWell(
              onTap: () {},
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              child: Center(
                child: Container(
                  height: 28,
                  width: 28,
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: provider.isMenuOpen
                        ? const Color(0xff0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: _buildDesktopPopupMenu(provider),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDesktopPopupMenu(ToggleMenuProvider provider) {
    return PopupMenuButton<String>(
      tooltip: '',
      icon: Icon(
        Icons.more_vert,
        size: 20,
        color: provider.isMenuOpen
            ? Colors.white
            : provider.isHovered
                ? const Color(0xff0058FF)
                : Colors.grey,
      ),
      onSelected: (String value) {
        provider.handleMenuSelection(value);
      },
      onOpened: () {
        provider.setMenuOpen(true);
      },
      onCanceled: () {
        provider.setMenuOpen(false);
      },
      constraints: const BoxConstraints(
        minWidth: 100,
        maxWidth: 120,
      ),
      itemBuilder: (BuildContext context) => EntityMenuItems._menuItems
          .map((item) => PopupMenuItem<String>(
                value: item['value'],
                height: 28,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      item['icon']!,
                      width: 16,
                      height: 16,
                      colorFilter: ColorFilter.mode(
                        Colors.grey.shade700,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        item['title']!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          color: Colors.black,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
        side: const BorderSide(
          color: Color(0xFF0058FF),
          width: 0.5,
        ),
      ),
      elevation: 8,
      color: Colors.white,
      splashRadius: 20,
      padding: const EdgeInsets.symmetric(vertical: 4),
    );
  }
}
