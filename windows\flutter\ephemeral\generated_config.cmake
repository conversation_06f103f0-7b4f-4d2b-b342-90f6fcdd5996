# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter-projects\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\ui-flutter\\design-time-ui-flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.4+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 4 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter-projects\\flutter"
  "PROJECT_DIR=C:\\ui-flutter\\design-time-ui-flutter"
  "FLUTTER_ROOT=C:\\flutter-projects\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\ui-flutter\\design-time-ui-flutter\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\ui-flutter\\design-time-ui-flutter"
  "FLUTTER_TARGET=C:\\ui-flutter\\design-time-ui-flutter\\test_multi_select_scroll.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\ui-flutter\\design-time-ui-flutter\\.dart_tool\\package_config.json"
)
