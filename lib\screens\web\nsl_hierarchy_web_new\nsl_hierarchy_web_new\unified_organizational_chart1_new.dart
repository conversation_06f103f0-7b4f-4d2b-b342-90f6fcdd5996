import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/nsl_hierarchy_provider_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/root_node_top_bar1_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/simplified_nsl_card1_new.dart';
import 'package:provider/provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class UnifiedOrganizationalChart1New extends StatelessWidget {
  final NSLNode rootNode;
  final Map<String, dynamic>? organizationalStructure;
  final String? selectedNodeId;
  final bool isSidePanelOpen;
  final bool isLoPanelOpen;
  final double sidePanelWidth;
  final NslTreeSidePanel? rootNodeDetails;
  final MetricsInfo? nodeTransactions;
  final Function(NSLNode)? onNodeTitleTap;
  final Function(NSLNode)? onNodeInfoTap;
  final double horizontalSpacing;
  final double verticalSpacing;
  final double lineThickness;
  final Color lineColor;

  const UnifiedOrganizationalChart1New({
    super.key,
    required this.rootNode,
    this.organizationalStructure,
    this.selectedNodeId,
    this.isSidePanelOpen = false,
    this.isLoPanelOpen = false,
    this.sidePanelWidth = 0.0,
    this.rootNodeDetails,
    this.nodeTransactions,
    this.onNodeTitleTap,
    this.onNodeInfoTap,
    this.horizontalSpacing = 120.0,
    this.verticalSpacing = 60.0,
    this.lineThickness = 1.0,
    this.lineColor = const Color.fromARGB(255, 202, 202, 202),
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProviderNew>(
      builder: (context, provider, child) {
        return _UnifiedOrganizationalChart1New(
          rootNode: rootNode,
          organizationalStructure: organizationalStructure,
          selectedNodeId: selectedNodeId,
          isSidePanelOpen: isSidePanelOpen,
          isLoPanelOpen: isLoPanelOpen,
          sidePanelWidth: sidePanelWidth,
          rootNodeDetails: rootNodeDetails,
          nodeTransactions: nodeTransactions,
          onNodeTitleTap: onNodeTitleTap,
          onNodeInfoTap: onNodeInfoTap,
          horizontalSpacing: horizontalSpacing,
          verticalSpacing: verticalSpacing,
          lineThickness: lineThickness,
          lineColor: lineColor,
          provider: provider,
        );
      },
    );
  }
}

class _UnifiedOrganizationalChart1New extends StatefulWidget {
  final NSLNode rootNode;
  final Map<String, dynamic>? organizationalStructure;
  final String? selectedNodeId;
  final bool isSidePanelOpen;
  final bool isLoPanelOpen;
  final double sidePanelWidth;
  final NslTreeSidePanel? rootNodeDetails;
  final MetricsInfo? nodeTransactions;
  final Function(NSLNode)? onNodeTitleTap;
  final Function(NSLNode)? onNodeInfoTap;
  final double horizontalSpacing;
  final double verticalSpacing;
  final double lineThickness;
  final Color lineColor;
  final NslHierarchyProviderNew provider;

  const _UnifiedOrganizationalChart1New({
    required this.rootNode,
    this.organizationalStructure,
    this.selectedNodeId,
    this.isSidePanelOpen = false,
    this.isLoPanelOpen = false,
    this.sidePanelWidth = 0.0,
    this.rootNodeDetails,
    this.nodeTransactions,
    this.onNodeTitleTap,
    this.onNodeInfoTap,
    this.horizontalSpacing = 120.0,
    this.verticalSpacing = 60.0,
    this.lineThickness = 1.0,
    this.lineColor = const Color.fromARGB(255, 202, 202, 202),
    required this.provider,
  });

  @override
  State<_UnifiedOrganizationalChart1New> createState() => _UnifiedOrganizationalChart1NewState();
}

class _UnifiedOrganizationalChart1NewState extends State<_UnifiedOrganizationalChart1New> {
  late NSLNode _rootNode;
  final TransformationController _transformationController = TransformationController();
  
  // Level configuration
  static const double levelHeight = 114.0;
  static const double headerHeight = 44.0;
  
  // Card dimensions
  double _cardWidth = 123.0;
  double _cardHeight = 60.0;

  @override
  void initState() {
    super.initState();
    _rootNode = widget.rootNode;
    _initializeDynamicLevels();
    _calculateAllSubtreeWidths(_rootNode);
    
    // Initialize selected path if a node is already selected
    if (widget.selectedNodeId != null) {
      final pathIds = widget.provider.getPathToNode(widget.selectedNodeId!);
      widget.provider.updateSelectedPathIds(pathIds);
    }
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _transformationController.value = Matrix4.identity()
        ..scale(0.8, 0.8)
        ..translate(25.0, 25.0);
      _updateActualSeparatorPositions();
      _scrollToCenter();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(_UnifiedOrganizationalChart1New oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.rootNode != widget.rootNode) {
      _rootNode = widget.rootNode;
      setState(() {});
    }
    
    if (oldWidget.isSidePanelOpen != widget.isSidePanelOpen ||
        oldWidget.sidePanelWidth != widget.sidePanelWidth) {
      setState(() {});
    }
    
    if (oldWidget.selectedNodeId != widget.selectedNodeId) {
      // setState(() {
      //   if (widget.selectedNodeId != null) {
      //     final pathIds = widget.provider.getPathToNode(widget.selectedNodeId!);
      //     widget.provider.updateSelectedPathIds(pathIds);
      //   } else {
      //     widget.provider.updateSelectedPathIds([]);
      //   }
      // });
      
      if (widget.selectedNodeId != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToCenter();
        });
      }
    }
  }

  void _initializeDynamicLevels() {
    final availableLevels = _getAvailableLevels();
    final levelCenterY = _calculateDynamicLevelCenterY();
    final dynamicSeparatorPositions = _calculateDynamicSeparatorPositions();
    
    widget.provider.setAvailableLevels(availableLevels);
    widget.provider.setLevelCenterY(levelCenterY);
    widget.provider.setDynamicSeparatorPositions(dynamicSeparatorPositions);
    _initializeSeparatorKeys();
  }

  void _initializeSeparatorKeys() {
    final separatorKeys = <String, GlobalKey>{};
    final availableLevels = widget.provider.availableLevels;
    
    for (int i = 0; i < availableLevels.length - 1; i++) {
      final parentLevel = availableLevels[i];
      final childLevel = availableLevels[i + 1];
      final separatorKey = '$parentLevel-$childLevel';
      separatorKeys[separatorKey] = GlobalKey();
    }
    
    widget.provider.setSeparatorKeys(separatorKeys);
  }

  double? _getActualSeparatorPosition(String separatorKey) {
    final key = widget.provider.separatorKeys[separatorKey];
    if (key?.currentContext != null) {
      final RenderBox? renderBox = key!.currentContext!.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero);
        return position.dy;
      }
    }
    return null;
  }

  void _updateActualSeparatorPositions() {
    final actualPositions = <String, double>{};
    for (final separatorKey in widget.provider.separatorKeys.keys) {
      final actualPosition = _getActualSeparatorPosition(separatorKey);
      if (actualPosition != null) {
        actualPositions[separatorKey] = actualPosition;
      }
    }
    widget.provider.setActualSeparatorPositions(actualPositions);
  }

  List<String> _getAvailableLevels() {
    var levels = widget.organizationalStructure?.keys.toList() ?? [];
    if (levels.isEmpty) {
      final orgStructure = widget.organizationalStructure?['organizational_structure'] as Map<String, dynamic>?;
      levels = orgStructure?.keys.toList() ?? [];
    }
    levels.sort((a, b) {
      final aNum = _extractLevelNumber(a);
      final bNum = _extractLevelNumber(b);
      return bNum.compareTo(aNum);
    });
    return levels;
  }

  int _extractLevelNumber(String level) {
    return int.tryParse(level.substring(1)) ?? 0;
  }

  Map<String, double> _calculateDynamicLevelCenterY() {
    final levelCenters = <String, double>{};
    final availableLevels = _getAvailableLevels();
    
    for (int i = 0; i < availableLevels.length; i++) {
      final level = availableLevels[i];
      final centerY = effectiveHeaderHeight + (i * levelHeight) + (levelHeight / 2);
      levelCenters[level] = centerY;
    }
    return levelCenters;
  }

  double get effectiveHeaderHeight {
    final baseHeaderHeight = widget.selectedNodeId != null ? 44.0 : 0.0;
    final topBarHeight = widget.selectedNodeId == null ? 36.5 : 0.0;
    return baseHeaderHeight + topBarHeight;
  }

  Map<String, double> _calculateDynamicSeparatorPositions() {
    final separators = <String, double>{};
    final availableLevels = _getAvailableLevels();
    
    for (int i = 0; i < availableLevels.length - 1; i++) {
      final parentLevel = availableLevels[i];
      final childLevel = availableLevels[i + 1];
      final separatorKey = '$parentLevel-$childLevel';
      final separatorY = effectiveHeaderHeight + ((i + 1) * levelHeight);
      separators[separatorKey] = separatorY;
    }
    return separators;
  }

  void _scrollToCenter() {
    if (!widget.provider.horizontalScrollController.hasClients) return;
    
    if (widget.isSidePanelOpen && widget.selectedNodeId != null) {
      _ensureSelectedNodeVisible(widget.selectedNodeId!);
    } else {
      _scrollToCenterNormally();
    }
  }

  void _scrollToCenterNormally() {
    _ensureNodeVisibleAfterExpansion(_rootNode.id);
  }

  void _ensureSelectedNodeVisible(String selectedNodeId) {
    final nodePositions = _calculateNodePositions(_rootNode);
    final selectedPosition = nodePositions[selectedNodeId];
    if (selectedPosition == null) return;

    final screenWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = screenWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth = screenWidth - leftPanelWidth - sidePanelWidth;

    final currentScrollOffset = widget.provider.horizontalScrollController.offset;
    final viewportLeft = currentScrollOffset;
    final viewportRight = currentScrollOffset + availableRightPanelWidth;

    final nodeLeft = selectedPosition.x;
    final nodeRight = selectedPosition.x + selectedPosition.width;

     const double padding = 50.0;
    double? newScrollOffset;

    if (nodeLeft < viewportLeft + padding) {
      newScrollOffset = math.max(0, nodeLeft - padding);
    } else if (nodeRight > viewportRight - padding) {
      newScrollOffset = nodeRight - availableRightPanelWidth + padding;
    }

    if (newScrollOffset != null) {
      newScrollOffset = math.max(0, newScrollOffset);
      widget.provider.horizontalScrollController.animateTo(
        newScrollOffset,
        duration: Duration(milliseconds: 1),
        curve: Curves.easeInOut,
      );
    }
  }

  void _ensureNodeVisibleAfterExpansion(String nodeId) {
    if (!widget.provider.horizontalScrollController.hasClients) return;
    
    final nodePositions = _calculateNodePositions(_rootNode);
    final nodePosition = nodePositions[nodeId];
    if (nodePosition == null) return;
    
    final screenWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = screenWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth = screenWidth - leftPanelWidth - sidePanelWidth;
    
    final nodeCenterX = nodePosition.x + (nodePosition.width / 2);
    final viewportCenterX = availableRightPanelWidth / 2;
    final scrollOffset = math.max(0.0, nodeCenterX - viewportCenterX);
    
    widget.provider.horizontalScrollController.animateTo(
      scrollOffset,
      duration: Duration(milliseconds: 1),
      curve: Curves.easeInOut,
    );
  }

  void _calculateAllSubtreeWidths(NSLNode node) {
    _getSubtreeWidth(node);
  }

  double _getSubtreeWidth(NSLNode node) {
    final existingWidth = widget.provider.getSubtreeWidth(node.id);
    if (existingWidth != null) {
      return existingWidth;
    }
    
    double width;
    if (!node.isExpanded || node.children.isEmpty) {
      width = _cardWidth;
    } else {
      double childrenCombinedWidth = node.children.length * _cardWidth;
      if (node.children.length > 1) {
        childrenCombinedWidth += (node.children.length - 1) * widget.horizontalSpacing;
      }
      width = childrenCombinedWidth;
      if (width < _cardWidth) {
        width = _cardWidth;
      }
    }
    
    widget.provider.setSubtreeWidth(node.id, width);
    return width;
  }

  double _calculateTotalTreeWidth(NSLNode rootNode) {
    final nodePositions = _calculateNodePositions(rootNode);
    
    double minX = double.infinity;
    double maxX = 0;
    
    for (final position in nodePositions.values) {
      final leftEdge = position.x;
      final rightEdge = position.x + position.width;
      
      if (leftEdge < minX) {
        minX = leftEdge;
      }
      if (rightEdge > maxX) {
        maxX = rightEdge;
      }
    }
    
    final leftPadding = math.max(200.0, -minX + 200.0);
    final rightPadding = 200.0;
    
    return (maxX - minX) + leftPadding + rightPadding;
  }

  @override
  Widget build(BuildContext context) {
    final newCardWidth = 123.0;
    final newCardHeight = 60.0;
    
    if (_cardWidth != newCardWidth || _cardHeight != newCardHeight) {
      _cardWidth = newCardWidth;
      _cardHeight = newCardHeight;
      widget.provider.clearSubtreeWidths();
      _calculateAllSubtreeWidths(_rootNode);
    }

    return Container(
      decoration: BoxDecoration(),
      child: Stack(
        children: [
          Row(
            children: [
              _buildLeftPanel(),
              Expanded(
                child: _buildRightPanel(),
              ),
            ],
          ),
          if (widget.selectedNodeId == null)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: RootNodeTopBar1New(
                rootNode: _rootNode,
                rootNodeDetails: widget.rootNodeDetails,
                nodeTransactions: widget.nodeTransactions,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLeftPanel() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.0695,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          right: BorderSide(color: Color(0xffD0D0D0), width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.selectedNodeId == null) SizedBox(height: 44),
          if (widget.selectedNodeId != null)
            Container(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.xs),
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                border: Border(
                  bottom: BorderSide(color: Color(0xFF797676), width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Organisation Departments',
                          maxLines: 2,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ..._buildDepartmentLevels(),
        ],
      ),
    );
  }

  List<Widget> _buildDepartmentLevels() {
    final List<Widget> levels = [];
    var availableLevels = widget.organizationalStructure?.keys.toList() ?? [];
    
    if (availableLevels.isEmpty) {
      final orgStructure = widget.organizationalStructure?['organizational_structure'] as Map<String, dynamic>?;
      availableLevels = orgStructure?.keys.toList() ?? [];
    }
    
    availableLevels.sort((a, b) {
      final aNum = int.tryParse(a.substring(1)) ?? 0;
      final bNum = int.tryParse(b.substring(1)) ?? 0;
      return bNum.compareTo(aNum);
    });

    final selectedNodeLevel = widget.provider.getSelectedNodeLevel();
    
    for (int i = 0; i < availableLevels.length; i++) {
      final levelKey = availableLevels[i];
      var levelData = widget.organizationalStructure![levelKey];
      
      if (levelData == null) {
        final orgStructure = widget.organizationalStructure?['organizational_structure'] as Map<String, dynamic>?;
        levelData = orgStructure?[levelKey];
      }
      
      if (levelData != null) {
        final levelName = levelData['level_name'] ?? levelKey;
        levels.add(
          _buildDepartmentLevel(
            level: levelKey,
            title: levelName,
            isSelected: selectedNodeLevel == levelKey,
          ),
        );
        
        if (i < availableLevels.length - 1) {
          final nextLevelKey = availableLevels[i + 1];
          final separatorKey = '$levelKey-$nextLevelKey';
          final globalKey = widget.provider.separatorKeys[separatorKey];
          levels.add(
            Container(
              key: globalKey,
              height: 0.5,
              color: Color(0xff797676),
            ),
          );
        }
        
        if (i == availableLevels.length - 1) {
          levels.add(
            Container(
              height: 0.5,
              color: Color(0xff797676),
            ),
          );
        }
      }
    }
    return levels;
  }

  Widget _buildDepartmentLevel({
    required String level,
    required String title,
    required bool isSelected,
  }) {
    return Container(
      width: double.infinity,
      height: levelHeight,
      padding: EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: isSelected
            ? Border(
                right: BorderSide(color: Color(0xFF0058FF), width: 3),
              )
            : null,
      ),
      child: InkWell(
        onTap: () {},
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              level,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.bold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRightPanel() {
    final totalTreeWidth = _calculateTotalTreeWidth(_rootNode);
    final screenWidth = MediaQuery.of(context).size.width;
    final minWidth = screenWidth * 2;
    final containerWidth = math.max(totalTreeWidth, minWidth);
    
    return SingleChildScrollView(
      controller: widget.provider.horizontalScrollController,
      scrollDirection: Axis.horizontal,
      child: Container(
        width: containerWidth,
        height: MediaQuery.of(context).size.height,
        color: Colors.white,
        child: _buildOrganizationTree(_rootNode),
      ),
    );
  }

  Widget _buildOrganizationTree(NSLNode rootNode) {
    final nodesInfo = _calculateNodePositions(rootNode);
    final nodeWidgets = _buildAllNodes(rootNode, nodesInfo);
    
    return Stack(
      children: [
        CustomPaint(
          painter: UnifiedConnectionsPainter(
            rootNode: _rootNode,
            nodePositions: nodesInfo,
            selectedPathIds: widget.provider.selectedPathIds,
            defaultLineColor: widget.lineColor,
            lineThickness: widget.lineThickness,
            verticalSpacing: widget.verticalSpacing,
            dynamicSeparatorPositions: widget.provider.dynamicSeparatorPositions,
            actualSeparatorPositions: widget.provider.actualSeparatorPositions,
          ),
          child: Container(),
        ),
        ...nodeWidgets,
      ],
    );
  }

  List<Widget> _buildAllNodes(NSLNode node, Map<String, UnifiedNodePosition> nodesInfo) {
    final List<Widget> allWidgets = [];
    final position = nodesInfo[node.id];
    
    if (position != null) {
      allWidgets.add(
        Positioned(
          left: position.x,
          top: position.y,
          child: _buildNodeWidget(node),
        ),
      );
      
      if (node.isExpanded) {
        for (final child in node.children) {
          allWidgets.addAll(_buildAllNodes(child, nodesInfo));
        }
      }
    }
    return allWidgets;
  }

  Widget _buildNodeWidget(NSLNode node) {
    return Consumer<NslHierarchyProviderNew>(
      builder: (context, provider, child) {
        final isNodeSelected = widget.selectedNodeId == node.id;
        final isInfoSelected = provider.selectedInfoNodeId == node.id;
        
        return SimplifiedNSLCard1New(
          node: node,
          isInfoTapped: node.id == provider.selectedInfoNodeId,
          isSelected: isNodeSelected,
          isHighlightedInPath: provider.selectedPathIds.contains(node.id),
          onTitleTap: () {
            final pathIds = provider.getPathToNode(node.id);
            widget.provider.updateSelectedPathIds(pathIds);
            widget.onNodeTitleTap?.call(node);
          },
          onInfoTap: () {
            widget.provider.onNodeInfoTapWithExpansion(node);
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _ensureNodeVisibleAfterExpansion(node.id);
            });
            widget.onNodeInfoTap?.call(node);
          },
        );
      },
    );
  }

  Map<String, UnifiedNodePosition> _calculatePreliminaryNodePositions(
      NSLNode rootNode, double leftPanelWidth, double availableRightPanelWidth) {
    final Map<String, UnifiedNodePosition> positions = {};
    
    final rightPanelCenterX = leftPanelWidth + (availableRightPanelWidth / 2);
    final rootY = widget.provider.levelCenterY[rootNode.level] ?? 0.0;
    final rootCardY = rootY - (_cardHeight / 2);
    final rootCardX = rightPanelCenterX - (_cardWidth / 2);
    
    positions[rootNode.id] = UnifiedNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootCardY,
      width: _cardWidth,
      height: _cardHeight,
      level: rootNode.level,
    );
    
    _calculateChildPositions(rootNode, positions, rootCardX, rootCardY, _cardWidth, _cardHeight);
    return positions;
  }

  Map<String, UnifiedNodePosition> _calculateNodePositions(NSLNode rootNode) {
    final Map<String, UnifiedNodePosition> positions = {};
    final totalWidth = MediaQuery.of(context).size.width;
    final leftPanelWidth = totalWidth * 0.0688;
    final sidePanelWidth = widget.isSidePanelOpen ? widget.sidePanelWidth : 0.0;
    final availableRightPanelWidth = totalWidth - leftPanelWidth - sidePanelWidth;
    
    final preliminaryPositions = _calculatePreliminaryNodePositions(rootNode, leftPanelWidth, availableRightPanelWidth);
    double minX = double.infinity;
    for (final position in preliminaryPositions.values) {
      if (position.x < minX) {
        minX = position.x;
      }
    }
    
    final dynamicLeftPadding = math.max(200.0, -minX + 200.0);
    final rightPanelCenterX = dynamicLeftPadding + leftPanelWidth + (availableRightPanelWidth / 2);
    final rootY = widget.provider.levelCenterY[rootNode.level] ?? 0.0;
    final rootCardY = rootY - (_cardHeight / 2);
    final rootCardX = rightPanelCenterX - (_cardWidth / 2);
    
    positions[rootNode.id] = UnifiedNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootCardY,
      width: _cardWidth,
      height: _cardHeight,
      level: rootNode.level,
    );
    
    _calculateChildPositions(rootNode, positions, rootCardX, rootCardY, _cardWidth, _cardHeight);
    return positions;
  }

  void _calculateChildPositions(
    NSLNode parent,
    Map<String, UnifiedNodePosition> positions,
    double parentCardX,
    double parentCardY,
    double parentCardWidth,
    double parentCardHeight,
  ) {
    if (!parent.isExpanded || parent.children.isEmpty) return;

    final firstChild = parent.children.first;
    final childLevelY = widget.provider.levelCenterY[firstChild.level] ?? 0.0;
    final childY = childLevelY - (_cardHeight / 2);

    _positionChildrenUnderParent(
      parent: parent,
      children: parent.children,
      parentPosition: positions[parent.id]!,
      childY: childY,
      positions: positions,
    );

    for (final child in parent.children) {
      final childPosition = positions[child.id];
      if (childPosition != null) {
        _calculateChildPositions(
          child,
          positions,
          childPosition.x,
          childPosition.y,
          childPosition.width,
          childPosition.height,
        );
      }
    }
  }

  void _positionChildrenUnderParent({
    required NSLNode parent,
    required List<NSLNode> children,
    required UnifiedNodePosition parentPosition,
    required double childY,
    required Map<String, UnifiedNodePosition> positions,
  }) {
    if (children.isEmpty) return;

    final numberOfChildren = children.length;
    
    if (numberOfChildren == 1) {
      final child = children[0];
      final parentCenterX = parentPosition.x + (parentPosition.width / 2);
      final childX = parentCenterX - (_cardWidth / 2);
      
      String? actualParentId = _findActualParentInPath(child.id, widget.provider.selectedPathIds);
      
      positions[child.id] = UnifiedNodePosition(
        id: child.id,
        x: childX,
        y: childY,
        width: _cardWidth,
        height: _cardHeight,
        parentId: actualParentId ?? parent.id,
        level: child.level,
      );
    } else {
      final reducedSpacing = widget.horizontalSpacing;
      final totalChildrenWidth = (numberOfChildren * _cardWidth) + ((numberOfChildren - 1) * reducedSpacing);
      final parentCenterX = parentPosition.x + (parentPosition.width / 2);
      final startXForChildrenBlock = parentCenterX - (totalChildrenWidth / 2);
      
      for (int i = 0; i < numberOfChildren; i++) {
        final child = children[i];
        final childX = startXForChildrenBlock + (i * (_cardWidth + reducedSpacing));
        
        String? actualParentId = _findActualParentInPath(child.id, widget.provider.selectedPathIds);
        
        positions[child.id] = UnifiedNodePosition(
          id: child.id,
          x: childX,
          y: childY,
          width: _cardWidth,
          height: _cardHeight,
          parentId: actualParentId ?? parent.id,
          level: child.level,
        );
      }
    }
  }

  String? _findActualParentInPath(String childId, List<String> pathIds) {
    int childIndex = pathIds.indexOf(childId);
    if (childIndex > 0) {
      final parentId = pathIds[childIndex - 1];
      return parentId;
    }
    return null;
  }
}

class UnifiedNodePosition {
  final String id;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? parentId;
  final String level;

  UnifiedNodePosition({
    required this.id,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.parentId,
    required this.level,
  });

  Offset get bottomCenter => Offset(x + width / 2, y + height);
  Offset get topCenter => Offset(x + width / 2, y);
  Offset get greenDotPosition {
    return Offset(x + 5, y - 5);
  }
}

class UnifiedConnectionsPainter extends CustomPainter {
  final NSLNode rootNode;
  final Map<String, UnifiedNodePosition> nodePositions;
  final List<String> selectedPathIds;
  final Color defaultLineColor;
  final double lineThickness;
  final double verticalSpacing;
  final Map<String, double> dynamicSeparatorPositions;
  final Map<String, double> actualSeparatorPositions;

  static const double levelHeight = 114.0;
  static const double headerHeight = 44.0;

  UnifiedConnectionsPainter({
    required this.rootNode,
    required this.nodePositions,
    required this.selectedPathIds,
    required this.defaultLineColor,
    required this.dynamicSeparatorPositions,
    required this.actualSeparatorPositions,
    this.lineThickness = 1.0,
    this.verticalSpacing = 80.0,
  });

  NSLNode? _findNodeById(NSLNode currentNode, String id) {
    if (currentNode.id == id) return currentNode;
    for (final child in currentNode.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = lineThickness
      ..isAntiAlias = false
      ..blendMode = BlendMode.srcOver
      ..style = PaintingStyle.stroke;

    for (final position in nodePositions.values) {
      if (position.parentId != null) {
        final parentPosition = nodePositions[position.parentId!];
        if (parentPosition != null) {
          final parentNode = _findNodeById(rootNode, position.parentId!);
          bool isPathHighlighted = selectedPathIds.contains(position.id) &&
              selectedPathIds.contains(position.parentId!);

          if (isPathHighlighted) {
            paint.color = Color(0xFF0058FF);
            paint.strokeWidth = lineThickness * 3;
          } else {
            paint.color = defaultLineColor;
            paint.strokeWidth = lineThickness;
          }
          _drawConnection(canvas, paint, parentPosition, position);
        }
      }
    }
  }

  void _drawConnection(Canvas canvas, Paint paint, UnifiedNodePosition parent,
      UnifiedNodePosition child) {
    final parentConnectionPoint = parent.bottomCenter;
    final childConnectionPoint = child.topCenter;

    final parentNode = _findNodeById(rootNode, parent.id);
    final childNode = _findNodeById(rootNode, child.id);
    double separatorY;

    if (parentNode?.level != null && childNode?.level != null) {
      final separatorKey = '${parentNode!.level}-${childNode!.level}';
      if (actualSeparatorPositions.containsKey(separatorKey)) {
        separatorY = actualSeparatorPositions[separatorKey]!;
      } else if (dynamicSeparatorPositions.containsKey(separatorKey)) {
        separatorY = dynamicSeparatorPositions[separatorKey]!;
      } else {
        separatorY = parentConnectionPoint.dy +
            (childConnectionPoint.dy - parentConnectionPoint.dy) / 2;
      }
    } else {
      separatorY = parentConnectionPoint.dy +
          (childConnectionPoint.dy - parentConnectionPoint.dy) / 2;
    }

    if (parentConnectionPoint.dx == childConnectionPoint.dx) {
      canvas.drawLine(parentConnectionPoint, childConnectionPoint, paint);
    } else {
      canvas.drawLine(parentConnectionPoint,
          Offset(parentConnectionPoint.dx, separatorY), paint);
      canvas.drawLine(Offset(parentConnectionPoint.dx, separatorY),
          Offset(childConnectionPoint.dx, separatorY), paint);
      canvas.drawLine(Offset(childConnectionPoint.dx, separatorY),
          childConnectionPoint, paint);
    }
    _drawArrowhead(canvas, paint, childConnectionPoint, math.pi / 2);
  }

  void _drawArrowhead(Canvas canvas, Paint paint, Offset point, double angle) {
    const double arrowSize = 10.0;
    const double arrowWidth = 10.0;
    final Path trianglePath = Path();
    trianglePath.moveTo(point.dx, point.dy);
    trianglePath.lineTo(point.dx - arrowWidth / 2, point.dy - arrowSize);
    trianglePath.lineTo(point.dx + arrowWidth / 2, point.dy - arrowSize);
    trianglePath.close();
    final fillPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.fill;
    canvas.drawPath(trianglePath, fillPaint);
  }

  @override
  bool shouldRepaint(UnifiedConnectionsPainter oldDelegate) =>
      oldDelegate.nodePositions != nodePositions ||
      oldDelegate.rootNode != rootNode ||
      oldDelegate.selectedPathIds != selectedPathIds ||
      oldDelegate.defaultLineColor != defaultLineColor ||
      oldDelegate.lineThickness != lineThickness;
}
