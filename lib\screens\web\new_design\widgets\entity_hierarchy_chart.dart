
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/entity_profile_card.dart'
    as entity_card;
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';


class ERDTable {
  final String id;
  final Entity1 entity;
  Offset position;
  ERDTable({required this.id, required this.entity, required this.position});
}

class ERDRelation {
  final String from;
  final String to;
  final String fromCardinality; // e.g. "1", "N"
  final String toCardinality;   // e.g. "1", "N"
  final String? fromAttribute;  // FK attribute name
  final String? toAttribute;    // PK attribute name
  
  ERDRelation({
    required this.from,
    required this.to,
    required this.fromCardinality,
    required this.toCardinality,
    this.fromAttribute,
    this.toAttribute,
  });
}

class EntitiesDiagram extends StatefulWidget {
  final entities_model.EntitiesData data;
  const EntitiesDiagram({super.key, required this.data});

  @override
  State<EntitiesDiagram> createState() => _EntitiesDiagramState();
}

class _EntitiesDiagramState extends State<EntitiesDiagram> {
  static const double cardWidth = 220;
  static const double cardHeight = 200;
  static const double gridSize = 40.0;

  late List<ERDTable> tables;
  late List<ERDRelation> relations;
  Entity1? selectedEntity;
  Offset? selectedEntityPosition;
  String? highlightedAttribute;
  bool showDataCard = false;
  double _zoomLevel = 1.0;
  static const double _minZoom = 0.5;
  static const double _maxZoom = 2.0;

  late final ScrollController _horizontalScrollController;
   final Map<String, bool> _expandedStates = {};
   entities_model.Entity? entity1;
   final Map<String, GlobalKey> _entityCardKeys = {};
   String? highlightedEntityId;

    @override
  void initState() {
    _horizontalScrollController = ScrollController();
    super.initState();

    final entityGroups = widget.data.entityGroups ?? [];
    tables = [];
    double x = 40;
    double y = 40;

    const double spacingX = 350;
    const double spacingY = 12;
    const double headerHeight = 48;
    const double attributeHeight = 32;
    const double verticalPadding = 12;
    

    int cardsPerColumn = 2;
    int cardCount = 0;

    for (final group in entityGroups) {
      final entities = group.entities ?? [];
      
      for (final entity in entities) {
        entity1=entity;
        final attributes = entity.attributes?.map((attr) {
          return Attribute(
            name: attr.name ?? '',
            type: attr.type ?? '',
            isPrimaryKey: attr.isPk ?? false,
            isForeignKey: attr.isFk ?? false,
            referencesEntity: attr.description ?? '',
          );
        }).toList() ?? [];

        final trimmedEntity = Entity1(
          name: entity.title ?? '',
          attributes: attributes,
          data: [],
        );

        tables.add(ERDTable(
          id: entity.id ?? '',
          entity: trimmedEntity,
          position: Offset(x, y),
        ));

        _expandedStates[trimmedEntity.name] = false;

        double cardHeight = headerHeight + verticalPadding * 2 + (attributeHeight * 3);

        y += cardHeight + spacingY;
        cardCount++;

        if (cardCount % cardsPerColumn == 0) {
          x += spacingX;
          y = 40;
        }
      }
    }
    for (final table in tables) {
  _entityCardKeys[table.id] = GlobalKey();
}

    relations = _extractRelationsFromEntities(tables);
     _layoutCards();
  }

  List<ERDRelation> _extractRelationsFromEntities(List<ERDTable> tables) {
    final List<ERDRelation> relations = [];

    final entityNameMap = {
      for (var t in tables) t.entity.name.trim().toLowerCase(): t,
    };

    final entityIdMap = {
      for (var t in tables) t.id.trim(): t,
    };

    for (final sourceTable in tables) {
      for (final attr in sourceTable.entity.attributes) {
        if (attr.isForeignKey == true) {
          ERDTable? targetTable;

          // 1. Try infer from FK attribute name
          final guessedName = _inferTargetEntityName(attr.name);
          targetTable = entityNameMap[guessedName.toLowerCase()];
          if (kDebugMode) {
            print("Trying inferred target for '${attr.name}': $guessedName => ${targetTable?.id}");
          }

          // 2. Try using referencesEntity
          if ((targetTable == null || targetTable.id == sourceTable.id) &&
              attr.referencesEntity != null &&
              attr.referencesEntity!.isNotEmpty) {
            final refName = attr.referencesEntity!.trim().toLowerCase();
            targetTable = entityNameMap[refName] ?? entityIdMap[attr.referencesEntity!.trim()];
            if (kDebugMode) {
              print("Trying referencesEntity '${attr.referencesEntity}': => ${targetTable?.id}");
            }
          }

          // 3. If valid and not self
          if (targetTable != null && targetTable.id != sourceTable.id) {
            final pk = targetTable.entity.attributes.firstWhere(
              (a) => a.isPrimaryKey == true,
              orElse: () => targetTable!.entity.attributes.first,
            );

            relations.add(ERDRelation(
              from: sourceTable.id,
              to: targetTable.id,
              fromCardinality: 'N',
              toCardinality: '1',
              fromAttribute: attr.name,
              toAttribute: pk.name,
            ));

            print("Relation added: ${sourceTable.id} -> ${targetTable.id}");
          } else {
            print(
                "Could not resolve relation for attribute: ${attr.name}, ref: ${attr.referencesEntity}");
          }
        }
      }
    }
    return relations;
  }

  String _inferTargetEntityName(String attributeName) {
    return attributeName
        .replaceAll(RegExp(r'_?id$', caseSensitive: false), '') // remove trailing _id or ID
        .trim()
        .replaceAll(RegExp(r'\s+'), ' '); // normalize spaces
  }

  Offset snapToGrid(Offset rawPosition, double gridSize) {
    final x = (rawPosition.dx / gridSize).round() * gridSize;
    final y = (rawPosition.dy / gridSize).round() * gridSize;
    return Offset(x, y);
  }
 
  

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

 List<InlineSpan> titleSpans = [];
    OverlayEntry? _profileTooltipOverlay;


    // Show entity profile tooltip
void _showEntityProfileTooltipForCard(GlobalKey key, entities_model.Entity entity) {
  _hideEntityProfileTooltip();

  final RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
  if (renderBox == null) return;

  final Offset globalPosition = renderBox.localToGlobal(Offset.zero);
  final Size size = renderBox.size;

  final screenSize = MediaQuery.of(context).size;
  const double tooltipWidth = 320.0;
  const double tooltipMaxHeight = 300.0;

  double left = globalPosition.dx + size.width + 12;
  double top = globalPosition.dy;

  if (left + tooltipWidth > screenSize.width - 20) {
    left = globalPosition.dx - tooltipWidth - 12;
  }
  if (left < 20) left = 20;
  if (top + tooltipMaxHeight > screenSize.height - 20) {
    top = screenSize.height - tooltipMaxHeight - 20;
  }
  if (top < 20) top = 20;

  _profileTooltipOverlay = OverlayEntry(
    builder: (context) => Positioned(
      left: left,
      top: top,
      child: MouseRegion(
        onEnter: (_) {},
        onExit: (_) => _hideEntityProfileTooltip(),
        child: Material(
          color: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(
              maxWidth: tooltipWidth,
              maxHeight: tooltipMaxHeight,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 8)],
            ),
            child: entity_card.EntityProfileCard(
              entity: entity,
              width: tooltipWidth,
              leftMargin: 0,
            ),
          ),
        ),
      ),
    ),
  );

  Overlay.of(context).insert(_profileTooltipOverlay!);
}


  // Hide entity profile tooltip
  void _hideEntityProfileTooltip() {
    _profileTooltipOverlay?.remove();
    _profileTooltipOverlay = null;
  }

  entities_model.Entity? getEntityById(String id) {
  for (final group in widget.data.entityGroups ?? []) {
    for (final entity in group.entities ?? []) {
      if (entity.id == id) {
        return entity;
      }
    }
  }
  return null;
}

Size _calculateCanvasSize() {
  double maxX = 0;
  double maxY = 0;

  for (final table in tables) {
    final expanded = _expandedStates[table.id] ?? false;
    final height = calculateCardHeight(table.entity, expanded);

    final right = table.position.dx + cardWidth;
    final bottom = table.position.dy + height;

    if (right > maxX) maxX = right;
    if (bottom > maxY) maxY = bottom;
  }

  return Size(maxX + 100, maxY + 100); // Add margin to avoid clipping
}

@override
Widget build(BuildContext context) {
  final canvasSize = _calculateCanvasSize();

  return Scaffold(
    body: Stack(
      children: [
        Positioned(
          top: 16,
          left: 2,
          right: 24,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Great! Here is your Entities for your solution.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 6),
              Wrap(
                spacing: 10,
                runSpacing: 6,
                children: [
                  // _buildCategoryPill('8 Core', Colors.orange.shade700),
                  // _buildCategoryPill('1 Transaction', Colors.green.shade700),
                ],
              ),
            ],
          ),
        ),

        // 🔹 Scrollable Canvas with border and zoom
        Positioned.fill(
          top: 40, // Push canvas below header
          child: Center(
            child: Scrollbar(
              controller: _horizontalScrollController,
              thumbVisibility: true,
              trackVisibility: true,
              scrollbarOrientation: ScrollbarOrientation.bottom,
              child: SingleChildScrollView(
                controller: _horizontalScrollController,
                scrollDirection: Axis.horizontal,
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Transform.scale(
                      alignment: Alignment.topLeft,
                      scale: _zoomLevel,
                      child: SizedBox(
                         width: canvasSize.width * _zoomLevel,
                           height: canvasSize.height * _zoomLevel,
                        child: Stack(
                          children: [
                            // Grid
                            Positioned.fill(
                              child: CustomPaint(
                                painter: _GridPainter(gridSize: gridSize),
                              ),
                            ),

                            // Relation lines
                            Positioned.fill(
                              child: CustomPaint(
                                painter: _ERDEdgePainter(
                                  tables: tables,
                                  relations: relations,
                                  cardWidth: cardWidth,
                                  cardHeight: cardHeight,
                                  expandedStates: _expandedStates,
                                  highlightedEntityId: highlightedEntityId,
                                ),
                              ),
                            ),

                            // Entity cards
                            ...tables.map((table) {
                              final cardKey = _entityCardKeys[table.id]!;
                              return Positioned(
                                left: table.position.dx,
                                top: table.position.dy,
                                child: GestureDetector(
                                  key: cardKey,
                                  onPanUpdate: (details) {
                                    setState(() {
                                      table.position += details.delta / _zoomLevel;
                                      if (selectedEntity == table.entity) {
                                        selectedEntityPosition = table.position;
                                      }
                                    });
                                  },
                                  onPanEnd: (_) {
                                    setState(() {
                                      table.position = snapToGrid(table.position, gridSize);
                                      if (selectedEntity == table.entity) {
                                        selectedEntityPosition = table.position;
                                      }
                                    });
                                  },
                                  child: SizedBox(
                                    width: cardWidth,
                                    child: EntityCard(
                                      entity: table.entity,
                                      isExpanded: _expandedStates[table.id] ?? false,
                                      onToggleExpand: (expanded) {
                                        setState(() {
                                          _expandedStates[table.id] = expanded;
                                          _layoutCards();
                                        });
                                      },
                                      onEntitySelected: () {
                                        setState(() {
                                          selectedEntity = table.entity;
                                          selectedEntityPosition = table.position;
                                          showDataCard = false;
                                          highlightedAttribute = null;
                                          highlightedEntityId = table.id;
                                        });
                                        _hideEntityProfileTooltip();
                                      },
                                      onInfoTap: () {
                                        setState(() {
                                          selectedEntity = table.entity;
                                          selectedEntityPosition = table.position;
                                          showDataCard = true;
                                          highlightedAttribute = null;
                                        });
                                      },
                                       onHeaderHover: (hovering) {
                                          final fullEntity = getEntityById(table.id);
                                          if (hovering && fullEntity != null) {
                                            _showEntityProfileTooltipForCard(cardKey, fullEntity);
                                          } else {
                                            _hideEntityProfileTooltip();
                                          }
                                        },
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),

                            // Data card on selection
                            if (selectedEntity != null &&
                                selectedEntityPosition != null &&
                                showDataCard)
                              _buildDataCardOverlay(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),

        // Zoom control
        Positioned(
          right: 24,
          top: MediaQuery.of(context).size.height / 2 - 80,
          child: _ZoomControl(
            minZoom: _minZoom,
            maxZoom: _maxZoom,
            zoomLevel: _zoomLevel,
            onZoomChanged: (newZoom) {
              setState(() {
                _zoomLevel = newZoom;
              });
            },
          ),
        ),
      ],
    ),
  );
}


Widget _buildDataCardOverlay() {
  if (selectedEntity == null || selectedEntityPosition == null || !showDataCard) {
    return const SizedBox.shrink();
  }

  double left = selectedEntityPosition!.dx + cardWidth + 24;
  double top = selectedEntityPosition!.dy;
  const double dataCardWidth = 420;
  const double dataCardHeight = 320;

  // Prevent data card from going outside canvas
  final canvasSize = _calculateCanvasSize();
  if (left + dataCardWidth > canvasSize.width) {
    left = selectedEntityPosition!.dx - dataCardWidth - 24;
  }
  if (top + dataCardHeight > canvasSize.height) {
    top = canvasSize.height - dataCardHeight - 24;
  }
  if (top < 0) top = 24;

  return Positioned(
    left: left,
    top: top,
    child: _EntityDataCard(
      entity: selectedEntity!,
      highlightedAttribute: highlightedAttribute,
      onClose: () {
        setState(() {
          selectedEntity = null;
          selectedEntityPosition = null;
          highlightedAttribute = null;
        });
      },
    ),
  );
}


double calculateCardHeight(Entity1 entity, bool isExpanded) {
  const double headerHeight = 48;
  const double verticalPadding = 12;
  const double rowHeight = 32;
  const int maxVisible = EntityCard.collapsedAttributeCount;
  final count = isExpanded ? entity.attributes.length : entity.attributes.take(maxVisible).length;
  return headerHeight + 2 * verticalPadding + (count * rowHeight);
}
  void _layoutCards() {
  double x = 40;
  double y = 40;
  const double spacingX = 420;
  const double spacingY = 30;
  int cardsPerColumn = 2;
  int count = 0;

  for (final table in tables) {
    final expanded = _expandedStates[table.id] ?? false;
    final height = calculateCardHeight(table.entity, expanded);
    table.position = Offset(x, y);
    y += height + spacingY;
    count++;
    if (count % cardsPerColumn == 0) {
      x += spacingX;
      y = 40;
    }
  }
}

}



// Grid painter for background grid
class _GridPainter extends CustomPainter {
  final double gridSize;

  _GridPainter({required this.gridSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.1)
      ..strokeWidth = 0.5;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _ERDEdgePainter extends CustomPainter {
  final List<ERDTable> tables;
  final List<ERDRelation> relations;
  final double cardWidth;
  final double cardHeight;
  final Map<String, bool> expandedStates;
  final String? highlightedEntityId;

  static const double headerHeight = 48;
  static const double attributeRowHeight = 32;
  static const double verticalPadding = 12;

  _ERDEdgePainter({
    required this.tables,
    required this.relations,
    required this.cardWidth,
    required this.cardHeight,
    required this.expandedStates,
    this.highlightedEntityId,
  });

  double getCardHeight(Entity1 entity) {
    final isExpanded = expandedStates[entity.name] ?? false;
    final visibleCount = isExpanded ? entity.attributes.length : 5;
    return headerHeight + visibleCount * attributeRowHeight + 2 * verticalPadding;
  }

  double getAttributeYOffset(Entity1 entity, String? attributeName) {
    final isExpanded = expandedStates[entity.name] ?? false;
    final idx = entity.attributes.indexWhere((a) => a.name == attributeName);

    if (idx == -1 || (!isExpanded && idx >= 5)) {
      return headerHeight + verticalPadding + (attributeRowHeight * 2);
    }

    return headerHeight + verticalPadding + idx * attributeRowHeight + attributeRowHeight / 2;
  }

  Offset getConnectionPoint(ERDTable table, double yOffset, Offset towards) {
    const double gap = 8.0;

    final centerX = table.position.dx + cardWidth / 2;
    final y = table.position.dy + yOffset;

    if (towards.dx > centerX) {
      return Offset(table.position.dx + cardWidth + gap, y);
    } else {
      return Offset(table.position.dx - gap, y);
    }
  }

  void drawCrowsFoot(Canvas canvas, Offset point, double angle, Color color, {double size = 14}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5;

    for (final offsetAngle in [-0.4, 0.0, 0.4]) {
      final dx = size * cos(angle + offsetAngle);
      final dy = size * sin(angle + offsetAngle);
      canvas.drawLine(point, point + Offset(dx, dy), paint);
    }
  }

  void drawOne(Canvas canvas, Offset point, double angle, Color color, {double size = 12}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5;

    final dx = size * cos(angle + pi / 2);
    final dy = size * sin(angle + pi / 2);
    canvas.drawLine(
      point - Offset(dx / 2, dy / 2),
      point + Offset(dx / 2, dy / 2),
      paint,
    );
  }

  void _drawCardinalityLabel(Canvas canvas, Offset position, String text) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    final bgRect = Rect.fromCenter(
      center: position,
      width: textPainter.width + 8,
      height: textPainter.height + 4,
    );

    final bgPaint = Paint()..color = Colors.white.withOpacity(0.9);
    canvas.drawRRect(
      RRect.fromRectAndRadius(bgRect, const Radius.circular(4)),
      bgPaint,
    );

    textPainter.paint(
      canvas,
      position - Offset(textPainter.width / 2, textPainter.height / 2),
    );
  }

  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint,
      {double dashLength = 6, double gapLength = 4}) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = sqrt(dx * dx + dy * dy);

    final direction = Offset(dx / distance, dy / distance);

    double traveled = 0;
    while (traveled < distance) {
      final currentStart = start + direction * traveled;
      traveled += dashLength;
      if (traveled > distance) traveled = distance;
      final currentEnd = start + direction * traveled;
      canvas.drawLine(currentStart, currentEnd, paint);
      traveled += gapLength;
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final tableMap = {for (var t in tables) t.id: t};

    for (final rel in relations) {
      final fromTable = tableMap[rel.from];
      final toTable = tableMap[rel.to];
      if (fromTable == null || toTable == null) continue;

      final isHighlighted = rel.from == highlightedEntityId || rel.to == highlightedEntityId;
      final Color lineColor = isHighlighted ? Colors.orange.shade700 : const Color(0xFF666666);

      final linePaint = Paint()
        ..color = lineColor
        ..strokeWidth = isHighlighted ? 1.5 : 1.5;

      if (rel.from == rel.to) {
        final yOffset = getAttributeYOffset(fromTable.entity, rel.fromAttribute);
        final start = Offset(fromTable.position.dx + cardWidth, fromTable.position.dy + yOffset);

        final path = Path();
        path.moveTo(start.dx, start.dy);
        path.relativeCubicTo(40, 0, 40, -40, 0, -40);
        path.relativeCubicTo(-40, 0, -40, 40, 0, 40);
        canvas.drawPath(path, linePaint);

        final labelPos = start + const Offset(30, -20);
        _drawCardinalityLabel(canvas, labelPos, rel.fromCardinality);
        continue;
      }

      final fromYOffset = getAttributeYOffset(fromTable.entity, rel.fromAttribute);
      final toYOffset = getAttributeYOffset(toTable.entity, rel.toAttribute);

      final fromCenter = Offset(
        fromTable.position.dx + cardWidth / 2,
        fromTable.position.dy + getCardHeight(fromTable.entity) / 2,
      );

      final toCenter = Offset(
        toTable.position.dx + cardWidth / 2,
        toTable.position.dy + getCardHeight(toTable.entity) / 2,
      );

      final fromPoint = getConnectionPoint(fromTable, fromYOffset, toCenter);
      final toPoint = getConnectionPoint(toTable, toYOffset, fromCenter);

      _drawDashedLine(canvas, fromPoint, toPoint, linePaint);

      final dx = toPoint.dx - fromPoint.dx;
      final dy = toPoint.dy - fromPoint.dy;
      final angle = atan2(dy, dx);
      const double symbolOffset = 2;

      final fromSymbolPoint = fromPoint + Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset);
      if (rel.fromCardinality == 'N') {
        drawCrowsFoot(canvas, fromSymbolPoint, angle, lineColor);
      } else if (rel.fromCardinality == '1') {
        drawOne(canvas, fromSymbolPoint, angle, lineColor);
      }

      final toSymbolPoint = toPoint - Offset(cos(angle) * symbolOffset, sin(angle) * symbolOffset);
      if (rel.toCardinality == 'N') {
        drawCrowsFoot(canvas, toSymbolPoint, angle + pi, lineColor);
      } else if (rel.toCardinality == '1') {
        drawOne(canvas, toSymbolPoint, angle + pi, lineColor);
      }
    }
  }

  @override
  bool shouldRepaint(covariant _ERDEdgePainter oldDelegate) {
    return oldDelegate.highlightedEntityId != highlightedEntityId ||
        oldDelegate.tables != tables ||
        oldDelegate.relations != relations ||
        oldDelegate.expandedStates != expandedStates;
  }
}




// Rest of the classes remain the same...
class _ZoomControl extends StatelessWidget {
  final double minZoom;
  final double maxZoom;
  final double zoomLevel;
  final ValueChanged<double> onZoomChanged;

  const _ZoomControl({
    required this.minZoom,
    required this.maxZoom,
    required this.zoomLevel,
    required this.onZoomChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black, width: 1.5),
            ),
            child: const Center(
              child: Icon(Icons.add, size: 20),
            ),
          ).asButton(() {
            final newZoom = (zoomLevel + 0.1).clamp(minZoom, maxZoom);
            onZoomChanged(newZoom);
          }),
          
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 2,
            height: 100,
            color: Colors.black,
            alignment: Alignment.center,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  left: -9,
                  top: 100 * (1 - (zoomLevel - minZoom) / (maxZoom - minZoom)) - 9,
                  child: GestureDetector(
                    onVerticalDragUpdate: (details) {
                      final lineHeight = 100;
                      final yPos = 1.0 - (details.localPosition.dy / lineHeight).clamp(0.0, 1.0);
                      final newZoom = minZoom + yPos * (maxZoom - minZoom);
                      onZoomChanged(newZoom);
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.lightGreen.shade200,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black, width: 1.5),
            ),
            child: const Center(
              child: Icon(Icons.remove, size: 20),
            ),
          ).asButton(() {
            final newZoom = (zoomLevel - 0.1).clamp(minZoom, maxZoom);
            onZoomChanged(newZoom);
          }),
        ],
      ),
    );
  }
}

extension WidgetExtension on Widget {
  Widget asButton(VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: this,
    );
  }
}

class CrowFootLegendBox extends StatelessWidget {
  const CrowFootLegendBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Crow\'s Foot Notation', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            _LegendRow(symbol: _OneSymbol(), label: 'One (|)'),
            _LegendRow(symbol: _CrowsFootSymbol(), label: 'Many (crow\'s foot)'),
          ],
        ),
      ),
    );
  }
}

class _LegendRow extends StatelessWidget {
  final Widget symbol;
  final String label;
  const _LegendRow({required this.symbol, required this.label});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(width: 28, height: 18, child: symbol),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }
}

class _OneSymbol extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _OneSymbolPainter(),
    );
  }
}

class _OneSymbolPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    canvas.drawLine(Offset(size.width / 2, 2), Offset(size.width / 2, size.height - 2), paint);
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _CrowsFootSymbol extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CrowsFootSymbolPainter(),
    );
  }
}

class _CrowsFootSymbolPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    final center = Offset(size.width / 2, size.height / 2);
    for (final offsetAngle in [-0.5, 0.0, 0.5]) {
      final dx = 10 * cos(offsetAngle);
      final dy = 10 * sin(offsetAngle);
      canvas.drawLine(center, center + Offset(dx, dy), paint);
    }
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _EntityDataCard extends StatelessWidget {
  final Entity1 entity;
  final String? highlightedAttribute;
  final VoidCallback onClose;

  const _EntityDataCard({
    required this.entity,
    this.highlightedAttribute,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${entity.name} Data',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            entity.attributes.isEmpty
                ? const Text('No data available.')
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: [
                        for (final attr in entity.attributes)
                          DataColumn(
                            label: Text(
                              attr.name,
                              style: TextStyle(
                                fontWeight: highlightedAttribute == attr.name ? FontWeight.bold : FontWeight.normal,
                                color: highlightedAttribute == attr.name ? Colors.blue : null,
                              ),
                            ),
                          ),
                      ],
                      rows: [
                        // for (final row in entity.attributes)
                          DataRow(
                            cells: [
                              for (final attr in entity.attributes)
                                DataCell(Text(attr.name.toString())),
                            ],
                          ),
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }
} 


// Updated EntityCard widget to match the modern card design from Image 2
class EntityCard extends StatefulWidget {
  final Entity1 entity;
  final VoidCallback? onEntitySelected;
  final VoidCallback? onInfoTap;
  final bool isSelected;
  final bool isExpanded;
  final ValueChanged<bool>? onToggleExpand;
  final ValueChanged<bool>? onHeaderHover; // NEW

  static const int collapsedAttributeCount = 5;

  const EntityCard({
    super.key,
    required this.entity,
    this.onEntitySelected,
    this.onInfoTap,
    this.isSelected = false,
    this.isExpanded = false,
    this.onToggleExpand,
    this.onHeaderHover, // NEW
  });

  @override
  State<EntityCard> createState() => _EntityCardState();
}

class _EntityCardState extends State<EntityCard> {
  @override
  Widget build(BuildContext context) {
    final visibleAttributes = widget.isExpanded
        ? widget.entity.attributes
        : widget.entity.attributes.take(EntityCard.collapsedAttributeCount).toList();

    return GestureDetector(
      onTap: widget.onEntitySelected,
      child: Container(
        width: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: widget.isSelected
              ? Border.all(color: const Color(0xFF2196F3), width: 2)
              : Border.all(color: Colors.grey.shade200, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with Tooltip & MouseRegion ONLY on entity.name text
            SizedBox(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: MouseRegion(
                        onEnter: (_) => widget.onHeaderHover?.call(true),
                        onExit: (_) => widget.onHeaderHover?.call(false),
                        child: Text(
                          widget.entity.name,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                    ),
                  ),
                  StaticToggleMenu(),
                ],
              ),
            ),

            // Attributes
            Column(
              children: visibleAttributes.asMap().entries.map((entry) {
                final index = entry.key;
                final attr = entry.value;
                final isLast = index == visibleAttributes.length - 1;

                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 6),
                  decoration: BoxDecoration(
                    border: isLast
                        ? null
                        : Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade200,
                              width: 1,
                            ),
                          ),
                  ),
                  child: Row(
  children: [
    Flexible(
      flex: 3,
      child:Row(
  children: [
    Expanded(
      child: RichText(
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        text: TextSpan(
          children: [
            TextSpan(
              text: attr.name,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
            if (attr.isPrimaryKey == true)
              WidgetSpan(
                alignment: PlaceholderAlignment.top,
                child: Transform.translate(
                  offset: Offset(0, -4), // 👈 adjust vertical offset here
                  child: Text(
                    '^PK',
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                      color: Color(0xff0058FF),
                      fontFamily: 'TiemposText',
                    ),
                  ),
                ),
              )
            else if (attr.isForeignKey == true)
              WidgetSpan(
                alignment: PlaceholderAlignment.top,
                child: Transform.translate(
                  offset: Offset(0, -4),
                  child: Text(
                    '^FK',
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                      color: Color(0xff0058FF),
                      fontFamily: 'TiemposText',
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    ),
  ],
),

    ),
    Flexible(
      flex: 2,
      child: Align(
        alignment: Alignment.centerRight,
        child: Text(
          attr.type,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
      ),
    ),
  ],
),

                );
              }).toList(),
            ),

            // Show more / less
            if (widget.entity.attributes.length > EntityCard.collapsedAttributeCount)
              TextButton(
                onPressed: () => widget.onToggleExpand?.call(!widget.isExpanded),
                child: Text(
                  widget.isExpanded ? 'Show Less' : 'Show More',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                ),
              ),
          ],
        ),
      ),
    );
  }
}






class Attribute {
  final String name;
  final String type;
  final bool isPrimaryKey;
  final bool isForeignKey;
  final String? referencesEntity;

  const Attribute({
    required this.name,
    required this.type,
    this.isPrimaryKey = false,
    this.isForeignKey = false,
    this.referencesEntity,
  });

  /// Convert Attribute to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'isPrimaryKey': isPrimaryKey,
      'isForeignKey': isForeignKey,
      if (referencesEntity != null) 'referencesEntity': referencesEntity,
    };
  }

  /// Create Attribute from JSON
  factory Attribute.fromJson(Map<String, dynamic> json) {
    return Attribute(
      name: json['name'] as String,
      type: json['type'] as String,
      isPrimaryKey: json['isPrimaryKey'] as bool? ?? false,
      isForeignKey: json['isForeignKey'] as bool? ?? false,
      referencesEntity: json['referencesEntity'] as String?,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Attribute &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          type == other.type &&
          isPrimaryKey == other.isPrimaryKey &&
          isForeignKey == other.isForeignKey &&
          referencesEntity == other.referencesEntity;

  @override
  int get hashCode => Object.hash(
        name,
        type,
        isPrimaryKey,
        isForeignKey,
        referencesEntity,
      );
}

/// Represents an entity in the ERD
class Entity1 {
  final String name;
  final List<Attribute> attributes;
  final List<Map<String, dynamic>> data;

  const Entity1({
    required this.name,
    required this.attributes,
    this.data = const [],
  });

  /// Convert Entity to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'attributes': attributes.map((attr) => attr.toJson()).toList(),
      if (data.isNotEmpty) 'data': data,
    };
  }

  /// Create Entity from JSON
  factory Entity1.fromJson(Map<String, dynamic> json) {
    return Entity1(
      name: json['name'] as String,
      attributes: (json['attributes'] as List)
          .map((attr) => Attribute.fromJson(attr as Map<String, dynamic>))
          .toList(),
      data: (json['data'] as List?)?.map((row) => Map<String, dynamic>.from(row as Map)).toList() ?? [],
    );
  }

  /// Convert Entity to JSON string
  String toJsonString() => jsonEncode(toJson());

  /// Create Entity from JSON string
  factory Entity1.fromJsonString(String jsonString) {
    return Entity1.fromJson(jsonDecode(jsonString) as Map<String, dynamic>);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Entity1 &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          attributes == other.attributes &&
          data == other.data;

  @override
  int get hashCode => Object.hash(name, attributes, data);
} 