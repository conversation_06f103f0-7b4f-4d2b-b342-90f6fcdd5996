import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

// Import your app files (adjust paths as needed)

class HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;
  final int? count;
  final double? width;

  const HoverNavItem({
    super.key,
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
    this.count,
    this.width,
  });

  @override
  State<HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: widget.width ?? 120,
          height: 34,
          decoration: BoxDecoration(
            color: widget.isActive
                ? const Color(0xff0058FF)
                : (isHovered ? const Color(0xffF0F0F0) : Colors.transparent),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  widget.iconPath,
                  width: 14,
                  height: 14,
                  colorFilter: ColorFilter.mode(
                    widget.isActive ? Colors.white : Colors.black87,
                    BlendMode.srcIn,
                  ),
                ),
                const SizedBox(width: 4),
                Text.rich(
                  TextSpan(
                    children: [
                      if (widget.count != null) ...[
                        TextSpan(
                          text: '${widget.count} ',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            color:
                                widget.isActive ? Colors.white : Colors.black87,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontWeight: FontManager.medium,
                          ),
                        ),
                      ],
                      TextSpan(
                        text: widget.label,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          color:
                              widget.isActive ? Colors.white : Colors.black87,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontWeight: FontManager.medium,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class HoverNavItems extends StatelessWidget {
  const HoverNavItems({super.key});

  @override
  Widget build(BuildContext context) {
    final currentIndex =
        Provider.of<WebHomeProvider>(context).currentScreenIndex;

    // Make Books active by default if no other specific screen is selected
    final isBooksActive = currentIndex == ScreenConstants.webMyLibrary ||
        (currentIndex != ScreenConstants.webMyProjects &&
            currentIndex != ScreenConstants.webMySolution &&
            currentIndex != ScreenConstants.webMyObject &&
            currentIndex != ScreenConstants.webAgentScreen);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate button width: (available width - spacing between items) / number of buttons
                // Total spacing = 4 gaps * 16px = 64px (between 5 buttons)
                final double buttonWidth = (580 - 64) / 5;

                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    HoverNavItem(
                      iconPath: 'assets/images/books-icon.svg',
                      label: 'Projects',
                      count: 12,
                      width: buttonWidth,
                      isActive: currentIndex == ScreenConstants.webMyProjects,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyProjects;
                      },
                    ),
                    const SizedBox(width: 16),
                    HoverNavItem(
                      iconPath: 'assets/images/books-icon.svg',
                      label: 'Books',
                      count: 12,
                      width: buttonWidth,
                      // isActive: isBooksActive,
                      isActive: currentIndex == ScreenConstants.webMyLibrary,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyLibrary;
                      },
                    ),
                    const SizedBox(width: 16),
                    HoverNavItem(
                      iconPath: 'assets/images/square-box-uncheck.svg',
                      label: 'Solutions',
                      count: 35,
                      width: buttonWidth,
                      isActive: currentIndex == ScreenConstants.webMySolution,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMySolution;
                      },
                    ),
                    const SizedBox(width: 16),
                    HoverNavItem(
                      iconPath: 'assets/images/cube-box.svg',
                      label: 'Objects',
                      count: 102,
                      width: buttonWidth,
                      isActive: currentIndex == ScreenConstants.webMyObject || currentIndex == ScreenConstants.createObjectScreenStatic ||  currentIndex == ScreenConstants.aiObjectScreenStatic,
                      // isActive: true,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyObject;
                      },
                    ),
                    const SizedBox(width: 16),
                    HoverNavItem(
                      iconPath: 'assets/images/agent-icon.svg',
                      label: 'Agents',
                      count: 10,
                      width: buttonWidth,
                      isActive: currentIndex == ScreenConstants.webAgentScreen,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                                .currentScreenIndex =
                            ScreenConstants.webAgentScreen;
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
