import 'dart:convert';

/// Utility class for parsing the new structured response format
class ResponseTextParser {
  /// Main parsing method for the new response structure
  static Map<String, dynamic> parseNewResponseStructure(String responseText) {
    try {
      // Clean the response text by removing escape characters and extra quotes
      String cleanedText =
          responseText.replaceAll('\\"', '"').replaceAll('\\n', '\n').trim();

      // If the text starts and ends with quotes, remove them
      if (cleanedText.startsWith('"') && cleanedText.endsWith('"')) {
        cleanedText = cleanedText.substring(1, cleanedText.length - 1);
      }

      Map<String, dynamic> result = {
        'thinking': [],
        'output1_text': '',
        'selection_options': [],
        'output2_json': null,
        'raw_text': cleanedText,
        'has_thinking': false,
        'has_selection_options': false,
        'has_output2': false,
      };

      // Extract <thinking> section
      result['thinking'] = _extractThinkingSection(cleanedText);
      result['has_thinking'] = result['thinking'].isNotEmpty;

      // Extract OUTPUT 1 section
      result['output1_text'] = _extractOutput1Section(cleanedText);

      // Extract SELECTION_OPTIONS from OUTPUT 1 (before cleaning)
      String rawOutput1 = _extractRawOutput1Section(cleanedText);
      result['selection_options'] = extractSelectionOptions(rawOutput1);
      result['has_selection_options'] = result['selection_options'].isNotEmpty;

      // Extract OUTPUT 2 JSON
      result['output2_json'] = _extractOutput2Json(cleanedText);
      result['has_output2'] = result['output2_json'] != null;

      return result;
    } catch (e) {
      return {
        'error': 'Failed to parse response: $e',
        'thinking': [],
        'output1_text': '',
        'selection_options': [],
        'output2_json': null,
        'raw_text': responseText,
        'has_thinking': false,
        'has_selection_options': false,
        'has_output2': false,
      };
    }
  }

  static String extractThinkingSectionAsString(String text) {
    if (text.contains("<thinking>") && text.contains("</thinking>")) {
      List x = text.split("</thinking>");
      String temp = x[0];
      temp = temp.split("<thinking>").length > 1
          ? temp.split("<thinking>")[1]
          : temp;
      return temp;
    } else {
      return '';
    }

    // // Find thinking section
    // RegExp thinkingPattern =
    //     RegExp(r'<thinking>(.*?)</thinking>', dotAll: true);
    // Match? thinkingMatch = thinkingPattern.firstMatch(text);

    // if (thinkingMatch != null) {
    //   // String thinkingContent = thinkingMatch.group(1)?.trim() ?? '';
    //   return text.replaceAll(thinkingPattern, '');
    // } else {
    //   return '';
    // }
  }

  /// Extracts the thinking section and returns numbered items as list
  static List<String> _extractThinkingSection(String text) {
    List<String> thinkingItems = [];

    // Find thinking section
    RegExp thinkingPattern =
        RegExp(r'<thinking>(.*?)</thinking>', dotAll: true);
    Match? thinkingMatch = thinkingPattern.firstMatch(text);

    if (thinkingMatch != null) {
      String thinkingContent = thinkingMatch.group(1)?.trim() ?? '';

      // Check if thinking contains numbered list
      List<String> lines = thinkingContent.split('\n');
      for (String line in lines) {
        String trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;

        // Check for numbered items (1. item, 2. item, etc.)
        RegExp numberedPattern = RegExp(r'^(\d+)\.\s*(.+)$');
        Match? numberedMatch = numberedPattern.firstMatch(trimmedLine);

        if (numberedMatch != null) {
          // Include the number in the returned string
          String fullItem =
              '${numberedMatch.group(1)}. ${numberedMatch.group(2)?.trim() ?? ''}';
          thinkingItems.add(fullItem);
        } else {
          // If it's not numbered but we have content, add it as is
          if (trimmedLine.isNotEmpty) {
            thinkingItems.add(trimmedLine);
          }
        }
      }
    }

    return thinkingItems;
  }

  /// Extracts raw OUTPUT 1 section text (for selection options extraction)
  static String _extractRawOutput1Section(String text) {
    // Find OUTPUT 1 start
    RegExp output1Pattern =
        RegExp(r'\*\*OUTPUT 1[^*]*\*\*', caseSensitive: false);
    Match? output1Match = output1Pattern.firstMatch(text);

    if (output1Match == null) {
      return '';
    }

    int output1Start = output1Match.end;

    // Find OUTPUT 2 start
    RegExp output2Pattern =
        RegExp(r'\*\*OUTPUT 2[^*]*\*\*', caseSensitive: false);
    Match? output2Match = output2Pattern.firstMatch(text);

    int output1End = output2Match?.start ?? text.length;

    // Extract text between OUTPUT 1 and OUTPUT 2 without cleaning
    String output1Text = text.substring(output1Start, output1End).trim();

    return output1Text;
  }

  /// Extracts OUTPUT 1 section text (cleaned for user display)
  static String _extractOutput1Section(String text) {
    // Get raw OUTPUT 1 content
    String output1Text = _extractRawOutput1Section(text);

    // Remove SELECTION_OPTIONS section from user content
    output1Text = _removeSelectionOptionsSection(output1Text);

    // Clean up but preserve line breaks and formatting
    output1Text = _cleanTextPreservingFormat(output1Text);

    return output1Text;
  }

  /// Removes SELECTION_OPTIONS section from text (removes the section header and everything after)
  static String _removeSelectionOptionsSection(String text) {
    final lines = text.split('\n');
    final cleanedLines = <String>[];

    bool insideOptionsBlock = false;

    for (var line in lines) {
      final trimmed = line.trim();

      // Detect start of SELECTION_OPTIONS
      if (RegExp(r'(\*\*)?SELECTION_OPTIONS(\*\*)?:?', caseSensitive: false)
          .hasMatch(trimmed)) {
        insideOptionsBlock = true;
        break; // Stop processing further lines completely
      }

      if (!insideOptionsBlock) {
        cleanedLines.add(line);
      }
    }

    return cleanedLines.join('\n').trim();
  }

  /// Cleans text while preserving formatting and line breaks, removes \1 lines
  static String _cleanTextPreservingFormat(String text) {
    // Split into lines first
    List<String> lines = text.split('\n');
    List<String> cleanedLines = [];

    for (String line in lines) {
      String trimmedLine = line.trim();

      // Skip lines that contain only \1 or start with \1
      if (trimmedLine == '\\1' || trimmedLine.startsWith('\\1')) {
        continue;
      }

      // Remove bold formatting but preserve structure
      String cleanedLine = line.replaceAll(RegExp(r'\*\*([^*]+)\*\*'), '');

      // Clean up excessive spaces on the line
      cleanedLine = cleanedLine.replaceAll(RegExp(r'[ \t]+'), ' ').trim();

      // Add non-empty lines
      if (cleanedLine.isNotEmpty) {
        cleanedLines.add(cleanedLine);
      }
    }

    // Join lines back
    String result = cleanedLines.join('\n');

    // Remove excessive blank lines (more than 2 consecutive)
    result = result.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    return result.trim();
  }

  /// Extracts valid selection options from the text
  static List<String> extractSelectionOptions(String text) {
    final options = <String>[];

    final selectionRegex =
        RegExp(r'\bSELECTION_OPTIONS\b[:\*_\s]*', caseSensitive: false);
    final match = selectionRegex.firstMatch(text);
    if (match == null) return options;

    final postOptions = text.substring(match.end);
    for (final rawLine in postOptions.split('\n')) {
      final line = rawLine.trim();
      if (line.isEmpty) continue;

      if (line.startsWith('**') ||
          line.contains('OUTPUT') ||
          line.startsWith('```')) {
        break;
      }
      if (line.endsWith('?')) break;

      final m = RegExp(r'^(?:□|\[.\]|[-•*]|\d+\.)\s+(.+)$').firstMatch(line) ??
          RegExp(r'^[A-Za-z]\)\s+(.+)$').firstMatch(line);

      if (m != null && m.group(1) != null) {
        options.add(m.group(1)!.trim());
      }
    }

    return options;
  }

  /// Extracts question that appears inside the SELECTION_OPTIONS block (before any options)
  static String extractQuestionInsideSelectionOptions(String text) {
    final selectionRegex =
        RegExp(r'\bSELECTION_OPTIONS\b[:\*_\s]*', caseSensitive: false);
    final match = selectionRegex.firstMatch(text);
    if (match == null) return '';

    for (final rawLine in text.substring(match.end).split('\n')) {
      final line = rawLine.trim();
      if (line.isEmpty) continue;
      if (_isOptionLine(line)) break;
      if (line.endsWith('?')) return line;
    }
    return '';
  }

  /// Extracts question that appears immediately after the options block
  static String extractQuestionAfterSelectionOptions(String text) {
    final selectionRegex =
        RegExp(r'\bSELECTION_OPTIONS\b[:\*_\s]*', caseSensitive: false);
    final match = selectionRegex.firstMatch(text);
    if (match == null) return '';

    final lines =
        text.substring(match.end).split('\n').map((l) => l.trim()).toList();
    var idx = 0;

    // Skip until we hit first option
    while (idx < lines.length && !_isOptionLine(lines[idx])) idx++;

    // Skip the option block
    while (idx < lines.length && _isOptionLine(lines[idx])) idx++;
    // Skip blank lines
    while (idx < lines.length && lines[idx].isEmpty) idx++;

    // First question after options is our answer
    for (; idx < lines.length; idx++) {
      if (lines[idx].endsWith('?')) return lines[idx];
    }

    return '';
  }

  static bool _isOptionLine(String line) {
    return RegExp(r'^(?:□|\[.\]|[-•*]|\d+\.)\s+').hasMatch(line) ||
        RegExp(r'^[A-Za-z]\)\s+').hasMatch(line);
  }

  // /// Extracts the most relevant question from SELECTION_OPTIONS or fallback
  // static String extractLastQuestion(String text) {
  //   final questionRegex = RegExp(
  //     r'^(.*?(What|Which|How|Why|Do|Does|Can|Should|Would|Is|Are|When|Where)\b.+\?)$',
  //     caseSensitive: false,
  //   );

  //   // First: Search inside SELECTION_OPTIONS
  //   final selectionRegex =
  //       RegExp(r'\*\*?SELECTION_OPTIONS\*\*?:?', caseSensitive: false);
  //   final selectionMatch = selectionRegex.firstMatch(text);
  //   if (selectionMatch != null) {
  //     final postOptionsText = text.substring(selectionMatch.end);
  //     final lines = postOptionsText.split('\n').map((l) => l.trim()).toList();

  //     for (final line in lines) {
  //       if (line.isEmpty) continue;

  //       // Question?
  //       if (line.endsWith('?')) return line;

  //       final qMatch = questionRegex.firstMatch(line);
  //       if (qMatch != null) return qMatch.group(1)!.trim();

  //       // Stop if section ends
  //       if (line.startsWith('**') ||
  //           line.contains('OUTPUT') ||
  //           line.startsWith('```')) break;
  //     }
  //   }

  //   // Fallback: Scan OUTPUT 1 section
  //   final output1Text = _extractRawOutput1Section(text);
  //   final lines = output1Text.split('\n').map((l) => l.trim()).toList();

  //   for (int i = lines.length - 1; i >= 0; i--) {
  //     final line = lines[i];
  //     if (line.isEmpty) continue;

  //     if (line.endsWith('?')) return line;

  //     final qMatch = questionRegex.firstMatch(line);
  //     if (qMatch != null) return qMatch.group(1)!.trim();
  //   }

  //   return '';
  // }

  /// Optional helper if you still want a fallback full scanner
  static String extractFallbackQuestionFromOutput1(String text) {
    final output1Text = _extractRawOutput1Section(text);
    final lines = output1Text.split('\n').map((l) => l.trim()).toList();

    for (int i = lines.length - 1; i >= 0; i--) {
      if (lines[i].endsWith('?')) return lines[i];
    }

    return '';
  }

  /// Extracts OUTPUT 2 JSON section
  static Map<String, dynamic>? _extractOutput2Json(String text) {
    try {
      // Find OUTPUT 2 section
      RegExp output2Pattern =
          RegExp(r'\*\*OUTPUT 2[^*]*\*\*', caseSensitive: false);
      Match? output2Match = output2Pattern.firstMatch(text);

      if (output2Match == null) {
        return null;
      }

      // Get text after OUTPUT 2
      String output2Text = text.substring(output2Match.end);

      // Look for JSON code block
      RegExp jsonBlockPattern =
          RegExp(r'```[^`]*?(\{.*?\})[^`]*?```', dotAll: true);
      Match? jsonBlockMatch = jsonBlockPattern.firstMatch(output2Text);

      if (jsonBlockMatch != null) {
        String jsonString = jsonBlockMatch.group(1) ?? '';
        return jsonDecode(jsonString);
      }

      // If no code block, look for direct JSON
      RegExp directJsonPattern = RegExp(r'(\{.*?\})', dotAll: true);
      Match? directJsonMatch = directJsonPattern.firstMatch(output2Text);

      if (directJsonMatch != null) {
        String jsonString = directJsonMatch.group(1) ?? '';
        return jsonDecode(jsonString);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Gets the user display text (OUTPUT 1 content)
  static String getUserDisplayText(Map<String, dynamic> parsedData) {
    return parsedData['output1_text'] ?? '';
  }

  /// Gets the thinking items as list
  static List<String> getThinkingItems(Map<String, dynamic> parsedData) {
    return List<String>.from(parsedData['thinking'] ?? []);
  }

  /// Gets the selection options as list
  static List<String> getSelectionOptions(Map<String, dynamic> parsedData) {
    return List<String>.from(parsedData['selection_options'] ?? []);
  }

  /// Gets the OUTPUT 2 JSON data
  static Map<String, dynamic>? getOutput2Json(Map<String, dynamic> parsedData) {
    return parsedData['output2_json'];
  }

  /// Checks if response has thinking section
  static bool hasThinking(Map<String, dynamic> parsedData) {
    return parsedData['has_thinking'] ?? false;
  }

  /// Checks if response has selection options
  static bool hasSelectionOptions(Map<String, dynamic> parsedData) {
    return parsedData['has_selection_options'] ?? false;
  }

  /// Checks if response has OUTPUT 2 JSON
  static bool hasOutput2Json(Map<String, dynamic> parsedData) {
    return parsedData['has_output2'] ?? false;
  }

  /// Creates a summary of the parsed response
  static Map<String, dynamic> createSummary(Map<String, dynamic> parsedData) {
    return {
      'has_thinking': hasThinking(parsedData),
      'thinking_items_count': getThinkingItems(parsedData).length,
      'has_selection_options': hasSelectionOptions(parsedData),
      'selection_options_count': getSelectionOptions(parsedData).length,
      'has_output2_json': hasOutput2Json(parsedData),
      'output1_text_length': getUserDisplayText(parsedData).length,
      'has_error': parsedData.containsKey('error'),
    };
  }

  /// Formats the parsed data for debugging
  static String formatForDebugging(Map<String, dynamic> parsedData) {
    StringBuffer buffer = StringBuffer();

    if (parsedData.containsKey('error')) {
      buffer.writeln('❌ ERROR: ${parsedData['error']}');
      return buffer.toString();
    }

    buffer.writeln('=== PARSED RESPONSE ANALYSIS ===\n');

    // Thinking section
    if (hasThinking(parsedData)) {
      buffer.writeln('🧠 THINKING ITEMS:');
      List<String> thinkingItems = getThinkingItems(parsedData);
      for (int i = 0; i < thinkingItems.length; i++) {
        buffer.writeln('   ${i + 1}. ${thinkingItems[i]}');
      }
      buffer.writeln();
    }

    // Output 1 text
    buffer.writeln('📄 OUTPUT 1 TEXT:');
    buffer
        .writeln('Length: ${getUserDisplayText(parsedData).length} characters');
    String preview = getUserDisplayText(parsedData);
    int previewLength = preview.length > 100 ? 100 : preview.length;
    buffer.writeln('Preview: ${preview.substring(0, previewLength)}...');
    buffer.writeln();

    // Selection options
    if (hasSelectionOptions(parsedData)) {
      buffer.writeln('☑️ SELECTION OPTIONS:');
      List<String> options = getSelectionOptions(parsedData);
      for (int i = 0; i < options.length; i++) {
        buffer.writeln('   ${i + 1}. ${options[i]}');
      }
      buffer.writeln();
    }

    // Output 2 JSON
    if (hasOutput2Json(parsedData)) {
      buffer.writeln('📊 OUTPUT 2 JSON:');
      Map<String, dynamic>? jsonData = getOutput2Json(parsedData);
      buffer.writeln('Keys: ${jsonData?.keys.join(', ')}');
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// Converts parsed data to JSON string
  static String toJson(Map<String, dynamic> parsedData) {
    try {
      return JsonEncoder.withIndent('  ').convert(parsedData);
    } catch (e) {
      return '{"error": "Failed to convert to JSON: $e"}';
    }
  }

  /// Quick parse method that returns just the essential parts
  static Map<String, dynamic> quickParse(String responseText) {
    Map<String, dynamic> fullParsed = parseNewResponseStructure(responseText);

    return {
      'user_text': getUserDisplayText(fullParsed),
      'thinking': getThinkingItems(fullParsed),
      'options': getSelectionOptions(fullParsed),
      'json_data': getOutput2Json(fullParsed),
      'summary': createSummary(fullParsed),
    };
  }

  /// Extracts only the user-facing content with preserved formatting
  static String extractUserContent(String responseText) {
    Map<String, dynamic> parsed = parseNewResponseStructure(responseText);
    return getUserDisplayText(parsed);
  }

  /// Extracts only the selection options
  static List<String> extractSelectionOptionsOnly(String responseText) {
    Map<String, dynamic> parsed = parseNewResponseStructure(responseText);
    return getSelectionOptions(parsed);
  }

  /// Extracts only the thinking items
  static List<String> extractThinkingOnly(String responseText) {
    Map<String, dynamic> parsed = parseNewResponseStructure(responseText);
    return getThinkingItems(parsed);
  }

  /// Extracts final question (last ?-ending line after SELECTION_OPTIONS)
  static String extractTrailingQuestion(String text) {
    // Grab the portion after SELECTION_OPTIONS
    RegExp selectionPattern =
        RegExp(r'\*\*SELECTION_OPTIONS:\*\*', caseSensitive: false);
    Match? selectionMatch = selectionPattern.firstMatch(text);

    if (selectionMatch != null) {
      String afterOptions = text.substring(selectionMatch.end);

      // Look for last ?-ending sentence after SELECTION_OPTIONS
      final lines =
          afterOptions.split('\n').map((line) => line.trim()).toList();
      for (int i = lines.length - 1; i >= 0; i--) {
        if (lines[i].endsWith('?')) {
          return lines[i];
        }
      }
    }

    return '';
  }

  static Map<String, dynamic> extractAllFromGlobalSummary(String aiResponse) {
    final Map<String, dynamic> result = {};

    // Step 1: Try to find a labeled "GLOBAL SUMMARY UPDATE" followed by a JSON block
    final summaryRegex = RegExp(
      r'(?:GLOBAL_SUMMARY_UPDATE|GLOBAL SUMMARY UPDATE)[\s\S]*?```json\s*([\s\S]+?)\s*```',
      caseSensitive: false,
    );

    Match? match = summaryRegex.firstMatch(aiResponse);
    String? jsonString;

    if (match != null) {
      jsonString = match.group(1)?.trim();
    } else {
      // Step 2: Fallback to the first JSON block anywhere
      final fallbackRegex =
          RegExp(r'```json\s*([\s\S]+?)\s*```', caseSensitive: false);
      final fallbackMatch = fallbackRegex.firstMatch(aiResponse);
      if (fallbackMatch != null) {
        jsonString = fallbackMatch.group(1)?.trim();
      }
    }

    if (jsonString == null) return {};

    try {
      final parsedJson = jsonDecode(jsonString) as Map<String, dynamic>;

      // Clean out keys like 'completion_level' and 'last_updated'
      parsedJson.forEach((sectionKey, sectionValue) {
        if (sectionValue is Map<String, dynamic>) {
          final cleanedSection = Map<String, dynamic>.from(sectionValue)
            ..remove('completion_level')
            ..remove('last_updated');
          result[sectionKey] = cleanedSection;
        } else {
          result[sectionKey] = sectionValue;
        }
      });

      return result;
    } catch (e) {
      print('Error parsing GLOBAL_SUMMARY_UPDATE: $e');
      return {};
    }
  }
}

class AiResponseParser {
  /// Parses a full AI response into sectioned map
  static Map<String, dynamic> parseSections(String text) {
    final Map<String, dynamic> sectionMap = {};
    String? currentSectionName;
    final StringBuffer currentContent = StringBuffer();

    if (text.contains("<thinking>") && text.contains("</thinking>")) {
      text = text.split("</thinking>").last.trim();
    }

    // 🧹 Remove summary before parsing
    text = _removeAllGlobalSummaryBlocks(text);

    final lines = LineSplitter.split(text);

    bool insideCodeBlock = false;
    final StringBuffer codeBuffer = StringBuffer();

    void _commitSection() {
      final contentStr = currentContent.toString().trim();
      if (contentStr.isEmpty) return;

      final key = currentSectionName?.trim().toLowerCase() ?? 'intro';

      if (_isJsonLike(contentStr)) {
        try {
          sectionMap[key] = json.decode(contentStr);
        } catch (_) {
          sectionMap[key] = contentStr;
        }
      } else {
        sectionMap[key] = contentStr;
      }

      currentContent.clear();
      currentSectionName = null;
    }

    for (var line in lines) {
      line = line.trim();
      if (line.isEmpty) continue;

      if (line.startsWith('```')) {
        insideCodeBlock = !insideCodeBlock;
        if (!insideCodeBlock) {
          currentContent.writeln(codeBuffer.toString());
          codeBuffer.clear();
        }
        continue;
      }

      if (insideCodeBlock) {
        codeBuffer.writeln(line);
        continue;
      }

      final sectionMatch = RegExp(r'^\*\*(?:OUTPUT\s*\d+:)?\s*(.+?)\s*\*\*$',
              caseSensitive: false)
          .firstMatch(line);

      if (sectionMatch != null) {
        _commitSection();
        currentSectionName = sectionMatch.group(1)?.trim();
        continue;
      }

      currentContent.writeln(line);
    }

    _commitSection(); // Finalize last section
    return sectionMap;
  }

  /// Converts parsed map into readable JSON string
  static String toJsonString(Map<String, dynamic> sectionMap) {
    final nested = sectionMap.map((k, v) {
      if (v is String && _isJsonLike(v)) {
        try {
          return MapEntry(k, json.decode(v));
        } catch (_) {
          return MapEntry(k, v);
        }
      }
      return MapEntry(k, v);
    });

    return const JsonEncoder.withIndent('  ').convert(nested);
  }

  static bool _isJsonLike(String input) {
    final trimmed = input.trim();
    return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
        (trimmed.startsWith('[') && trimmed.endsWith(']'));
  }

  static String _removeAllGlobalSummaryBlocks(String text) {
    // Match **OUTPUT 2: GLOBAL SUMMARY UPDATE** with optional markdown, code blocks, etc.
    // Step 1: Try to find a labeled "GLOBAL SUMMARY UPDATE" followed by a JSON block
    final summaryRegex = RegExp(
      r'(?:GLOBAL_SUMMARY_UPDATE|GLOBAL SUMMARY UPDATE)[\s\S]*?```json\s*([\s\S]+?)\s*```',
      caseSensitive: false,
    );

    return text
        .replaceAll(summaryRegex, '')
        .replaceAll("**OUTPUT 2:", "")
        .trim();
  }
}
