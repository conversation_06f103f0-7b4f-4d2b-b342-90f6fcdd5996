import 'package:flutter/material.dart';
import 'agent_manual_response_model.dart';

@immutable
class AgentNode {
  final String id;
  final String name;
  final String role;
  final String department;
  final Color departmentColor;
  final List<AgentNode> children;
  final bool isExpanded;
  final String? parentRole;
  final String? reportsTo;
  final String? organizationalLevel;
  final String? team;
  final Employee originalEmployee;

  const AgentNode({
    required this.id,
    required this.name,
    required this.role,
    required this.department,
    this.departmentColor = Colors.grey,
    this.children = const [],
    this.isExpanded = true,
    this.parentRole,
    this.reportsTo,
    this.organizationalLevel,
    this.team,
    required this.originalEmployee,
  });

  AgentNode copyWith({
    String? id,
    String? name,
    String? role,
    String? department,
    Color? departmentColor,
    List<AgentNode>? children,
    bool? isExpanded,
    String? parentRole,
    String? reportsTo,
    String? organizationalLevel,
    String? team,
    Employee? originalEmployee,
  }) {
    return AgentNode(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      department: department ?? this.department,
      departmentColor: departmentColor ?? this.departmentColor,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      parentRole: parentRole ?? this.parentRole,
      reportsTo: reportsTo ?? this.reportsTo,
      organizationalLevel: organizationalLevel ?? this.organizationalLevel,
      team: team ?? this.team,
      originalEmployee: originalEmployee ?? this.originalEmployee,
    );
  }

  factory AgentNode.fromEmployee(Employee employee, String roleId) {
    return AgentNode(
      id: roleId,
      name: employee.name ?? 'Unknown',
      role: employee.name ?? 'Unknown Role',
      department: employee.department ?? 'General',
      departmentColor: _getDepartmentColor(employee.department),
      parentRole: employee.parentRole,
      reportsTo: employee.reportsTo,
      organizationalLevel: employee.organizationalLevel,
      team: employee.team,
      originalEmployee: employee,
    );
  }

  static Color _getDepartmentColor(String? department) {
    if (department == null) return Colors.grey;
    
    switch (department.toLowerCase()) {
      case 'sales':
        return Colors.blue;
      case 'design':
      case 'ui':
      case 'ux':
        return Colors.orange;
      case 'marketing':
        return Colors.green;
      case 'engineering':
      case 'development':
        return Colors.purple;
      case 'hr':
      case 'human resources':
        return Colors.pink;
      case 'finance':
        return Colors.teal;
      case 'operations':
        return Colors.brown;
      case 'leadership':
      case 'management':
      case 'executive':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }
}

class AgentHierarchyBuilder {
  static AgentNode? buildHierarchy(Map<String, Employee> roles) {
    if (roles.isEmpty) return null;

    // If there's only one role, return it as a single node
    if (roles.length == 1) {
      var entry = roles.entries.first;
      return AgentNode.fromEmployee(entry.value, entry.key);
    }

    // Convert employees to agent nodes
    Map<String, AgentNode> nodeMap = {};
    for (var entry in roles.entries) {
      nodeMap[entry.key] = AgentNode.fromEmployee(entry.value, entry.key);
    }

    // Find root nodes and build relationships
    List<AgentNode> rootCandidates = [];
    Map<String, List<AgentNode>> childrenMap = {};

    // Initialize children map
    for (var node in nodeMap.values) {
      childrenMap[node.id] = [];
    }

    // Simplified approach - just show all roles as flat structure for now to ensure all 5 are visible
    Set<String> nodesWithParents = {};
    
    for (var node in nodeMap.values) {
      print('🌳 Processing ${node.name}: reportsTo="${node.reportsTo}", parentRole="${node.parentRole}"');
      
      // Only use reportsTo for now to avoid circular references
      if (node.reportsTo != null && node.reportsTo!.isNotEmpty && node.reportsTo!.toLowerCase() != 'null') {
        // Check if the parent exists in our node map
        var parentExists = nodeMap.values.any((parent) => 
          parent.id != node.id &&
          (parent.role.toLowerCase() == node.reportsTo!.toLowerCase() ||
           parent.name.toLowerCase() == node.reportsTo!.toLowerCase()));
           
        if (parentExists) {
          for (var potentialParent in nodeMap.values) {
            if (potentialParent.id != node.id &&
                (potentialParent.role.toLowerCase() == node.reportsTo!.toLowerCase() ||
                 potentialParent.name.toLowerCase() == node.reportsTo!.toLowerCase())) {
              childrenMap[potentialParent.id]!.add(node);
              nodesWithParents.add(node.id);
              print('🌳 ✅ ${node.name} reports to ${potentialParent.name}');
              break;
            }
          }
        } else {
          print('🌳 ${node.name} reports to ${node.reportsTo} (not in system)');
        }
      } else { 
        print('🌳 ${node.name} has no reportsTo relationship');
      }
    }
    
    // Add all nodes that don't have parents as root candidates
    print('🌳 Nodes with parents: ${nodesWithParents.toList()}');
    for (var node in nodeMap.values) {
      if (!nodesWithParents.contains(node.id)) {
        rootCandidates.add(node);
        print('🌳 Adding as root candidate: ${node.name}');
      } else {
        print('🌳 ${node.name} has a parent, not adding as root');
      }
    }

    print('🌳 Root candidates: ${rootCandidates.length}');
    for (var root in rootCandidates) {
      print('🌳 Root: ${root.name}');
    }

    // Build the tree recursively
    AgentNode buildNodeWithChildren(AgentNode node) {
      var children = childrenMap[node.id] ?? [];
      var childNodes = children.map((child) => buildNodeWithChildren(child)).toList();
      print('🌳 Building node ${node.name} with ${childNodes.length} children');
      return node.copyWith(children: childNodes);
    }

    // If we have multiple root candidates, create a virtual root
    if (rootCandidates.length > 1) {
      // Create a virtual organization root
      var virtualRoot = AgentNode(
        id: 'org_root',
        name: 'Organization',
        role: 'Organization',
        department: 'Leadership',
        departmentColor: Colors.indigo,
        children: [],
        originalEmployee: Employee(),
      );
      
      // Build all root candidates with their children
      var rootNodesWithChildren = rootCandidates.map((root) => buildNodeWithChildren(root)).toList();
      
      print('🌳 Virtual root will have ${rootNodesWithChildren.length} children');
      return virtualRoot.copyWith(children: rootNodesWithChildren);
    }

    // If we have exactly one root, build its tree
    if (rootCandidates.length == 1) {
      return buildNodeWithChildren(rootCandidates.first);
    }

    // Fallback: if no roots found, try to find the most senior role
    var seniorNode = nodeMap.values.firstWhere(
      (n) => n.role.toLowerCase().contains('ceo') ||
             n.role.toLowerCase().contains('director') ||
             n.role.toLowerCase().contains('manager') ||
             n.role.toLowerCase().contains('lead') ||
             n.organizationalLevel == '1',
      orElse: () => nodeMap.values.first,
    );

    return buildNodeWithChildren(seniorNode);
  }
}
