class MetricsInfo {
    int? status;
    String? message;
    Result? result;

    MetricsInfo({
        this.status,
        this.message,
        this.result,
    });

    factory MetricsInfo.fromJson(Map<String, dynamic> json) => MetricsInfo(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
    };

    MetricsInfo copyWith({
        int? status,
        String? message,
        Result? result,
    }) => 
        MetricsInfo(
            status: status ?? this.status,
            message: message ?? this.message,
            result: result ?? this.result,
        );
}

class Result {
    int? totalTransactions;
    int? revenue;
    double? cost;
    double? margin;
    List<BalanceSheet>? pnl;
    List<BalanceSheet>? balanceSheet;

    Result({
        this.totalTransactions,
        this.revenue,
        this.cost,
        this.margin,
        this.pnl,
        this.balanceSheet,
    });

    factory Result.fromJson(Map<String, dynamic> json) => Result(
        totalTransactions: json["totalTransactions"],
        revenue: json["revenue"],
        cost: json["cost"]?.toDouble(),
        margin: json["margin"]?.toDouble(),
        pnl: json["pnl"] == null ? [] : List<BalanceSheet>.from(json["pnl"]!.map((x) => BalanceSheet.fromJson(x))),
        balanceSheet: json["balanceSheet"] == null ? [] : List<BalanceSheet>.from(json["balanceSheet"]!.map((x) => BalanceSheet.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "totalTransactions": totalTransactions,
        "revenue": revenue,
        "cost": cost,
        "margin": margin,
        "pnl": pnl == null ? [] : List<dynamic>.from(pnl!.map((x) => x.toJson())),
        "balanceSheet": balanceSheet == null ? [] : List<dynamic>.from(balanceSheet!.map((x) => x.toJson())),
    };

    Result copyWith({
        int? totalTransactions,
        int? revenue,
        double? cost,
        double? margin,
        List<BalanceSheet>? pnl,
        List<BalanceSheet>? balanceSheet,
    }) => 
        Result(
            totalTransactions: totalTransactions ?? this.totalTransactions,
            revenue: revenue ?? this.revenue,
            cost: cost ?? this.cost,
            margin: margin ?? this.margin,
            pnl: pnl ?? this.pnl,
            balanceSheet: balanceSheet ?? this.balanceSheet,
        );
}

class BalanceSheet {
    String? name;
    double? sum;
    List<Detail>? details;

    BalanceSheet({
        this.name,
        this.sum,
        this.details,
    });

    factory BalanceSheet.fromJson(Map<String, dynamic> json) => BalanceSheet(
        name: json["name"],
        sum: json["sum"]?.toDouble(),
        details: json["details"] == null ? [] : List<Detail>.from(json["details"]!.map((x) => Detail.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "sum": sum,
        "details": details == null ? [] : List<dynamic>.from(details!.map((x) => x.toJson())),
    };

    BalanceSheet copyWith({
        String? name,
        double? sum,
        List<Detail>? details,
    }) => 
        BalanceSheet(
            name: name ?? this.name,
            sum: sum ?? this.sum,
            details: details ?? this.details,
        );
}

class Detail {
    String? name;
    double? value;

    Detail({
        this.name,
        this.value,
    });

    factory Detail.fromJson(Map<String, dynamic> json) => Detail(
        name: json["name"],
        value: json["value"]?.toDouble(),
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "value": value,
    };

    Detail copyWith({
        String? name,
        double? value,
    }) => 
        Detail(
            name: name ?? this.name,
            value: value ?? this.value,
        );
}
