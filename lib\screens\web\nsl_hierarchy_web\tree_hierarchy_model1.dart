import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/enhanced_go_lo_list_panel.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/modern_side_panel1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/unified_organizational_chart1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/nsl_hierarchy_provider.dart';

class TreeHierarchyModel1 extends StatelessWidget {
  const TreeHierarchyModel1({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => NslHierarchyProvider(),
      child: const _TreeHierarchyView(),
    );
  }
}

class _TreeHierarchyView extends StatelessWidget {
  const _TreeHierarchyView();

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Calculate side panel dimensions
        final screenWidth = MediaQuery.of(context).size.width;
        provider.updateSidePanelDimensions(screenWidth);

        return Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              // Main content row
              Row(
                children: [
                  // Unified organizational chart (combines left panel + hierarchy chart)
                  Expanded(
                    flex: provider.showSidePanel ? 3 : 1,
                    child: provider.filteredRootNode != null
                        ? UnifiedOrganizationalChart1(
                            organizationalStructure: provider.organizationalStructure,
                            rootNode: provider.filteredRootNode!,
                            selectedNodeId: provider.selectedNodeId,
                            isSidePanelOpen: provider.showSidePanel,
                            sidePanelWidth: provider.sidePanelWidth,
                            rootNodeDetails: provider.nodeDetails,
                            nodeTransactions: provider.nodeTransactions,
                            onNodeTitleTap: provider.onNodeTitleTap,
                            onNodeInfoTap: provider.onNodeInfoTap,
                          )
                        : _buildEmptyState(provider),
                  ),
                  // Side panel for NSL node details
                  if (provider.showSidePanel && provider.selectedNode != null)
                    SizedBox(
                      width: provider.sidePanelWidth,
                      child: ModernSidePanel1(
                        nodeData: provider.selectedNode!,
                        nodeDetails: provider.nodeDetails,
                        isLoadingNodeDetails: provider.isLoadingNodeDetails,
                        nodeTransactions: provider.nodeTransactions,
                        isLoadingTransactions: provider.isLoadingTransactions,
                        dateRange: provider.systemInfo?['default_time_range'],
                        onClose: provider.hideSidePanel,
                        onArrowTap: provider.onArrowTap,
                        onGoLoPanelClosed: provider.clearArrowSelection,
                        clearSelectionTrigger: provider.clearSelectionTrigger,
                        onDateRangeChanged: provider.onDateRangeChanged,
                      ),
                    ),
                  
                  // Multiple GO/LO Panels
                  ...provider.activeGoLoPanels.map((panel) => 
                    Container(
                      width: screenWidth * 0.16,
                      child: EnhancedGoLoListPanel(
                        panelId: panel.id,
                        metricType: panel.metricType,
                        onClose: () => provider.closeGoLoPanel(panel.id),
                      ),
                    ),
                  ).toList(),
                ],
              ),
              if (provider.showSidePanel && provider.selectedNode != null)
                Positioned(
                   right: provider.sidePanelWidth -
              12 + 
              (provider.activeGoLoPanels.isNotEmpty 
                  ? provider.activeGoLoPanels.length * screenWidth * 0.16 
                  : 0),
                  top: 24, // Adjust vertical position as needed
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      // Changed from InkWell
                      onTap: provider.hideSidePanel,
 
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border:
                              Border.all(color: Colors.grey.shade400, width: 1),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 12,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
 
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(NslHierarchyProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            provider.searchQuery.isEmpty
                ? 'No NSL hierarchy data available'
                : 'No nodes found for "${provider.searchQuery}"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          if (provider.searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                provider.clearSearch();
              },
              child: Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
