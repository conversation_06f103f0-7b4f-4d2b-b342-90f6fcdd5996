import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/go_lo_list_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_hierarchy_model_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_tree_side_details_new.dart';


class NslHierarchyApiServiceNew {
  static const String baseUrl = 'http://***********:9223';
  static const String modulesEndpoint = '/nsl-prescriptor/api/v1/modules';
  
  static Future<NslTreeHierarchy?> fetchModules() async {
    try {
      print('Loading NSL hierarchy data from local modules_data.json...');
      
      // Load the JSON file from assets
      final String jsonString = await rootBundle.loadString(
        'lib/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/json/modules_data.json'
      );
      
      final jsonData = json.decode(jsonString);
      final result = NslTreeHierarchy.fromJson(jsonData);
      print('Successfully loaded ${result.result?.nodes?.length ?? 0} nodes from local JSON file');
      return result;
    } catch (e) {
      print('Error loading modules from local JSON: $e');
      return null;
    }
  }

  static Future<NslTreeSidePanel?> fetchNodeDetails(String nodeId) async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint/$nodeId');
      print('Fetching node details for nodeId: $nodeId');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = NslTreeSidePanel.fromJson(jsonData);
        print('Successfully loaded node details for $nodeId - GOs: ${result.result?.goCount}, LOs: ${result.result?.loCount}');
        return result;
      } else {
        print('API Error for node $nodeId: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching node details for $nodeId: $e');
      return null;
    }
  }

  static Future<MetricsInfo?> fetchNodeTransactions(String nodeId, int fromUnixSeconds, int toUnixSeconds) async {
    try {
      final url = Uri.parse('$baseUrl$modulesEndpoint/$nodeId/transactions?from=$fromUnixSeconds&to=$toUnixSeconds');
      print('Fetching transactions for nodeId: $nodeId from $fromUnixSeconds to $toUnixSeconds');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = MetricsInfo.fromJson(jsonData);
        print('Successfully loaded transactions for $nodeId - Transactions: ${result.result?.totalTransactions}, Revenue: ${result.result?.revenue}');
        return result;
      } else {
        print('API Error for transactions $nodeId: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching transactions for $nodeId: $e');
      return null;
    }
  }

  static Future<GoLoListModel?> fetchGoLoList({String? nodeId, int pageNumber = 1, int pageSize = 50}) async {
    try {
      // Use the provided nodeId or default to 'owner'
      final targetNodeId = nodeId ?? 'owner';
      final url = Uri.parse('$baseUrl$modulesEndpoint/$targetNodeId/golo?pageNumber=$pageNumber&pageSize=$pageSize');
      print('Fetching GO/LO list for nodeId: $targetNodeId - Page: $pageNumber, Size: $pageSize');
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final result = GoLoListModel.fromJson(jsonData);
        print('Successfully loaded GO/LO list - Page: $pageNumber, GOs: ${result.result?.data?.length ?? 0}, Total: ${result.result?.totalHits}');
        return result;
      } else {
        print('API Error for GO/LO list: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error fetching GO/LO list: $e');
      return null;
    }
  }
}
