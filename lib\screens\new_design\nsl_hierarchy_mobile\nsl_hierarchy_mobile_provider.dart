import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/providers/base_provider.dart';
import 'package:nsl/services/nsl_hierarchy_api_service.dart';
import 'package:nsl/services/nsl_hierarchy_data_transformer.dart';

class NSLHierarchyMobileProvider extends BaseProvider {
  // Core hierarchy data
  List<NSLHierarchyData1> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  List<NSLNode> _allNodesFlat = []; // Cache for all nodes
  Timer? _searchDebounceTimer; // For debouncing search input
  // Add these at the top with other properties
  bool _showSearchField = false;
  // Selection and expansion state
  NSLHierarchyData1? _selectedNode;
  String? _selectedNodeId;
  final Set<String> _expandedNodes = {};

  bool get showSearchField => _showSearchField;
  // Panel state
  bool _showBottomPanel = false;

  // Search state
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // API data
  NslTreeSidePanel? _nodeDetails;
  bool _isLoadingNodeDetails = false;
  MetricsInfo? _nodeTransactions;
  bool _isLoadingTransactions = false;
  Map<String, dynamic>? _systemInfo;

  // Getters
  List<NSLHierarchyData1> get nslNodes => _nslNodes;
  NSLNode? get rootNode => _rootNode;
  NSLNode? get filteredRootNode => _filteredRootNode;
  NSLHierarchyData1? get selectedNode => _selectedNode;
  String? get selectedNodeId => _selectedNodeId;
  
  Set<String> get expandedNodes => _expandedNodes;
  bool get showBottomPanel => _showBottomPanel;

  TextEditingController get searchController => _searchController;
  String get searchQuery => _searchQuery;
  NslTreeSidePanel? get nodeDetails => _nodeDetails;
  bool get isLoadingNodeDetails => _isLoadingNodeDetails;
  MetricsInfo? get nodeTransactions => _nodeTransactions;
  bool get isLoadingTransactions => _isLoadingTransactions;
  Map<String, dynamic>? get systemInfo => _systemInfo;
  bool get hasError => error != null;

  NSLHierarchyMobileProvider() {
    _searchController.addListener(_onSearchChanged);
    loadNSLData();
  }
// Add these methods to your provider class
  void toggleSearchField() {
    _showSearchField = !_showSearchField;
    if (!_showSearchField) {
      clearSearch();
    }
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    _searchController.clear();
    _buildFilteredTree();
    notifyListeners();
  }

  void searchNodes(String query) {
    _searchQuery = query.trim().toLowerCase();

    // Cancel previous debounce timer
    _searchDebounceTimer?.cancel();

    // Only search after a short delay when user stops typing
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _buildFilteredTree();
      notifyListeners();
    });
  }
  // void searchNodes(String query) {
  //   _searchQuery = query.trim().toLowerCase();
  //   _buildFilteredTree();
  //   notifyListeners();
  // }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _searchQuery = _searchController.text.trim();
      _buildFilteredTree();
      notifyListeners();
    });
  }

  // Core data loading
  Future<void> loadNSLData() async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        // Fetch data from API
        final apiResponse = await NslHierarchyApiService.fetchModules();

        if (apiResponse == null) {
          throw Exception('Failed to fetch data from API');
        }

        // Transform API data to hierarchy structure
        _rootNode = NslHierarchyDataTransformer.transformApiDataToHierarchy(
            apiResponse);

        if (_rootNode == null) {
          throw Exception('Failed to transform API data to hierarchy');
        }

        if (_rootNode != null) {
          _nslNodes = _rootNode!.originalData.getAllNodes();
          _allNodesFlat = _flattenHierarchy(_rootNode!); //
          // Initialize with root node expanded
          _expandedNodes.add(_rootNode!.id);
        }

        // Store system info (if available in the response)
        _systemInfo = {}; // Initialize empty for now

        // Build filtered tree
        _buildFilteredTree();
      },
      context: 'NSLHierarchyMobileProvider.loadNSLData',
    );
  }

  List<NSLNode> _flattenHierarchy(NSLNode node) {
    final List<NSLNode> result = [node];
    for (var child in node.children) {
      result.addAll(_flattenHierarchy(child));
    }
    return result;
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode?.copyWith();
      _expandedNodes.clear();
      if (_rootNode != null) _expandedNodes.add(_rootNode!.id);
      return;
    }

    // 1. First find all matching nodes using the cached flat list
    final matchingNodes = _allNodesFlat
        .where((node) => _nodeMatchesQuery(node, _searchQuery))
        .toList();

    if (matchingNodes.isEmpty) {
      _filteredRootNode = null;
      return;
    }

    // 2. Build the filtered tree (this is now faster since we have matching nodes)
    _filteredRootNode =
        _buildSearchResultTreeWithMatches(_rootNode, matchingNodes);

    // 3. Calculate which nodes to expand
    _updateExpandedNodes(matchingNodes);
  }

  void _updateExpandedNodes(List<NSLNode> matchingNodes) {
    _expandedNodes.clear();
    if (_rootNode != null) _expandedNodes.add(_rootNode!.id);

    // Only expand paths to the first few matches (for performance)
    final maxMatchesToExpand = 5; // Adjust this number as needed
    for (final match in matchingNodes.take(maxMatchesToExpand)) {
      final path = _getPathToNodeIds(_rootNode, match.id);
      _expandedNodes.addAll(path);
    }
  }

  NSLNode? _buildSearchResultTreeWithMatches(
      NSLNode? node, List<NSLNode> matchingNodes) {
    if (node == null) return null;

    // Check if current node is a match
    final isMatch = matchingNodes.any((m) => m.id == node.id);

    // Process children only if needed
    List<NSLNode> newChildren = [];
    if (isMatch ||
        _isPotentialAncestor(node, matchingNodes) ||
        _expandedNodes.contains(node.id)) {
      newChildren = node.children
          .map((child) =>
              _buildSearchResultTreeWithMatches(child, matchingNodes))
          .where((child) => child != null)
          .map((child) => child as NSLNode) // Explicit cast here
          .toList();
    }

    if (isMatch || newChildren.isNotEmpty || _expandedNodes.contains(node.id)) {
      return node.copyWith(
        children: newChildren,
        // isHighlighted: isMatch,
      );
    }

    return null;
  }

  bool _isPotentialAncestor(NSLNode node, List<NSLNode> matchingNodes) {
    // Quick check if this node could be an ancestor of any match
    return matchingNodes.any(
        (m) => m.id.startsWith(node.id)); // Adjust based on your ID structure
  }

  // Include node if:
  // - It matches the search, OR
  // - It's on path to a match, OR
  // - It has matching children

  NSLNode? _deepSearch(NSLNode? node, String query) {
    if (node == null) return null;

    // Check if current node matches
    final currentMatches = _nodeMatchesQuery(node, query);

    // Search in children
    final matchedChildren = node.children
        .map((child) => _deepSearch(child, query))
        .where((child) => child != null)
        .cast<NSLNode>()
        .toList();

    // If current node matches or has matching children, include it
    if (currentMatches || matchedChildren.isNotEmpty) {
      return node.copyWith(
        children: matchedChildren,
        // Add any highlighting or search-specific properties here
      );
    }

    return null;
  }

  bool _nodeMatchesQuery(NSLNode node, String query) {
    // Check all possible fields that might contain the search term
    return node.title.toLowerCase().contains(query) ||
        node.id.toLowerCase().contains(query) ||
        (node.employeeId?.toLowerCase().contains(query) ?? false) ||
        (node.title?.toLowerCase().contains(query) ?? false);
  }

// Add this helper method to find a node by ID
  NSLNode? findNodeById(String nodeId, [NSLNode? startNode]) {
    startNode ??= _rootNode;
    if (startNode == null) return null;

    if (startNode.id == nodeId) return startNode;

    for (var child in startNode.children) {
      final found = findNodeById(nodeId, child);
      if (found != null) return found;
    }

    return null;
  }

  // void _buildFilteredTree() {
  //   if (_searchQuery.isEmpty) {
  //     _filteredRootNode = _rootNode?.copyWith(); // Reset to original tree
  //     return;
  //   }

  //   final query = _searchQuery.toLowerCase();

  //   // Find all nodes that match by title, ID, or employeeId
  //   final matchingNodes = _nslNodes.where((node) {
  //     final titleMatch = node.title.toLowerCase().contains(query);
  //     final idMatch = node.id.toLowerCase().contains(query);
  //     final employeeIdMatch =
  //         node.employeeId?.toLowerCase().contains(query) ?? false;

  //     return titleMatch || idMatch || employeeIdMatch;
  //   }).toList();

  //   if (matchingNodes.isEmpty) {
  //     _filteredRootNode = null; // No matches found
  //     return;
  //   }

  //   // Create a new tree with matching nodes and their ancestors
  //   _filteredRootNode = _buildSearchResultTree(_rootNode, matchingNodes);
  // }
  List<NSLNode> _findAllMatchingNodes(NSLNode? node, String query) {
    if (node == null) return [];

    final List<NSLNode> matches = [];

    if (_nodeMatchesQuery(node, query)) {
      matches.add(node);
    }

    for (var child in node.children) {
      matches.addAll(_findAllMatchingNodes(child, query));
    }

    return matches;
  }

  bool _isAncestorOfAnyMatch(NSLNode node, List<NSLNode> matchingNodes) {
    return matchingNodes.any((match) => _isAncestor(node, match));
  }

  bool _isAncestor(NSLNode potentialAncestor, NSLNode node) {
    NSLNode? current = node;
    while (current != null) {
      if (current.id == potentialAncestor.id) return true;
      current = _findParentNode(_rootNode, current.id);
    }
    return false;
  }

  NSLNode? _buildSearchResultTree(
      NSLNode? node, List<NSLHierarchyData1> matchingNodes) {
    if (node == null) return null;

    // Check if current node matches search by either title or ID
    final isMatch = matchingNodes.any((n) => n.id == node.id);

    // Rebuild children (filtering non-matching branches)
    final newChildren = node.children
        .map((child) => _buildSearchResultTree(child, matchingNodes))
        .where((child) => child != null)
        .cast<NSLNode>()
        .toList();

    // Include node if:
    // - It matches the search, OR
    // - It has matching descendants
    if (isMatch || newChildren.isNotEmpty) {
      return node.copyWith(
        children: newChildren,
        // isHighlighted: isMatch, // Mark matching nodes
      );
    }

    return null;
  }

  NSLNode? _findNodeInTree(NSLNode? node, String targetId) {
    if (node == null) return null;

    if (node.id == targetId) {
      return node;
    }

    for (var child in node.children) {
      final found = _findNodeInTree(child, targetId);
      if (found != null) {
        return found;
      }
    }

    return null;
  }

  // Node interaction methods
  void onNodeInfoTap(String nodeId) {
    if (_expandedNodes.contains(nodeId)) {
      // If the clicked node is already expanded, collapse it and all its descendants
      _collapseNodeAndDescendants(nodeId);
    } else {
      // Sibling accordion behavior: collapse siblings but keep parent path
      _expandNodeWithSiblingAccordion(nodeId);
    }
    notifyListeners();
  }

  void _collapseNodeAndDescendants(String nodeId) {
    // Remove the node and all its descendants from expanded nodes
    final nodeToCollapse = _findNodeInTree(_rootNode, nodeId);
    if (nodeToCollapse != null) {
      _removeNodeAndDescendants(nodeToCollapse);
    }
  }

  void _removeNodeAndDescendants(NSLNode node) {
    _expandedNodes.remove(node.id);
    for (var child in node.children) {
      _removeNodeAndDescendants(child);
    }
  }

  void _expandNodeWithSiblingAccordion(String nodeId) {
    final nodeToExpand = _findNodeInTree(_rootNode, nodeId);
    if (nodeToExpand == null) return;

    // Find the parent of the node to expand
    final parentNode = _findParentNode(_rootNode, nodeId);

    if (parentNode != null) {
      // Collapse all siblings of the node to expand
      for (var sibling in parentNode.children) {
        if (sibling.id != nodeId) {
          _removeNodeAndDescendants(sibling);
        }
      }
    }

    // Ensure the path from root to this node is expanded
    _ensurePathToNodeExpanded(nodeId);

    // Expand the clicked node
    _expandedNodes.add(nodeId);
  }

  NSLNode? _findParentNode(NSLNode? node, String targetId) {
    if (node == null) return null;

    // Check if any direct child has the target ID
    for (var child in node.children) {
      if (child.id == targetId) {
        return node;
      }
    }

    // Recursively search in children
    for (var child in node.children) {
      final parent = _findParentNode(child, targetId);
      if (parent != null) {
        return parent;
      }
    }

    return null;
  }

  void _ensurePathToNodeExpanded(String nodeId) {
    final pathToNode = _getPathToNodeIds(_rootNode, nodeId);
    for (var pathNodeId in pathToNode) {
      _expandedNodes.add(pathNodeId);
    }
  }

  List<String> _getPathToNodeIds(NSLNode? node, String targetId,
      [List<String>? currentPath]) {
    if (node == null) return [];

    currentPath ??= [];
    currentPath.add(node.id);

    if (node.id == targetId) {
      return List.from(currentPath);
    }

    for (var child in node.children) {
      final path = _getPathToNodeIds(child, targetId, List.from(currentPath));
      if (path.isNotEmpty) {
        return path;
      }
    }

    return [];
  }

  void onNodeCircleTap(NSLHierarchyData1 nodeData) {
    _selectedNode = nodeData;
    _selectedNodeId = nodeData.id;
    _showBottomPanel = true;

    // Fetch node details and transactions
    _fetchNodeDetails(nodeData.id);
    _fetchNodeTransactions(nodeData.id);

    notifyListeners();
  }

  void hideBottomPanel() {
    _showBottomPanel = false;
    _selectedNode = null;
    _selectedNodeId = null;
    notifyListeners();
  }

  // // Search methods
  // void clearSearch() {
  //   _searchController.clear();
  // }

  // API methods
  Future<void> _fetchNodeDetails(String nodeId) async {
    _isLoadingNodeDetails = true;
    notifyListeners();

    try {
      _nodeDetails = await NslHierarchyApiService.fetchNodeDetails(nodeId);
    } catch (e) {
      // Error handling is done by BaseProvider
      _nodeDetails = null;
    } finally {
      _isLoadingNodeDetails = false;
      notifyListeners();
    }
  }

  Future<void> _fetchNodeTransactions(String nodeId) async {
    _isLoadingTransactions = true;
    notifyListeners();

    try {
      // Get current time and 30 days ago for default date range
      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      final fromUnixSeconds = thirtyDaysAgo.millisecondsSinceEpoch ~/ 1000;
      final toUnixSeconds = now.millisecondsSinceEpoch ~/ 1000;

      _nodeTransactions = await NslHierarchyApiService.fetchNodeTransactions(
          nodeId, fromUnixSeconds, toUnixSeconds);
    } catch (e) {
      // Error handling is done by BaseProvider
      _nodeTransactions = null;
    } finally {
      _isLoadingTransactions = false;
      notifyListeners();
    }
  }

  // Date range change handler
  void onDateRangeChanged(Map<String, dynamic> newDateRange) {
    if (_selectedNodeId != null) {
      _fetchNodeTransactions(_selectedNodeId!);
    }
  }
}
