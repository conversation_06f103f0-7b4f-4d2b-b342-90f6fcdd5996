// lib/theme/spacing.dart

import 'package:flutter/material.dart';

import '../widgets/responsive_builder.dart';

class AppSpacing {
  // Base unit for spacing (4px)
  static const double unit = 4.0;

  // Named spacing values
  static const double xxs = unit * 1; // 4px
  static const double xs = unit * 2; // 8px
  static const double sm = unit * 3; // 12px
  static const double md = unit * 4; // 16px
  static const double lg = unit * 6; // 24px
  static const double xl = unit * 8; // 32px
  static const double xxl = unit * 12; // 48px
  static const double xxxl = unit * 16; // 64px
  static const double size14 = 14; //14
  static const double size18 = 18; //18
  static const double size34 = 34; //18
  static const double size20 = 20; //18
  static const double size22 = 22; //18
  static const double size10 = 10; //10
  static const double size6 = 6; //10
  static const double size40 = unit * 10; // 40px
  static const double width180 = 180; //180
  static const double spaceBetweenMainContentAndSidePanelForSmallScreens = 33;
  static const double spaceBetweenMainContentAndSidePanel = 77;
  static const double spaceBetweenMainStatic = 10;

  // Message-specific spacing
  static const double messageGap = sm; // 12px between messages
  static const double messageGroupGap = lg; // 24px between message groups
  static const double messagePaddingH = md; // 16px horizontal padding
  static const double messagePaddingV = sm; // 12px vertical padding

  // Thread indentation (for replies/threads)
  static const double threadIndent = xl; // 32px indentation

  static const double radius100 = 100;

  // Screen edge padding
  static const EdgeInsets screenPadding = EdgeInsets.symmetric(
    horizontal: md,
    vertical: sm,
  );

  // Responsive spacing helpers
  static double getResponsiveSpacing(
      double baseSpacing, DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing * 0.875; // 12.5% smaller on mobile
      case DeviceType.tablet:
        return baseSpacing * 0.925; // 7.5% smaller on tablet
      case DeviceType.desktop:
        return baseSpacing;
    }
  }

  static double getResponsiveHeight(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    if (screenWidth >= 3840) {
      return screenHeight / 1.9;
    } else if (screenWidth >= 2510) {
      return screenHeight / 2;
    } else if (screenWidth >= 1920) {
      return screenHeight / 1.3;
    } else if (screenWidth >= 1600) {
      return screenHeight / 1.1;
    } else if (screenWidth >= 1440) {
      return screenHeight / 1;
    } else if (screenWidth >= 1366) {
      return screenHeight / .87;
    } else if (screenWidth >= 1280) {
      return screenHeight / 0.69;
    } else {
      return screenHeight / 0.85;
    }
  }
}
