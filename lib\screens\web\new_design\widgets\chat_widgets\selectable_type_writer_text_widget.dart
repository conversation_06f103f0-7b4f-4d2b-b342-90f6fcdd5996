// class SelectableTypewriterText extends StatefulWidget {
//   final String text;
//   final Duration speed;
//   final TextStyle? style;
//   final VoidCallback? onComplete;

//   const SelectableTypewriterText({
//     required this.text,
//     this.speed = const Duration(milliseconds: 100),
//     this.style,
//     this.onComplete,
//     Key? key,
//   }) : super(key: key);

//   @override
//   _SelectableTypewriterTextState createState() =>
//       _SelectableTypewriterTextState();
// }

// class _SelectableTypewriterTextState extends State<SelectableTypewriterText> {
//   String _displayedText = '';
//   int _index = 0;
//   Timer? _timer;

//   @override
//   void initState() {
//     super.initState();
//     _startTyping();
//   }

//   void _startTyping() {
//     _timer = Timer.periodic(widget.speed, (timer) {
//       if (_index < widget.text.length) {
//         setState(() {
//           _displayedText += widget.text[_index];
//           _index++;
//         });
//         if (widget.text[_index] == '\n') {
//           widget.onComplete?.call();
//         }
//       } else {
//         _timer?.cancel();
//         widget.onComplete?.call();
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SelectableText(
//       _displayedText,
//       style: widget.style,
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'dart:async';

class SelectableTypewriterText extends StatefulWidget {
  final List<InlineSpan> textSpans;
  final Duration speed;
  final VoidCallback? onComplete;
  final VoidCallback? onNewline;

  const SelectableTypewriterText({
    required this.textSpans,
    this.speed = const Duration(milliseconds: 100),
    this.onComplete,
    this.onNewline,
    super.key,
  });

  @override
  State<SelectableTypewriterText> createState() =>
      _SelectableTypewriterTextState();
}

class _SelectableTypewriterTextState extends State<SelectableTypewriterText> {
  int _charCount = 0;
  Timer? _timer;
  late final String _fullText;
  late final List<InlineSpan> _spans;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _spans = widget.textSpans;
    _fullText = _spans.map((span) => (span as TextSpan).text ?? '').join();
    _startTyping();
  }

  void _startTyping() {
    _timer = Timer.periodic(widget.speed, (timer) {
      if (_charCount < _fullText.length) {
        final newChar = _fullText[_charCount];
        setState(() {
          _charCount++;
        });

        if (newChar == '\n') {
          widget.onNewline?.call(); // <-- Trigger scroll
        }
      }

      if (_charCount >= _fullText.length) {
        _timer?.cancel();
        setState(() => _isComplete = true);
        widget.onComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final visibleSpans = <InlineSpan>[];
    int charsLeft = _charCount;

    for (final span in _spans) {
      final spanText = (span as TextSpan).text ?? '';
      if (charsLeft <= 0) break;
      if (spanText.isNotEmpty) {
        if (spanText.length <= charsLeft) {
          visibleSpans.add(span);
          charsLeft -= spanText.length;
        } else {
          visibleSpans.add(TextSpan(
            text: spanText.substring(0, charsLeft),
            style: span.style,
          ));
          break;
        }
      }
    }

    return SelectableText.rich(
      TextSpan(children: visibleSpans),
    );
  }
}
