


// Configuration classes for scalable level management
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/models/nsl_heirarchy_model1.dart';

class MetricCell {
  final String dataPath;
  final String label;
  final String? formatter;
  
  const MetricCell(this.dataPath, this.label, [this.formatter]);
}

class MetricRow {
  final MetricCell left;
  final MetricCell right;
  
  const MetricRow(this.left, this.right);
}

class FinancialRow {
  final List<MetricCell> cells;
  
  const FinancialRow(this.cells);
}

class LevelConfig1 {
  final List<dynamic> metricsLayout; // Can contain MetricRow or FinancialRow
  final List<MetricCell> financialCards;
  
  const LevelConfig1({
    required this.metricsLayout,
    required this.financialCards,
  });
}

// Dynamic data accessor for any level
class DynamicDataAccessor {
  final NSLHierarchyData1? nodeData;
  
  DynamicDataAccessor(this.nodeData);
  
  String getMetricValue(String key, [String fallback = 'N/A']) {
    final metrics = nodeData?.metrics;
    if (metrics == null) return fallback;
    
    switch (key) {
      case 'm3_nodes': return metrics.m3Nodes?.toString() ?? fallback;
      case 'm2_nodes': return metrics.m2Nodes?.toString() ?? fallback;
      case 'm1_nodes': return metrics.m1Employees?.toString() ?? fallback;
      case 'total_gos': return metrics.totalGos?.toString() ?? fallback;
      case 'total_los': return metrics.totalLos?.toString() ?? fallback;
      case 'gos': return metrics.gos?.toString() ?? fallback;
      case 'los': return metrics.los?.toString() ?? fallback;
      case 'transactions': return metrics.transactions ?? fallback;
      case 'bet_efficiency': return metrics.betEfficiency ?? fallback;
      case 'team_efficiency': return metrics.teamEfficiency ?? fallback;
      case 'local_objectives': return metrics.localObjectives?.toString() ?? fallback;
      case 'personal_bets': return metrics.personalBets?.toString() ?? fallback;
      case 'lo_efficiency': return metrics.loEfficiency ?? fallback;
      default: return fallback;
    }
  }
  
  String getFinancialValue(String key, [String fallback = 'N/A']) {
    final financial = nodeData?.financialSummary;
    if (financial == null) return fallback;
    
    switch (key) {
      case 'revenue': return financial.revenue;
      case 'cost': return financial.cost;
      case 'margin': return financial.margin;
      default: return fallback;
    }
  }
  
  String formatValue(String value, String? formatter) {
    if (formatter == null) return value;
    
    switch (formatter) {
      case 'currency':
        // Simple currency formatting
        if (value.startsWith('\$')) return value;
        return '\$${value}';
      case 'percentage':
        if (value.endsWith('%')) return value;
        return '${value}%';
      case 'number':
        return value;
      default:
        return value;
    }
  }
}

// Configuration for all levels - easily extensible for future levels
class LevelConfigurations {
  static const Map<String, LevelConfig1> configs = {
    'M4': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('m3_nodes', 'M3 Nodes'),
          MetricCell('transactions', 'Total Transactions'),
        ),
        MetricRow(
          MetricCell('total_gos', 'Total GOs'),
          MetricCell('total_los', 'Total LOs'),
        ),
        FinancialRow([
          MetricCell('revenue', 'Revenue', 'currency'),
          MetricCell('cost', 'Cost', 'currency'),
          MetricCell('margin', 'Margin', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('revenue', 'Total Revenue', 'currency'),
        MetricCell('margin', 'Net Margin', 'percentage'),
        MetricCell('transactions', 'Total Transactions'),
        MetricCell('bet_efficiency', 'BET Efficiency', 'percentage'),
      ],
    ),
    
    'M3': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('m2_nodes', 'M2 Nodes'),
          MetricCell('transactions', 'Total Transactions'),
        ),
        MetricRow(
          MetricCell('gos', 'GOs'),
          MetricCell('los', 'LOs'),
        ),
        FinancialRow([
          MetricCell('revenue', 'Revenue', 'currency'),
          MetricCell('cost', 'Cost', 'currency'),
          MetricCell('margin', 'Margin', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('revenue', 'Total Revenue', 'currency'),
        MetricCell('margin', 'Net Margin', 'percentage'),
        MetricCell('transactions', 'Total Transactions'),
        MetricCell('bet_efficiency', 'BET Efficiency', 'percentage'),
      ],
    ),
    
    'M2': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('m1_nodes', 'M1 Employees'),
          MetricCell('transactions', 'Total Transactions',),
        ),
        MetricRow(
          MetricCell('gos', 'GOs'),
          MetricCell('los', 'LOs'),
        ),
        FinancialRow([
          MetricCell('revenue', 'Revenue', 'currency'),
          MetricCell('cost', 'Cost', 'currency'),
          MetricCell('margin', 'Margin', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('revenue', 'Team Revenue', 'currency'),
        MetricCell('margin', 'Team Margin', 'percentage'),
        MetricCell('team_efficiency', 'Team Efficiency', 'percentage'),
        MetricCell('bet_efficiency', 'BET Efficiency', 'percentage'),
      ],
    ),
    
    'M1': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('local_objectives', 'Local Objectives'),
          MetricCell('personal_bets', 'Personal BETs'),
        ),
        MetricRow(
          MetricCell('lo_efficiency', 'LO Efficiency', 'percentage'),
          MetricCell('revenue', 'Annual Salary', 'currency'),
        ),
        FinancialRow([
          MetricCell('revenue', 'Value Output', 'currency'),
          MetricCell('cost', 'Efficiency', 'percentage'),
          MetricCell('margin', 'Performance', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('revenue', 'Annual Salary', 'currency'),
        MetricCell('cost', 'Value Output', 'currency'),
        MetricCell('lo_efficiency', 'Individual Efficiency', 'percentage'),
        MetricCell('margin', 'Performance Score', 'percentage'),
      ],
    ),
    
    // Future levels - easily extensible without code changes
    'M5': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('m4_nodes', 'M4 Nodes'),
          MetricCell('strategic_initiatives', 'Strategic Initiatives'),
        ),
        MetricRow(
          MetricCell('board_metrics', 'Board Metrics'),
          MetricCell('stakeholder_value', 'Stakeholder Value'),
        ),
        FinancialRow([
          MetricCell('total_revenue', 'Total Revenue', 'currency'),
          MetricCell('total_cost', 'Total Cost', 'currency'),
          MetricCell('roi', 'ROI', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('total_revenue', 'Enterprise Revenue', 'currency'),
        MetricCell('roi', 'Return on Investment', 'percentage'),
        MetricCell('market_share', 'Market Share', 'percentage'),
        MetricCell('growth_rate', 'Growth Rate', 'percentage'),
      ],
    ),
    
    'M6': LevelConfig1(
      metricsLayout: [
        MetricRow(
          MetricCell('m5_entities', 'M5 Entities'),
          MetricCell('global_initiatives', 'Global Initiatives'),
        ),
        MetricRow(
          MetricCell('ecosystem_metrics', 'Ecosystem Metrics'),
          MetricCell('sustainability_index', 'Sustainability Index'),
        ),
        FinancialRow([
          MetricCell('global_revenue', 'Global Revenue', 'currency'),
          MetricCell('impact_investment', 'Impact Investment', 'currency'),
          MetricCell('esg_score', 'ESG Score', 'percentage'),
        ]),
      ],
      financialCards: [
        MetricCell('global_revenue', 'Global Revenue', 'currency'),
        MetricCell('esg_score', 'ESG Performance', 'percentage'),
        MetricCell('innovation_index', 'Innovation Index', 'percentage'),
        MetricCell('market_leadership', 'Market Leadership', 'percentage'),
      ],
    ),
    
    // M7, M8, M9... can be added infinitely
  };
  
  static LevelConfig1? getConfig(String level) {
    return configs[level];
  }
  
  static LevelConfig1 getConfigOrDefault(String level) {
    return configs[level] ?? configs['M4']!;
  }
}
