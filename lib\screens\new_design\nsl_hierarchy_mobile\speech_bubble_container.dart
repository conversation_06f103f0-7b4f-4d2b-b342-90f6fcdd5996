import 'package:flutter/material.dart';

enum TailDirection { left, right, top, bottom }

class SpeechBubbleContainer extends StatelessWidget {
  final Widget child;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final bool showTail;
  final TailDirection tailDirection;
  final double tailSize;
  final double borderRadius;
  final double tailOffset; // Offset from center (negative = up, positive = down)

  const SpeechBubbleContainer({
    Key? key,
    required this.child,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.showTail = true,
    this.tailDirection = TailDirection.left,
    this.tailSize = 10.0,
    this.borderRadius = 8.0,
    this.tailOffset = 0.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: SpeechBubblePainter(
        backgroundColor: backgroundColor,
        borderColor: borderColor,
        borderWidth: borderWidth,
        showTail: showTail,
        tailDirection: tailDirection,
        tailSize: tailSize,
        borderRadius: borderRadius,
        tailOffset: tailOffset,
      ),
      child: Container(
        margin: EdgeInsets.only(
          left: tailDirection == TailDirection.left ? tailSize : 0,
          right: tailDirection == TailDirection.right ? tailSize : 0,
          top: tailDirection == TailDirection.top ? tailSize : 0,
          bottom: tailDirection == TailDirection.bottom ? tailSize : 0,
        ),
        child: child,
      ),
    );
  }
}

class SpeechBubblePainter extends CustomPainter {
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final bool showTail;
  final TailDirection tailDirection;
  final double tailSize;
  final double borderRadius;
  final double tailOffset;

  SpeechBubblePainter({
    required this.backgroundColor,
    required this.borderColor,
    required this.borderWidth,
    required this.showTail,
    required this.tailDirection,
    required this.tailSize,
    required this.borderRadius,
    required this.tailOffset,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..strokeJoin = StrokeJoin.round;

    // Calculate the main rectangle bounds
    double rectLeft = tailDirection == TailDirection.left ? tailSize : 0;
    double rectTop = tailDirection == TailDirection.top ? tailSize : 0;
    double rectRight = size.width - (tailDirection == TailDirection.right ? tailSize : 0);
    double rectBottom = size.height - (tailDirection == TailDirection.bottom ? tailSize : 0);

    final path = Path();

    if (showTail) {
      // Create path with integrated tail
      switch (tailDirection) {
        case TailDirection.left:
          double tailY = rectTop + (rectBottom - rectTop) / 2 + tailOffset;
          tailY = tailY.clamp(rectTop + tailSize / 2, rectBottom - tailSize / 2);
          
          // Start from top-left corner
          path.moveTo(rectLeft + borderRadius, rectTop);
          // Top edge
          path.lineTo(rectRight - borderRadius, rectTop);
          // Top-right corner
          path.quadraticBezierTo(rectRight, rectTop, rectRight, rectTop + borderRadius);
          // Right edge
          path.lineTo(rectRight, rectBottom - borderRadius);
          // Bottom-right corner
          path.quadraticBezierTo(rectRight, rectBottom, rectRight - borderRadius, rectBottom);
          // Bottom edge
          path.lineTo(rectLeft + borderRadius, rectBottom);
          // Bottom-left corner
          path.quadraticBezierTo(rectLeft, rectBottom, rectLeft, rectBottom - borderRadius);
          // Left edge to tail end
          path.lineTo(rectLeft, tailY + tailSize / 2);
          // Curved tail - bottom curve
          path.quadraticBezierTo(rectLeft - tailSize * 0.25, tailY + tailSize * 0.25, 0, tailY);
          // Curved tail - top curve
          path.quadraticBezierTo(rectLeft - tailSize * 0.25, tailY - tailSize * 0.25, rectLeft, tailY - tailSize / 2);
          // Continue left edge to top-left corner
          path.lineTo(rectLeft, rectTop + borderRadius);
          // Top-left corner
          path.quadraticBezierTo(rectLeft, rectTop, rectLeft + borderRadius, rectTop);
          break;
          
        case TailDirection.right:
          double tailY = rectTop + (rectBottom - rectTop) / 2 + tailOffset;
          tailY = tailY.clamp(rectTop + tailSize / 2, rectBottom - tailSize / 2);
          
          // Start from top-left corner
          path.moveTo(rectLeft + borderRadius, rectTop);
          // Top edge
          path.lineTo(rectRight - borderRadius, rectTop);
          // Top-right corner
          path.quadraticBezierTo(rectRight, rectTop, rectRight, rectTop + borderRadius);
          // Right edge to tail start
          path.lineTo(rectRight, tailY - tailSize / 2);
          // Curved tail - top curve
          path.quadraticBezierTo(rectRight + tailSize * 0.25, tailY - tailSize * 0.25, size.width, tailY);
          // Curved tail - bottom curve
          path.quadraticBezierTo(rectRight + tailSize * 0.25, tailY + tailSize * 0.25, rectRight, tailY + tailSize / 2);
          // Continue right edge
          path.lineTo(rectRight, rectBottom - borderRadius);
          // Bottom-right corner
          path.quadraticBezierTo(rectRight, rectBottom, rectRight - borderRadius, rectBottom);
          // Bottom edge
          path.lineTo(rectLeft + borderRadius, rectBottom);
          // Bottom-left corner
          path.quadraticBezierTo(rectLeft, rectBottom, rectLeft, rectBottom - borderRadius);
          // Left edge
          path.lineTo(rectLeft, rectTop + borderRadius);
          // Top-left corner
          path.quadraticBezierTo(rectLeft, rectTop, rectLeft + borderRadius, rectTop);
          break;
          
        case TailDirection.top:
          double tailX = rectLeft + (rectRight - rectLeft) / 2 + tailOffset;
          tailX = tailX.clamp(rectLeft + tailSize / 2, rectRight - tailSize / 2);
          
          // Start from tail point
          path.moveTo(tailX, 0);
          // Curved tail - right curve
          path.quadraticBezierTo(tailX + tailSize * 0.25, rectTop - tailSize * 0.25, tailX + tailSize / 2, rectTop);
          // Top edge
          path.lineTo(rectRight - borderRadius, rectTop);
          // Top-right corner
          path.quadraticBezierTo(rectRight, rectTop, rectRight, rectTop + borderRadius);
          // Right edge
          path.lineTo(rectRight, rectBottom - borderRadius);
          // Bottom-right corner
          path.quadraticBezierTo(rectRight, rectBottom, rectRight - borderRadius, rectBottom);
          // Bottom edge
          path.lineTo(rectLeft + borderRadius, rectBottom);
          // Bottom-left corner
          path.quadraticBezierTo(rectLeft, rectBottom, rectLeft, rectBottom - borderRadius);
          // Left edge
          path.lineTo(rectLeft, rectTop + borderRadius);
          // Top-left corner
          path.quadraticBezierTo(rectLeft, rectTop, rectLeft + borderRadius, rectTop);
          // Top edge to tail
          path.lineTo(tailX - tailSize / 2, rectTop);
          // Curved tail - left curve
          path.quadraticBezierTo(tailX - tailSize * 0.25, rectTop - tailSize * 0.25, tailX, 0);
          break;
          
        case TailDirection.bottom:
          double tailX = rectLeft + (rectRight - rectLeft) / 2 + tailOffset;
          tailX = tailX.clamp(rectLeft + tailSize / 2, rectRight - tailSize / 2);
          
          // Start from top-left corner
          path.moveTo(rectLeft + borderRadius, rectTop);
          // Top edge
          path.lineTo(rectRight - borderRadius, rectTop);
          // Top-right corner
          path.quadraticBezierTo(rectRight, rectTop, rectRight, rectTop + borderRadius);
          // Right edge
          path.lineTo(rectRight, rectBottom - borderRadius);
          // Bottom-right corner
          path.quadraticBezierTo(rectRight, rectBottom, rectRight - borderRadius, rectBottom);
          // Bottom edge to tail start
          path.lineTo(tailX + tailSize / 2, rectBottom);
          // Curved tail - right curve
          path.quadraticBezierTo(tailX + tailSize * 0.25, rectBottom + tailSize * 0.25, tailX, size.height);
          // Curved tail - left curve
          path.quadraticBezierTo(tailX - tailSize * 0.25, rectBottom + tailSize * 0.25, tailX - tailSize / 2, rectBottom);
          // Continue bottom edge
          path.lineTo(rectLeft + borderRadius, rectBottom);
          // Bottom-left corner
          path.quadraticBezierTo(rectLeft, rectBottom, rectLeft, rectBottom - borderRadius);
          // Left edge
          path.lineTo(rectLeft, rectTop + borderRadius);
          // Top-left corner
          path.quadraticBezierTo(rectLeft, rectTop, rectLeft + borderRadius, rectTop);
          break;
      }
    } else {
      // Simple rounded rectangle without tail
      final rect = RRect.fromRectAndRadius(
        Rect.fromLTRB(rectLeft, rectTop, rectRight, rectBottom),
        Radius.circular(borderRadius),
      );
      path.addRRect(rect);
    }

    // Draw the shape
    canvas.drawPath(path, paint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is SpeechBubblePainter &&
        (oldDelegate.backgroundColor != backgroundColor ||
            oldDelegate.borderColor != borderColor ||
            oldDelegate.borderWidth != borderWidth ||
            oldDelegate.showTail != showTail ||
            oldDelegate.tailDirection != tailDirection ||
            oldDelegate.tailSize != tailSize ||
            oldDelegate.borderRadius != borderRadius ||
            oldDelegate.tailOffset != tailOffset);
  }
}